"""
Group Buy API endpoints
"""
import logging
from flask import Blueprint, jsonify, request, g
from marshmallow import Schema, fields, validate
from middleware.auth import auth_required, admin_required, optional_auth
from middleware.validation import validate_json, validate_query, validate_path, PaginationSchema
from middleware.rate_limiting import rate_limit, strict_rate_limit, lenient_rate_limit
from services.groupbuy_service import GroupBuyService
from utils.formatters import success_response, error_response

logger = logging.getLogger(__name__)

# Create Blueprint
groupbuy_bp = Blueprint('groupbuys', __name__)

# Initialize service
groupbuy_service = GroupBuyService()

# Validation schemas
class GroupBuyFilterSchema(PaginationSchema):
    """Schema for group buy filtering"""
    status = fields.String(missing='Active', validate=validate.OneOf(['Active', 'Successful', 'Failed', 'Cancelled']))
    productVariantId = fields.String(missing=None)
    category = fields.String(missing=None)
    expiring_soon = fields.Boolean(missing=False)

class GroupBuyCreateSchema(Schema):
    """Schema for creating group buys"""
    productVariantId = fields.String(required=True, validate=validate.Length(min=1))
    targetQuantity = fields.Integer(required=True, validate=validate.Range(min=1))
    groupPrice = fields.Float(required=True, validate=validate.Range(min=0))
    expiresAt = fields.Integer(required=True, validate=validate.Range(min=1))  # Unix timestamp
    description = fields.String(missing='', validate=validate.Length(max=1000))

class GroupBuyUpdateSchema(Schema):
    """Schema for updating group buys"""
    targetQuantity = fields.Integer(validate=validate.Range(min=1))
    groupPrice = fields.Float(validate=validate.Range(min=0))
    expiresAt = fields.Integer(validate=validate.Range(min=1))
    description = fields.String(validate=validate.Length(max=1000))

class GroupBuyStatusUpdateSchema(Schema):
    """Schema for updating group buy status"""
    status = fields.String(required=True, validate=validate.OneOf(['Active', 'Successful', 'Failed', 'Cancelled']))
    notes = fields.String(missing='', validate=validate.Length(max=500))

class GroupBuyJoinSchema(Schema):
    """Schema for joining group buy"""
    quantity = fields.Integer(missing=1, validate=validate.Range(min=1, max=100))

# Public endpoints
@groupbuy_bp.route('/groupbuys', methods=['GET'])
@lenient_rate_limit()
@optional_auth
@validate_query(GroupBuyFilterSchema)
def get_groupbuys(query_params):
    """Get group buys with filtering and pagination"""
    try:
        result = groupbuy_service.get_groupbuys(
            status=query_params.get('status'),
            product_variant_id=query_params.get('productVariantId'),
            category=query_params.get('category'),
            expiring_soon=query_params.get('expiring_soon'),
            page=query_params.get('page'),
            per_page=query_params.get('per_page'),
            sort_by=query_params.get('sort_by'),
            sort_order=query_params.get('sort_order')
        )

        return success_response(
            data=result,
            message="Group buys retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting group buys: {str(e)}")
        return error_response(
            message="Failed to retrieve group buys",
            code="GROUPBUYS_FETCH_ERROR"
        )

@groupbuy_bp.route('/groupbuys/<groupbuy_id>', methods=['GET'])
@lenient_rate_limit()
@optional_auth
@validate_path(groupbuy_id=fields.String(required=True))
def get_groupbuy_details(path_params):
    """Get group buy details"""
    try:
        groupbuy_id = path_params['groupbuy_id']
        result = groupbuy_service.get_groupbuy_details(groupbuy_id)

        if not result:
            return error_response(
                message="Group buy not found",
                code="GROUPBUY_NOT_FOUND",
                status_code=404
            )

        return success_response(
            data=result,
            message="Group buy details retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting group buy {path_params.get('groupbuy_id')}: {str(e)}")
        return error_response(
            message="Failed to retrieve group buy details",
            code="GROUPBUY_FETCH_ERROR"
        )

@groupbuy_bp.route('/groupbuys/trending', methods=['GET'])
@lenient_rate_limit()
@optional_auth
@validate_query(PaginationSchema)
def get_trending_groupbuys(query_params):
    """Get trending group buys"""
    try:
        result = groupbuy_service.get_trending_groupbuys(
            page=query_params.get('page'),
            per_page=query_params.get('per_page')
        )

        return success_response(
            data=result,
            message="Trending group buys retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting trending group buys: {str(e)}")
        return error_response(
            message="Failed to retrieve trending group buys",
            code="TRENDING_GROUPBUYS_FETCH_ERROR"
        )

@groupbuy_bp.route('/groupbuys/<groupbuy_id>/join', methods=['POST'])
@rate_limit()
@auth_required
@validate_path(groupbuy_id=fields.String(required=True))
@validate_json(GroupBuyJoinSchema)
def join_groupbuy(path_params, validated_data):
    """Join group buy"""
    try:
        groupbuy_id = path_params['groupbuy_id']
        user_id = g.user_id
        quantity = validated_data.get('quantity', 1)

        result = groupbuy_service.join_groupbuy(groupbuy_id, user_id, quantity)

        if not result:
            return error_response(
                message="Group buy not found or not available for joining",
                code="GROUPBUY_JOIN_ERROR",
                status_code=404
            )

        return success_response(
            data=result,
            message="Successfully joined group buy"
        )

    except ValueError as e:
        logger.warning(f"Group buy join validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="GROUPBUY_JOIN_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error joining group buy {path_params.get('groupbuy_id')}: {str(e)}")
        return error_response(
            message="Failed to join group buy",
            code="GROUPBUY_JOIN_ERROR"
        )

@groupbuy_bp.route('/groupbuys/<groupbuy_id>/leave', methods=['POST'])
@rate_limit()
@auth_required
@validate_path(groupbuy_id=fields.String(required=True))
@validate_json(GroupBuyJoinSchema)
def leave_groupbuy(path_params, validated_data):
    """Leave group buy"""
    try:
        groupbuy_id = path_params['groupbuy_id']
        user_id = g.user_id
        quantity = validated_data.get('quantity', 1)

        result = groupbuy_service.leave_groupbuy(groupbuy_id, user_id, quantity)

        if not result:
            return error_response(
                message="Group buy not found or user not participating",
                code="GROUPBUY_LEAVE_ERROR",
                status_code=404
            )

        return success_response(
            data=result,
            message="Successfully left group buy"
        )

    except ValueError as e:
        logger.warning(f"Group buy leave validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="GROUPBUY_LEAVE_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error leaving group buy {path_params.get('groupbuy_id')}: {str(e)}")
        return error_response(
            message="Failed to leave group buy",
            code="GROUPBUY_LEAVE_ERROR"
        )

# Admin endpoints
@groupbuy_bp.route('/admin/groupbuys', methods=['POST'])
@rate_limit()
@auth_required
@admin_required
@validate_json(GroupBuyCreateSchema)
def create_groupbuy(validated_data):
    """Create new group buy (Admin only)"""
    try:
        admin_id = g.user_id
        result = groupbuy_service.create_groupbuy(validated_data, admin_id)

        return success_response(
            data=result,
            message="Group buy created successfully",
            status_code=201
        )

    except ValueError as e:
        logger.warning(f"Group buy creation validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="GROUPBUY_CREATE_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error creating group buy: {str(e)}")
        return error_response(
            message="Failed to create group buy",
            code="GROUPBUY_CREATE_ERROR"
        )

@groupbuy_bp.route('/admin/groupbuys/<groupbuy_id>', methods=['PUT'])
@rate_limit()
@auth_required
@admin_required
@validate_path(groupbuy_id=fields.String(required=True))
@validate_json(GroupBuyUpdateSchema)
def update_groupbuy(path_params, validated_data):
    """Update group buy (Admin only)"""
    try:
        groupbuy_id = path_params['groupbuy_id']
        admin_id = g.user_id

        result = groupbuy_service.update_groupbuy(groupbuy_id, validated_data, admin_id)

        if not result:
            return error_response(
                message="Group buy not found",
                code="GROUPBUY_NOT_FOUND",
                status_code=404
            )

        return success_response(
            data=result,
            message="Group buy updated successfully"
        )

    except ValueError as e:
        logger.warning(f"Group buy update validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="GROUPBUY_UPDATE_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error updating group buy {path_params.get('groupbuy_id')}: {str(e)}")
        return error_response(
            message="Failed to update group buy",
            code="GROUPBUY_UPDATE_ERROR"
        )

@groupbuy_bp.route('/admin/groupbuys/<groupbuy_id>/status', methods=['PUT'])
@strict_rate_limit()
@auth_required
@admin_required
@validate_path(groupbuy_id=fields.String(required=True))
@validate_json(GroupBuyStatusUpdateSchema)
def update_groupbuy_status(path_params, validated_data):
    """Update group buy status (Admin only)"""
    try:
        groupbuy_id = path_params['groupbuy_id']
        admin_id = g.user_id

        result = groupbuy_service.update_groupbuy_status(
            groupbuy_id=groupbuy_id,
            new_status=validated_data['status'],
            admin_id=admin_id,
            notes=validated_data.get('notes', '')
        )

        if not result:
            return error_response(
                message="Group buy not found",
                code="GROUPBUY_NOT_FOUND",
                status_code=404
            )

        return success_response(
            data=result,
            message="Group buy status updated successfully"
        )

    except ValueError as e:
        logger.warning(f"Group buy status update validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="GROUPBUY_STATUS_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error updating group buy status for {path_params.get('groupbuy_id')}: {str(e)}")
        return error_response(
            message="Failed to update group buy status",
            code="GROUPBUY_STATUS_UPDATE_ERROR"
        )

@groupbuy_bp.route('/admin/groupbuys/<groupbuy_id>', methods=['DELETE'])
@strict_rate_limit()
@auth_required
@admin_required
@validate_path(groupbuy_id=fields.String(required=True))
def delete_groupbuy(path_params):
    """Delete/deactivate group buy (Admin only)"""
    try:
        groupbuy_id = path_params['groupbuy_id']
        admin_id = g.user_id

        result = groupbuy_service.delete_groupbuy(groupbuy_id, admin_id)

        if not result:
            return error_response(
                message="Group buy not found",
                code="GROUPBUY_NOT_FOUND",
                status_code=404
            )

        return success_response(
            message="Group buy deleted successfully"
        )

    except ValueError as e:
        logger.warning(f"Group buy deletion validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="GROUPBUY_DELETE_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error deleting group buy {path_params.get('groupbuy_id')}: {str(e)}")
        return error_response(
            message="Failed to delete group buy",
            code="GROUPBUY_DELETE_ERROR"
        )
