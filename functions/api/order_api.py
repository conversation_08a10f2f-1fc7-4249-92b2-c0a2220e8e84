"""
Order API endpoints
"""
import logging
from flask import Blueprint, jsonify, request, g
from marshmallow import Schema, fields, validate
from middleware.auth import auth_required, admin_required, optional_auth
from middleware.validation import validate_json, validate_query, validate_path, PaginationSchema
from middleware.rate_limiting import rate_limit, strict_rate_limit
from services.order_service import OrderService
from utils.formatters import success_response, error_response

logger = logging.getLogger(__name__)

# Create Blueprint
order_bp = Blueprint('orders', __name__)

# Initialize service
order_service = OrderService()

# Validation schemas
class OrderFilterSchema(PaginationSchema):
    """Schema for order filtering"""
    status = fields.String(missing=None, validate=validate.OneOf([
        'Pending Payment', 'Processing', 'Action Required', 
        'Partially Shipped', 'Shipped', 'Delivered', 'Cancelled'
    ]))
    date_from = fields.Integer(missing=None)  # Unix timestamp
    date_to = fields.Integer(missing=None)    # Unix timestamp

class OrderItemSchema(Schema):
    """Schema for order items"""
    productVariantId = fields.String(required=True)
    quantity = fields.Integer(required=True, validate=validate.Range(min=1))
    pricePerUnit = fields.Float(required=True, validate=validate.Range(min=0))
    isGroupBuy = fields.Boolean(missing=False)
    groupBuyId = fields.String(missing=None)

class ShippingAddressSchema(Schema):
    """Schema for shipping address"""
    addressLine1 = fields.String(required=True, validate=validate.Length(min=1, max=200))
    addressLine2 = fields.String(missing='', validate=validate.Length(max=200))
    city = fields.String(required=True, validate=validate.Length(min=1, max=100))
    stateOrProvince = fields.String(required=True, validate=validate.Length(min=1, max=100))
    postalCode = fields.String(required=True, validate=validate.Length(min=1, max=20))
    country = fields.String(required=True, validate=validate.Length(min=1, max=100))

class OrderCreateSchema(Schema):
    """Schema for creating orders"""
    items = fields.List(fields.Nested(OrderItemSchema), required=True, validate=validate.Length(min=1))
    shippingAddress = fields.Nested(ShippingAddressSchema, required=True)
    currency = fields.String(missing='USD', validate=validate.OneOf(['USD', 'EUR', 'GBP']))
    notes = fields.String(missing='', validate=validate.Length(max=500))

class OrderStatusUpdateSchema(Schema):
    """Schema for updating order status"""
    status = fields.String(required=True, validate=validate.OneOf([
        'Pending Payment', 'Processing', 'Action Required', 
        'Partially Shipped', 'Shipped', 'Delivered', 'Cancelled'
    ]))
    notes = fields.String(missing='', validate=validate.Length(max=500))

class OrderIssueCreateSchema(Schema):
    """Schema for creating order issues"""
    orderItemId = fields.String(required=True)
    issueType = fields.String(required=True, validate=validate.OneOf([
        'OutOfStock', 'PriceIncrease', 'SupplierQualityIssue', 'ShippingDelay'
    ]))
    details = fields.String(required=True, validate=validate.Length(min=1, max=1000))
    proposedSolution = fields.String(missing='', validate=validate.Length(max=1000))

class OrderIssueResponseSchema(Schema):
    """Schema for responding to order issues"""
    response = fields.String(required=True, validate=validate.OneOf([
        'AcceptPriceIncrease', 'ChooseAlternative', 'CancelItem', 'ContactSupport'
    ]))
    notes = fields.String(missing='', validate=validate.Length(max=500))

# User endpoints
@order_bp.route('/orders', methods=['POST'])
@rate_limit()
@auth_required
@validate_json(OrderCreateSchema)
def create_order(validated_data):
    """Create new order"""
    try:
        user_id = g.user_id
        result = order_service.create_order(user_id, validated_data)
        
        return success_response(
            data=result,
            message="Order created successfully",
            status_code=201
        )
        
    except ValueError as e:
        logger.warning(f"Order creation validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="ORDER_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error creating order for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to create order",
            code="ORDER_CREATE_ERROR"
        )

@order_bp.route('/orders', methods=['GET'])
@rate_limit()
@auth_required
@validate_query(OrderFilterSchema)
def get_user_orders(query_params):
    """Get user's orders"""
    try:
        user_id = g.user_id
        result = order_service.get_user_orders(
            user_id=user_id,
            status=query_params.get('status'),
            date_from=query_params.get('date_from'),
            date_to=query_params.get('date_to'),
            page=query_params.get('page'),
            per_page=query_params.get('per_page'),
            sort_by=query_params.get('sort_by'),
            sort_order=query_params.get('sort_order')
        )
        
        return success_response(
            data=result,
            message="Orders retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting orders for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to retrieve orders",
            code="ORDERS_FETCH_ERROR"
        )

@order_bp.route('/orders/<order_id>', methods=['GET'])
@rate_limit()
@auth_required
@validate_path(order_id=fields.String(required=True))
def get_order_details(path_params):
    """Get order details"""
    try:
        order_id = path_params['order_id']
        user_id = g.user_id
        
        result = order_service.get_order_details(order_id, user_id)
        
        if not result:
            return error_response(
                message="Order not found",
                code="ORDER_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            data=result,
            message="Order details retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting order {path_params.get('order_id')} for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to retrieve order details",
            code="ORDER_FETCH_ERROR"
        )

@order_bp.route('/orders/<order_id>/issues/<issue_id>/respond', methods=['POST'])
@strict_rate_limit()
@auth_required
@validate_path(order_id=fields.String(required=True), issue_id=fields.String(required=True))
@validate_json(OrderIssueResponseSchema)
def respond_to_order_issue(path_params, validated_data):
    """Respond to order issue"""
    try:
        order_id = path_params['order_id']
        issue_id = path_params['issue_id']
        user_id = g.user_id
        
        result = order_service.respond_to_order_issue(
            issue_id=issue_id,
            order_id=order_id,
            user_id=user_id,
            response=validated_data['response'],
            notes=validated_data.get('notes', '')
        )
        
        if not result:
            return error_response(
                message="Order issue not found or access denied",
                code="ORDER_ISSUE_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            data=result,
            message="Order issue response recorded successfully"
        )
        
    except ValueError as e:
        logger.warning(f"Order issue response validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="ORDER_ISSUE_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error responding to order issue {path_params.get('issue_id')}: {str(e)}")
        return error_response(
            message="Failed to respond to order issue",
            code="ORDER_ISSUE_RESPONSE_ERROR"
        )

@order_bp.route('/orders/action-required', methods=['GET'])
@rate_limit()
@auth_required
def get_action_required_orders():
    """Get orders requiring user action"""
    try:
        user_id = g.user_id
        result = order_service.get_action_required_orders(user_id)
        
        return success_response(
            data=result,
            message="Action required orders retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting action required orders for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to retrieve action required orders",
            code="ACTION_REQUIRED_ORDERS_FETCH_ERROR"
        )

# Payment endpoints (manual validation)
@order_bp.route('/orders/<order_id>/payment-info', methods=['GET'])
@rate_limit()
@auth_required
@validate_path(order_id=fields.String(required=True))
def get_payment_info(path_params):
    """Get payment information for manual payment"""
    try:
        order_id = path_params['order_id']
        user_id = g.user_id
        
        result = order_service.get_payment_info(order_id, user_id)
        
        if not result:
            return error_response(
                message="Order not found or payment not required",
                code="ORDER_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            data=result,
            message="Payment information retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting payment info for order {path_params.get('order_id')}: {str(e)}")
        return error_response(
            message="Failed to retrieve payment information",
            code="PAYMENT_INFO_FETCH_ERROR"
        )

@order_bp.route('/orders/<order_id>/payment-confirmation', methods=['POST'])
@strict_rate_limit()
@auth_required
@validate_path(order_id=fields.String(required=True))
def submit_payment_confirmation(path_params):
    """Submit payment confirmation (user indicates they've made payment)"""
    try:
        order_id = path_params['order_id']
        user_id = g.user_id
        
        # Get optional payment reference from request
        payment_reference = None
        if request.is_json:
            json_data = request.get_json()
            payment_reference = json_data.get('paymentReference', '')
        
        result = order_service.submit_payment_confirmation(order_id, user_id, payment_reference)
        
        if not result:
            return error_response(
                message="Order not found or payment confirmation not allowed",
                code="ORDER_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            data=result,
            message="Payment confirmation submitted successfully"
        )
        
    except ValueError as e:
        logger.warning(f"Payment confirmation validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="PAYMENT_CONFIRMATION_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error submitting payment confirmation for order {path_params.get('order_id')}: {str(e)}")
        return error_response(
            message="Failed to submit payment confirmation",
            code="PAYMENT_CONFIRMATION_ERROR"
        )

# Admin endpoints
@order_bp.route('/admin/orders', methods=['GET'])
@rate_limit()
@auth_required
@admin_required
@validate_query(OrderFilterSchema)
def get_all_orders(query_params):
    """Get all orders (Admin only)"""
    try:
        result = order_service.get_all_orders(
            status=query_params.get('status'),
            date_from=query_params.get('date_from'),
            date_to=query_params.get('date_to'),
            page=query_params.get('page'),
            per_page=query_params.get('per_page'),
            sort_by=query_params.get('sort_by'),
            sort_order=query_params.get('sort_order')
        )

        return success_response(
            data=result,
            message="All orders retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting all orders: {str(e)}")
        return error_response(
            message="Failed to retrieve orders",
            code="ADMIN_ORDERS_FETCH_ERROR"
        )

@order_bp.route('/admin/orders/<order_id>/status', methods=['PUT'])
@strict_rate_limit()
@auth_required
@admin_required
@validate_path(order_id=fields.String(required=True))
@validate_json(OrderStatusUpdateSchema)
def update_order_status(path_params, validated_data):
    """Update order status (Admin only)"""
    try:
        order_id = path_params['order_id']
        admin_id = g.user_id

        result = order_service.update_order_status(
            order_id=order_id,
            new_status=validated_data['status'],
            admin_id=admin_id,
            notes=validated_data.get('notes', '')
        )

        if not result:
            return error_response(
                message="Order not found",
                code="ORDER_NOT_FOUND",
                status_code=404
            )

        return success_response(
            data=result,
            message="Order status updated successfully"
        )

    except ValueError as e:
        logger.warning(f"Order status update validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="ORDER_STATUS_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error updating order status for order {path_params.get('order_id')}: {str(e)}")
        return error_response(
            message="Failed to update order status",
            code="ORDER_STATUS_UPDATE_ERROR"
        )

@order_bp.route('/admin/orders/<order_id>/issues', methods=['POST'])
@strict_rate_limit()
@auth_required
@admin_required
@validate_path(order_id=fields.String(required=True))
@validate_json(OrderIssueCreateSchema)
def create_order_issue(path_params, validated_data):
    """Create order issue (Admin only)"""
    try:
        order_id = path_params['order_id']
        admin_id = g.user_id

        result = order_service.create_order_issue(
            order_id=order_id,
            order_item_id=validated_data['orderItemId'],
            issue_type=validated_data['issueType'],
            details=validated_data['details'],
            proposed_solution=validated_data.get('proposedSolution', ''),
            admin_id=admin_id
        )

        if not result:
            return error_response(
                message="Order or order item not found",
                code="ORDER_NOT_FOUND",
                status_code=404
            )

        return success_response(
            data=result,
            message="Order issue created successfully",
            status_code=201
        )

    except ValueError as e:
        logger.warning(f"Order issue creation validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="ORDER_ISSUE_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error creating order issue for order {path_params.get('order_id')}: {str(e)}")
        return error_response(
            message="Failed to create order issue",
            code="ORDER_ISSUE_CREATE_ERROR"
        )

@order_bp.route('/admin/orders/<order_id>/payment/verify', methods=['POST'])
@strict_rate_limit()
@auth_required
@admin_required
@validate_path(order_id=fields.String(required=True))
def verify_payment(path_params):
    """Verify manual payment (Admin only)"""
    try:
        order_id = path_params['order_id']
        admin_id = g.user_id

        # Get verification details from request
        verification_notes = ''
        if request.is_json:
            json_data = request.get_json()
            verification_notes = json_data.get('verificationNotes', '')

        result = order_service.verify_payment(order_id, admin_id, verification_notes)

        if not result:
            return error_response(
                message="Order not found or payment verification not allowed",
                code="ORDER_NOT_FOUND",
                status_code=404
            )

        return success_response(
            data=result,
            message="Payment verified successfully"
        )

    except ValueError as e:
        logger.warning(f"Payment verification validation error: {str(e)}")
        return error_response(
            message=str(e),
            code="PAYMENT_VERIFICATION_VALIDATION_ERROR",
            status_code=400
        )
    except Exception as e:
        logger.error(f"Error verifying payment for order {path_params.get('order_id')}: {str(e)}")
        return error_response(
            message="Failed to verify payment",
            code="PAYMENT_VERIFICATION_ERROR"
        )

@order_bp.route('/admin/orders/pending-payment', methods=['GET'])
@rate_limit()
@auth_required
@admin_required
def get_pending_payment_orders():
    """Get orders pending payment verification (Admin only)"""
    try:
        result = order_service.get_pending_payment_orders()

        return success_response(
            data=result,
            message="Pending payment orders retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting pending payment orders: {str(e)}")
        return error_response(
            message="Failed to retrieve pending payment orders",
            code="PENDING_PAYMENT_ORDERS_FETCH_ERROR"
        )
