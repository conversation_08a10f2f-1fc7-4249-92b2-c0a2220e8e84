"""
Search API endpoints
"""
import logging
from flask import Blueprint, jsonify, request, g
from marshmallow import Schema, fields, validate
from middleware.auth import auth_required, admin_required, optional_auth
from middleware.validation import validate_json, validate_query, validate_path, PaginationSchema
from middleware.rate_limiting import rate_limit, strict_rate_limit, lenient_rate_limit
from services.search_service import SearchService
from utils.formatters import success_response, error_response

logger = logging.getLogger(__name__)

# Create Blueprint
search_bp = Blueprint('search', __name__)

# Initialize service
search_service = SearchService()

# Validation schemas
class ProductSearchSchema(Schema):
    """Schema for advanced product search"""
    query = fields.String(required=True, validate=validate.Length(min=1, max=200))
    category = fields.String(missing=None, validate=validate.Length(max=100))
    min_price = fields.Float(missing=None, validate=validate.Range(min=0))
    max_price = fields.Float(missing=None, validate=validate.Range(min=0))
    status = fields.String(missing='Active', validate=validate.OneOf(['Active', 'Archived', 'Discontinued']))
    sort_by = fields.String(missing='relevance', validate=validate.OneOf([
        'relevance', 'price_asc', 'price_desc', 'name_asc', 'name_desc', 'newest', 'oldest'
    ]))
    page = fields.Integer(missing=1, validate=validate.Range(min=1))
    per_page = fields.Integer(missing=20, validate=validate.Range(min=1, max=100))
    facets = fields.Boolean(missing=True)
    highlight = fields.Boolean(missing=True)

class GroupBuySearchSchema(Schema):
    """Schema for group buy search"""
    query = fields.String(required=True, validate=validate.Length(min=1, max=200))
    status = fields.String(missing='Active', validate=validate.OneOf(['Active', 'Successful', 'Failed', 'Cancelled']))
    category = fields.String(missing=None, validate=validate.Length(max=100))
    expiring_soon = fields.Boolean(missing=False)
    min_progress = fields.Integer(missing=None, validate=validate.Range(min=0, max=100))
    sort_by = fields.String(missing='relevance', validate=validate.OneOf([
        'relevance', 'progress_desc', 'progress_asc', 'expiring_soon', 'newest', 'oldest'
    ]))
    page = fields.Integer(missing=1, validate=validate.Range(min=1))
    per_page = fields.Integer(missing=20, validate=validate.Range(min=1, max=100))

class SearchSuggestionsSchema(Schema):
    """Schema for search suggestions"""
    query = fields.String(required=True, validate=validate.Length(min=1, max=100))
    limit = fields.Integer(missing=5, validate=validate.Range(min=1, max=20))
    type = fields.String(missing='all', validate=validate.OneOf(['all', 'products', 'groupbuys', 'categories']))

class CategorySearchSchema(Schema):
    """Schema for category search"""
    query = fields.String(missing='', validate=validate.Length(max=100))
    include_counts = fields.Boolean(missing=True)

class ReindexSchema(Schema):
    """Schema for reindex operations"""
    type = fields.String(required=True, validate=validate.OneOf(['products', 'groupbuys', 'all']))
    force = fields.Boolean(missing=False)

class SyncSchema(Schema):
    """Schema for sync operations"""
    type = fields.String(required=True, validate=validate.OneOf(['product', 'groupbuy']))
    ids = fields.List(fields.String(), required=True, validate=validate.Length(min=1, max=100))

# Public endpoints
@search_bp.route('/search/products', methods=['POST'])
@lenient_rate_limit()
@optional_auth
@validate_json(ProductSearchSchema)
def search_products(validated_data):
    """Advanced product search with filters and faceting"""
    try:
        user_id = getattr(g, 'user_id', None)

        result = search_service.search_products(
            query=validated_data['query'],
            filters={
                'category': validated_data.get('category'),
                'min_price': validated_data.get('min_price'),
                'max_price': validated_data.get('max_price'),
                'status': validated_data.get('status')
            },
            sort_by=validated_data.get('sort_by'),
            page=validated_data.get('page'),
            per_page=validated_data.get('per_page'),
            include_facets=validated_data.get('facets'),
            include_highlights=validated_data.get('highlight'),
            user_id=user_id
        )

        return success_response(
            data=result,
            message="Product search completed successfully"
        )

    except Exception as e:
        logger.error(f"Error in product search: {str(e)}")
        return error_response(
            message="Failed to search products",
            code="PRODUCT_SEARCH_ERROR"
        )

@search_bp.route('/search/suggestions', methods=['GET'])
@lenient_rate_limit()
@optional_auth
@validate_query(SearchSuggestionsSchema)
def get_search_suggestions(query_params):
    """Get search autocomplete and suggestions"""
    try:
        user_id = getattr(g, 'user_id', None)

        result = search_service.get_search_suggestions(
            query=query_params['query'],
            limit=query_params.get('limit'),
            search_type=query_params.get('type'),
            user_id=user_id
        )

        return success_response(
            data=result,
            message="Search suggestions retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting search suggestions: {str(e)}")
        return error_response(
            message="Failed to get search suggestions",
            code="SEARCH_SUGGESTIONS_ERROR"
        )

@search_bp.route('/search/groupbuys', methods=['POST'])
@lenient_rate_limit()
@optional_auth
@validate_json(GroupBuySearchSchema)
def search_groupbuys(validated_data):
    """Search group buys with advanced filtering"""
    try:
        user_id = getattr(g, 'user_id', None)

        result = search_service.search_groupbuys(
            query=validated_data['query'],
            filters={
                'status': validated_data.get('status'),
                'category': validated_data.get('category'),
                'expiring_soon': validated_data.get('expiring_soon'),
                'min_progress': validated_data.get('min_progress')
            },
            sort_by=validated_data.get('sort_by'),
            page=validated_data.get('page'),
            per_page=validated_data.get('per_page'),
            user_id=user_id
        )

        return success_response(
            data=result,
            message="Group buy search completed successfully"
        )

    except Exception as e:
        logger.error(f"Error in group buy search: {str(e)}")
        return error_response(
            message="Failed to search group buys",
            code="GROUPBUY_SEARCH_ERROR"
        )

@search_bp.route('/search/categories', methods=['GET'])
@lenient_rate_limit()
@optional_auth
@validate_query(CategorySearchSchema)
def search_categories(query_params):
    """Search and filter product categories"""
    try:
        result = search_service.search_categories(
            query=query_params.get('query', ''),
            include_counts=query_params.get('include_counts')
        )

        return success_response(
            data=result,
            message="Categories retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error searching categories: {str(e)}")
        return error_response(
            message="Failed to search categories",
            code="CATEGORY_SEARCH_ERROR"
        )

@search_bp.route('/search/trending', methods=['GET'])
@lenient_rate_limit()
@optional_auth
def get_trending_searches():
    """Get trending search terms and popular queries"""
    try:
        user_id = getattr(g, 'user_id', None)

        result = search_service.get_trending_searches(user_id=user_id)

        return success_response(
            data=result,
            message="Trending searches retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting trending searches: {str(e)}")
        return error_response(
            message="Failed to get trending searches",
            code="TRENDING_SEARCHES_ERROR"
        )

# Admin endpoints
@search_bp.route('/admin/search/reindex', methods=['POST'])
@strict_rate_limit()
@auth_required
@admin_required
@validate_json(ReindexSchema)
def reindex_search(validated_data):
    """Manually trigger search index rebuild (Admin only)"""
    try:
        admin_id = g.user_id

        result = search_service.reindex_search_data(
            index_type=validated_data['type'],
            force=validated_data.get('force', False),
            admin_id=admin_id
        )

        return success_response(
            data=result,
            message="Search reindex initiated successfully"
        )

    except Exception as e:
        logger.error(f"Error initiating search reindex: {str(e)}")
        return error_response(
            message="Failed to initiate search reindex",
            code="SEARCH_REINDEX_ERROR"
        )

@search_bp.route('/admin/search/sync', methods=['POST'])
@strict_rate_limit()
@auth_required
@admin_required
@validate_json(SyncSchema)
def sync_search_data(validated_data):
    """Sync specific products/group buys to search index (Admin only)"""
    try:
        admin_id = g.user_id

        result = search_service.sync_search_data(
            data_type=validated_data['type'],
            ids=validated_data['ids'],
            admin_id=admin_id
        )

        return success_response(
            data=result,
            message="Search data sync completed successfully"
        )

    except Exception as e:
        logger.error(f"Error syncing search data: {str(e)}")
        return error_response(
            message="Failed to sync search data",
            code="SEARCH_SYNC_ERROR"
        )

@search_bp.route('/admin/search/analytics', methods=['GET'])
@rate_limit()
@auth_required
@admin_required
def get_search_analytics():
    """Get search performance metrics and analytics (Admin only)"""
    try:
        result = search_service.get_search_analytics()

        return success_response(
            data=result,
            message="Search analytics retrieved successfully"
        )

    except Exception as e:
        logger.error(f"Error getting search analytics: {str(e)}")
        return error_response(
            message="Failed to get search analytics",
            code="SEARCH_ANALYTICS_ERROR"
        )
