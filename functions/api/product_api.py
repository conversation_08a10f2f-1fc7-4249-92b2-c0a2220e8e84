"""
Product API endpoints
"""
import logging
from flask import Blueprint, jsonify, request
from marshmallow import Schema, fields, validate
from middleware.auth import auth_required, admin_required, optional_auth
from middleware.validation import validate_json, validate_query, validate_path, PaginationSchema
from middleware.rate_limiting import rate_limit, lenient_rate_limit
from services.product_service import ProductService
from utils.formatters import success_response, error_response

logger = logging.getLogger(__name__)

# Create Blueprint
product_bp = Blueprint('products', __name__)

# Initialize service
product_service = ProductService()

# Validation schemas
class ProductFilterSchema(PaginationSchema):
    """Schema for product filtering"""
    category = fields.String(missing=None)
    status = fields.String(missing='Active', validate=validate.OneOf(['Active', 'Archived', 'Discontinued']))
    search = fields.String(missing=None)

class ProductCreateSchema(Schema):
    """Schema for creating products"""
    name = fields.String(required=True, validate=validate.Length(min=1, max=200))
    description = fields.String(required=True, validate=validate.Length(min=1, max=2000))
    category = fields.String(required=True, validate=validate.Length(min=1, max=100))
    coverImageUrl = fields.String(missing='')
    images = fields.List(fields.String(), missing=[])
    status = fields.String(missing='Active', validate=validate.OneOf(['Active', 'Archived', 'Discontinued']))

class ProductUpdateSchema(Schema):
    """Schema for updating products"""
    name = fields.String(validate=validate.Length(min=1, max=200))
    description = fields.String(validate=validate.Length(min=1, max=2000))
    category = fields.String(validate=validate.Length(min=1, max=100))
    coverImageUrl = fields.String()
    images = fields.List(fields.String())
    status = fields.String(validate=validate.OneOf(['Active', 'Archived', 'Discontinued']))

class VariantCreateSchema(Schema):
    """Schema for creating product variants"""
    sku = fields.String(required=True, validate=validate.Length(min=1, max=100))
    myPrice = fields.Float(required=True, validate=validate.Range(min=0))
    attributes = fields.Dict(missing={})
    status = fields.String(missing='Active', validate=validate.OneOf(['Active', 'Inactive']))

# Public endpoints
@product_bp.route('/products', methods=['GET'])
@lenient_rate_limit()
@optional_auth
@validate_query(ProductFilterSchema)
def get_products(query_params):
    """Get products with filtering and pagination"""
    try:
        result = product_service.get_products(
            category=query_params.get('category'),
            status=query_params.get('status'),
            search=query_params.get('search'),
            page=query_params.get('page'),
            per_page=query_params.get('per_page'),
            sort_by=query_params.get('sort_by'),
            sort_order=query_params.get('sort_order')
        )
        
        return success_response(
            data=result,
            message="Products retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting products: {str(e)}")
        return error_response(
            message="Failed to retrieve products",
            code="PRODUCTS_FETCH_ERROR"
        )

@product_bp.route('/products/<product_id>', methods=['GET'])
@lenient_rate_limit()
@optional_auth
@validate_path(product_id=fields.String(required=True))
def get_product(path_params):
    """Get product by ID"""
    try:
        product_id = path_params['product_id']
        result = product_service.get_product_by_id(product_id)
        
        if not result:
            return error_response(
                message="Product not found",
                code="PRODUCT_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            data=result,
            message="Product retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting product {path_params.get('product_id')}: {str(e)}")
        return error_response(
            message="Failed to retrieve product",
            code="PRODUCT_FETCH_ERROR"
        )

@product_bp.route('/products/<product_id>/variants', methods=['GET'])
@lenient_rate_limit()
@optional_auth
@validate_path(product_id=fields.String(required=True))
def get_product_variants(path_params):
    """Get product variants"""
    try:
        product_id = path_params['product_id']
        result = product_service.get_product_variants(product_id)
        
        return success_response(
            data=result,
            message="Product variants retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting variants for product {path_params.get('product_id')}: {str(e)}")
        return error_response(
            message="Failed to retrieve product variants",
            code="VARIANTS_FETCH_ERROR"
        )

@product_bp.route('/categories', methods=['GET'])
@lenient_rate_limit()
@optional_auth
def get_categories():
    """Get all product categories"""
    try:
        result = product_service.get_categories()
        
        return success_response(
            data=result,
            message="Categories retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting categories: {str(e)}")
        return error_response(
            message="Failed to retrieve categories",
            code="CATEGORIES_FETCH_ERROR"
        )

# Admin endpoints
@product_bp.route('/admin/products', methods=['POST'])
@rate_limit()
@auth_required
@admin_required
@validate_json(ProductCreateSchema)
def create_product(validated_data):
    """Create new product (Admin only)"""
    try:
        result = product_service.create_product(validated_data)
        
        return success_response(
            data=result,
            message="Product created successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Error creating product: {str(e)}")
        return error_response(
            message="Failed to create product",
            code="PRODUCT_CREATE_ERROR"
        )

@product_bp.route('/admin/products/<product_id>', methods=['PUT'])
@rate_limit()
@auth_required
@admin_required
@validate_path(product_id=fields.String(required=True))
@validate_json(ProductUpdateSchema)
def update_product(path_params, validated_data):
    """Update product (Admin only)"""
    try:
        product_id = path_params['product_id']
        result = product_service.update_product(product_id, validated_data)
        
        if not result:
            return error_response(
                message="Product not found",
                code="PRODUCT_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            data=result,
            message="Product updated successfully"
        )
        
    except Exception as e:
        logger.error(f"Error updating product {path_params.get('product_id')}: {str(e)}")
        return error_response(
            message="Failed to update product",
            code="PRODUCT_UPDATE_ERROR"
        )

@product_bp.route('/admin/products/<product_id>', methods=['DELETE'])
@rate_limit()
@auth_required
@admin_required
@validate_path(product_id=fields.String(required=True))
def delete_product(path_params):
    """Delete product (Admin only)"""
    try:
        product_id = path_params['product_id']
        result = product_service.delete_product(product_id)
        
        if not result:
            return error_response(
                message="Product not found",
                code="PRODUCT_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            message="Product deleted successfully"
        )
        
    except Exception as e:
        logger.error(f"Error deleting product {path_params.get('product_id')}: {str(e)}")
        return error_response(
            message="Failed to delete product",
            code="PRODUCT_DELETE_ERROR"
        )

@product_bp.route('/admin/products/<product_id>/variants', methods=['POST'])
@rate_limit()
@auth_required
@admin_required
@validate_path(product_id=fields.String(required=True))
@validate_json(VariantCreateSchema)
def create_variant(path_params, validated_data):
    """Create product variant (Admin only)"""
    try:
        product_id = path_params['product_id']
        validated_data['productId'] = product_id
        
        result = product_service.create_variant(validated_data)
        
        return success_response(
            data=result,
            message="Product variant created successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Error creating variant for product {path_params.get('product_id')}: {str(e)}")
        return error_response(
            message="Failed to create product variant",
            code="VARIANT_CREATE_ERROR"
        )
