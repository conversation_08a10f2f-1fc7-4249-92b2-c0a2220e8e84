"""
User API endpoints
"""
import logging
from flask import Blueprint, jsonify, request, g
from marshmallow import Schema, fields, validate
from middleware.auth import auth_required, admin_required
from middleware.validation import validate_json, validate_query, validate_path, EmailField, PhoneField
from middleware.rate_limiting import rate_limit, strict_rate_limit
from services.user_service import UserService
from utils.formatters import success_response, error_response

logger = logging.getLogger(__name__)

# Create Blueprint
user_bp = Blueprint('users', __name__)

# Initialize service
user_service = UserService()

# Validation schemas
class UserProfileUpdateSchema(Schema):
    """Schema for updating user profile"""
    displayName = fields.String(validate=validate.Length(min=1, max=100))
    photoURL = fields.String(validate=validate.URL())
    phoneNumber = PhoneField()

class ShippingAddressSchema(Schema):
    """Schema for shipping address"""
    addressLine1 = fields.String(required=True, validate=validate.Length(min=1, max=200))
    addressLine2 = fields.String(missing='', validate=validate.Length(max=200))
    city = fields.String(required=True, validate=validate.Length(min=1, max=100))
    stateOrProvince = fields.String(required=True, validate=validate.Length(min=1, max=100))
    postalCode = fields.String(required=True, validate=validate.Length(min=1, max=20))
    country = fields.String(required=True, validate=validate.Length(min=1, max=100))
    isDefault = fields.Boolean(missing=False)

class ShippingAddressUpdateSchema(Schema):
    """Schema for updating shipping address"""
    addressLine1 = fields.String(validate=validate.Length(min=1, max=200))
    addressLine2 = fields.String(validate=validate.Length(max=200))
    city = fields.String(validate=validate.Length(min=1, max=100))
    stateOrProvince = fields.String(validate=validate.Length(min=1, max=100))
    postalCode = fields.String(validate=validate.Length(min=1, max=20))
    country = fields.String(validate=validate.Length(min=1, max=100))
    isDefault = fields.Boolean()

# User profile endpoints
@user_bp.route('/user/profile', methods=['GET'])
@rate_limit()
@auth_required
def get_user_profile():
    """Get user profile"""
    try:
        user_id = g.user_id
        result = user_service.get_user_profile(user_id)
        
        if not result:
            return error_response(
                message="User profile not found",
                code="USER_PROFILE_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            data=result,
            message="User profile retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting user profile for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to retrieve user profile",
            code="USER_PROFILE_FETCH_ERROR"
        )

@user_bp.route('/user/profile', methods=['PUT'])
@rate_limit()
@auth_required
@validate_json(UserProfileUpdateSchema)
def update_user_profile(validated_data):
    """Update user profile"""
    try:
        user_id = g.user_id
        result = user_service.update_user_profile(user_id, validated_data)
        
        if not result:
            return error_response(
                message="User profile not found",
                code="USER_PROFILE_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            data=result,
            message="User profile updated successfully"
        )
        
    except Exception as e:
        logger.error(f"Error updating user profile for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to update user profile",
            code="USER_PROFILE_UPDATE_ERROR"
        )

# Shipping address endpoints
@user_bp.route('/user/addresses', methods=['GET'])
@rate_limit()
@auth_required
def get_shipping_addresses():
    """Get user's shipping addresses"""
    try:
        user_id = g.user_id
        result = user_service.get_shipping_addresses(user_id)
        
        return success_response(
            data=result,
            message="Shipping addresses retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting shipping addresses for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to retrieve shipping addresses",
            code="ADDRESSES_FETCH_ERROR"
        )

@user_bp.route('/user/addresses', methods=['POST'])
@rate_limit()
@auth_required
@validate_json(ShippingAddressSchema)
def add_shipping_address(validated_data):
    """Add new shipping address"""
    try:
        user_id = g.user_id
        result = user_service.add_shipping_address(user_id, validated_data)
        
        return success_response(
            data=result,
            message="Shipping address added successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Error adding shipping address for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to add shipping address",
            code="ADDRESS_ADD_ERROR"
        )

@user_bp.route('/user/addresses/<address_id>', methods=['PUT'])
@rate_limit()
@auth_required
@validate_path(address_id=fields.String(required=True))
@validate_json(ShippingAddressUpdateSchema)
def update_shipping_address(path_params, validated_data):
    """Update shipping address"""
    try:
        user_id = g.user_id
        address_id = path_params['address_id']
        
        result = user_service.update_shipping_address(user_id, address_id, validated_data)
        
        if not result:
            return error_response(
                message="Shipping address not found",
                code="ADDRESS_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            data=result,
            message="Shipping address updated successfully"
        )
        
    except Exception as e:
        logger.error(f"Error updating shipping address {path_params.get('address_id')} for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to update shipping address",
            code="ADDRESS_UPDATE_ERROR"
        )

@user_bp.route('/user/addresses/<address_id>', methods=['DELETE'])
@rate_limit()
@auth_required
@validate_path(address_id=fields.String(required=True))
def delete_shipping_address(path_params):
    """Delete shipping address"""
    try:
        user_id = g.user_id
        address_id = path_params['address_id']
        
        result = user_service.delete_shipping_address(user_id, address_id)
        
        if not result:
            return error_response(
                message="Shipping address not found",
                code="ADDRESS_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            message="Shipping address deleted successfully"
        )
        
    except Exception as e:
        logger.error(f"Error deleting shipping address {path_params.get('address_id')} for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to delete shipping address",
            code="ADDRESS_DELETE_ERROR"
        )

@user_bp.route('/user/addresses/<address_id>/set-default', methods=['POST'])
@rate_limit()
@auth_required
@validate_path(address_id=fields.String(required=True))
def set_default_address(path_params):
    """Set default shipping address"""
    try:
        user_id = g.user_id
        address_id = path_params['address_id']
        
        result = user_service.set_default_address(user_id, address_id)
        
        if not result:
            return error_response(
                message="Shipping address not found",
                code="ADDRESS_NOT_FOUND",
                status_code=404
            )
        
        return success_response(
            data=result,
            message="Default shipping address updated successfully"
        )
        
    except Exception as e:
        logger.error(f"Error setting default address {path_params.get('address_id')} for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to set default address",
            code="DEFAULT_ADDRESS_ERROR"
        )

# User initialization endpoint (called after authentication)
@user_bp.route('/user/initialize', methods=['POST'])
@rate_limit()
@auth_required
def initialize_user():
    """Initialize user profile after authentication"""
    try:
        user_id = g.user_id
        user_email = g.user_email
        
        result = user_service.initialize_user_profile(user_id, user_email)
        
        return success_response(
            data=result,
            message="User profile initialized successfully"
        )
        
    except Exception as e:
        logger.error(f"Error initializing user profile for user {g.user_id}: {str(e)}")
        return error_response(
            message="Failed to initialize user profile",
            code="USER_INITIALIZATION_ERROR"
        )
