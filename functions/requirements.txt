# Firebase Functions dependencies
functions-framework==3.*
firebase-admin==6.*
google-cloud-firestore==2.*

# Web framework
flask==2.*
flask-cors==4.*

# Search integration
algoliasearch==3.*

# Data validation and serialization
marshmallow==3.*
marshmallow-dataclass==8.*

# HTTP requests
requests==2.*

# Date/time handling
python-dateutil==2.*

# Utilities
python-dotenv==1.*

# Rate limiting
redis==4.*

# Payment processing
stripe==7.*

# Logging and monitoring
structlog==23.*

# Testing (dev dependencies)
pytest==7.*
pytest-mock==3.*
pytest-asyncio==0.*

# Type hints
typing-extensions==4.*