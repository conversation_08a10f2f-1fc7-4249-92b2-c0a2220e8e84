"""
Response formatting utilities
"""
import time
import uuid
from typing import Any, Dict, Optional
from flask import jsonify, Response

def success_response(data: Any = None, 
                    message: str = "Success", 
                    status_code: int = 200,
                    meta: Optional[Dict[str, Any]] = None) -> tuple:
    """Format successful API response"""
    response_data = {
        'success': True,
        'message': message,
        'timestamp': int(time.time()),
        'request_id': str(uuid.uuid4())[:8]
    }
    
    if data is not None:
        response_data['data'] = data
    
    if meta:
        response_data['meta'] = meta
    
    return jsonify(response_data), status_code

def error_response(message: str = "An error occurred",
                  code: str = "ERROR",
                  status_code: int = 400,
                  details: Optional[Dict[str, Any]] = None) -> tuple:
    """Format error API response"""
    response_data = {
        'success': False,
        'error': {
            'code': code,
            'message': message
        },
        'timestamp': int(time.time()),
        'request_id': str(uuid.uuid4())[:8]
    }
    
    if details:
        response_data['error']['details'] = details
    
    return jsonify(response_data), status_code

def paginated_response(items: list,
                      page: int,
                      per_page: int,
                      total: int,
                      message: str = "Data retrieved successfully") -> tuple:
    """Format paginated API response"""
    total_pages = (total + per_page - 1) // per_page
    
    data = {
        'items': items,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total': total,
            'pages': total_pages,
            'has_next': page < total_pages,
            'has_prev': page > 1
        }
    }
    
    return success_response(data=data, message=message)

def validation_error_response(errors: Dict[str, Any]) -> tuple:
    """Format validation error response"""
    return error_response(
        message="Validation failed",
        code="VALIDATION_ERROR",
        status_code=400,
        details=errors
    )

def not_found_response(resource: str = "Resource") -> tuple:
    """Format not found error response"""
    return error_response(
        message=f"{resource} not found",
        code="NOT_FOUND",
        status_code=404
    )

def unauthorized_response(message: str = "Authentication required") -> tuple:
    """Format unauthorized error response"""
    return error_response(
        message=message,
        code="UNAUTHORIZED",
        status_code=401
    )

def forbidden_response(message: str = "Insufficient privileges") -> tuple:
    """Format forbidden error response"""
    return error_response(
        message=message,
        code="FORBIDDEN",
        status_code=403
    )

def rate_limit_response(retry_after: Optional[int] = None) -> tuple:
    """Format rate limit error response"""
    response = error_response(
        message="Rate limit exceeded",
        code="RATE_LIMIT_EXCEEDED",
        status_code=429
    )
    
    if retry_after:
        response[0].headers['Retry-After'] = str(retry_after)
    
    return response

def internal_error_response(message: str = "Internal server error") -> tuple:
    """Format internal server error response"""
    return error_response(
        message=message,
        code="INTERNAL_ERROR",
        status_code=500
    )

def format_timestamp(timestamp: int) -> str:
    """Format timestamp to ISO string"""
    import datetime
    return datetime.datetime.fromtimestamp(timestamp / 1000).isoformat()

def format_currency(amount: float, currency: str = "USD") -> str:
    """Format currency amount"""
    if currency == "USD":
        return f"${amount:.2f}"
    elif currency == "EUR":
        return f"€{amount:.2f}"
    elif currency == "GBP":
        return f"£{amount:.2f}"
    else:
        return f"{currency} {amount:.2f}"

def sanitize_data(data: Any) -> Any:
    """Sanitize data for API response"""
    if isinstance(data, dict):
        sanitized = {}
        for key, value in data.items():
            # Skip private fields
            if key.startswith('_'):
                continue
            sanitized[key] = sanitize_data(value)
        return sanitized
    elif isinstance(data, list):
        return [sanitize_data(item) for item in data]
    elif isinstance(data, str):
        # Basic HTML escaping
        return data.replace('<', '&lt;').replace('>', '&gt;')
    else:
        return data

def add_cors_headers(response: Response, origin: str = None) -> Response:
    """Add CORS headers to response"""
    if origin:
        response.headers['Access-Control-Allow-Origin'] = origin
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
    response.headers['Access-Control-Allow-Credentials'] = 'true'
    return response

def create_api_key() -> str:
    """Generate API key"""
    import secrets
    import string
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(32))

def mask_sensitive_data(data: Dict[str, Any], sensitive_fields: list = None) -> Dict[str, Any]:
    """Mask sensitive data in API responses"""
    if sensitive_fields is None:
        sensitive_fields = ['password', 'token', 'secret', 'key', 'private']
    
    masked_data = data.copy()
    
    for field in sensitive_fields:
        if field in masked_data:
            if isinstance(masked_data[field], str) and len(masked_data[field]) > 4:
                masked_data[field] = masked_data[field][:2] + '*' * (len(masked_data[field]) - 4) + masked_data[field][-2:]
            else:
                masked_data[field] = '***'
    
    return masked_data

def calculate_response_time(start_time: float) -> float:
    """Calculate response time in milliseconds"""
    return round((time.time() - start_time) * 1000, 2)

def create_etag(data: Any) -> str:
    """Create ETag for response caching"""
    import hashlib
    import json
    
    if isinstance(data, dict) or isinstance(data, list):
        data_str = json.dumps(data, sort_keys=True)
    else:
        data_str = str(data)
    
    return hashlib.md5(data_str.encode()).hexdigest()

def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    import math
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    return text[:max_length - len(suffix)] + suffix

def validate_and_format_phone(phone: str) -> str:
    """Validate and format phone number"""
    import re
    # Remove all non-digit characters
    digits = re.sub(r'\D', '', phone)
    
    # Basic validation
    if len(digits) < 10:
        raise ValueError("Phone number too short")
    
    # Format as +1 (XXX) XXX-XXXX for US numbers
    if len(digits) == 10:
        return f"+1 ({digits[:3]}) {digits[3:6]}-{digits[6:]}"
    elif len(digits) == 11 and digits[0] == '1':
        return f"+1 ({digits[1:4]}) {digits[4:7]}-{digits[7:]}"
    else:
        return f"+{digits}"

def generate_order_number() -> str:
    """Generate unique order number"""
    import datetime
    now = datetime.datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")
    random_suffix = str(uuid.uuid4())[:4].upper()
    return f"ORD-{timestamp}-{random_suffix}"

def calculate_discount_percentage(original_price: float, discounted_price: float) -> int:
    """Calculate discount percentage"""
    if original_price <= 0:
        return 0
    
    discount = original_price - discounted_price
    percentage = (discount / original_price) * 100
    return max(0, min(100, round(percentage)))

def format_search_results(results: list, query: str) -> list:
    """Format search results with highlighting"""
    formatted_results = []
    
    for result in results:
        formatted_result = result.copy()
        
        # Add search relevance score if available
        if '_score' in result:
            formatted_result['relevance_score'] = result['_score']
        
        # Add query context
        formatted_result['search_query'] = query
        
        formatted_results.append(formatted_result)
    
    return formatted_results
