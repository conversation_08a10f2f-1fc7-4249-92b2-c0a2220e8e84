"""
CORS configuration for Firebase Cloud Functions
"""
import os
import logging
from typing import List, Dict, Any
from flask_cors import CORS
from flask import Flask

logger = logging.getLogger(__name__)

class CORSConfig:
    """CORS configuration management"""
    
    @classmethod
    def get_cors_origins(cls) -> List[str]:
        """Get allowed CORS origins from environment"""
        origins_str = os.getenv('CORS_ORIGINS', 'http://localhost:3000')
        origins = [origin.strip() for origin in origins_str.split(',')]
        
        # Add default origins for development
        default_origins = [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://127.0.0.1:3000',
            'http://127.0.0.1:3001'
        ]
        
        # Merge with environment origins, avoiding duplicates
        all_origins = list(set(origins + default_origins))
        
        logger.info(f"CORS origins configured: {all_origins}")
        return all_origins
    
    @classmethod
    def get_cors_methods(cls) -> List[str]:
        """Get allowed CORS methods"""
        methods_str = os.getenv('CORS_METHODS', 'GET,POST,PUT,DELETE,OPTIONS')
        methods = [method.strip() for method in methods_str.split(',')]
        
        # Ensure OPTIONS is always included
        if 'OPTIONS' not in methods:
            methods.append('OPTIONS')
        
        return methods
    
    @classmethod
    def get_cors_headers(cls) -> List[str]:
        """Get allowed CORS headers"""
        headers_str = os.getenv('CORS_HEADERS', 'Content-Type,Authorization,X-Requested-With')
        headers = [header.strip() for header in headers_str.split(',')]
        
        # Add essential headers if not present
        essential_headers = [
            'Content-Type',
            'Authorization',
            'X-Requested-With',
            'Accept',
            'Origin',
            'X-CSRF-Token'
        ]
        
        for header in essential_headers:
            if header not in headers:
                headers.append(header)
        
        return headers
    
    @classmethod
    def configure_cors(cls, app: Flask) -> CORS:
        """Configure CORS for Flask app"""
        try:
            cors_config = {
                'origins': cls.get_cors_origins(),
                'methods': cls.get_cors_methods(),
                'allow_headers': cls.get_cors_headers(),
                'supports_credentials': True,
                'max_age': 86400,  # 24 hours
                'send_wildcard': False,
                'automatic_options': True
            }
            
            cors = CORS(app, resources={
                r"/api/*": cors_config,
                r"/admin/*": cors_config
            })
            
            logger.info("CORS configured successfully")
            return cors
            
        except Exception as e:
            logger.error(f"Failed to configure CORS: {str(e)}")
            raise
    
    @classmethod
    def is_origin_allowed(cls, origin: str) -> bool:
        """Check if origin is allowed"""
        allowed_origins = cls.get_cors_origins()
        
        # Check exact match
        if origin in allowed_origins:
            return True
        
        # Check wildcard patterns (if any)
        for allowed_origin in allowed_origins:
            if allowed_origin == '*':
                return True
            if allowed_origin.endswith('*'):
                pattern = allowed_origin[:-1]
                if origin.startswith(pattern):
                    return True
        
        return False
    
    @classmethod
    def get_cors_response_headers(cls, origin: str = None) -> Dict[str, str]:
        """Get CORS response headers"""
        headers = {}
        
        if origin and cls.is_origin_allowed(origin):
            headers['Access-Control-Allow-Origin'] = origin
        else:
            # Use first allowed origin as fallback
            origins = cls.get_cors_origins()
            if origins:
                headers['Access-Control-Allow-Origin'] = origins[0]
        
        headers.update({
            'Access-Control-Allow-Methods': ','.join(cls.get_cors_methods()),
            'Access-Control-Allow-Headers': ','.join(cls.get_cors_headers()),
            'Access-Control-Allow-Credentials': 'true',
            'Access-Control-Max-Age': '86400'
        })
        
        return headers
    
    @classmethod
    def handle_preflight(cls, origin: str = None) -> tuple:
        """Handle CORS preflight request"""
        if origin and not cls.is_origin_allowed(origin):
            return {'error': 'Origin not allowed'}, 403
        
        headers = cls.get_cors_response_headers(origin)
        return '', 200, headers

# Environment-specific CORS settings
class EnvironmentCORS:
    """Environment-specific CORS configurations"""
    
    @classmethod
    def get_development_config(cls) -> Dict[str, Any]:
        """Get development CORS configuration"""
        return {
            'origins': [
                'http://localhost:3000',
                'http://localhost:3001',
                'http://127.0.0.1:3000',
                'http://127.0.0.1:3001',
                'http://localhost:8080',
                'http://127.0.0.1:8080'
            ],
            'methods': ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
            'allow_headers': ['*'],
            'supports_credentials': True,
            'send_wildcard': False
        }
    
    @classmethod
    def get_staging_config(cls) -> Dict[str, Any]:
        """Get staging CORS configuration"""
        return {
            'origins': [
                'https://staging-admin.maomao.com',
                'https://staging.maomao.com'
            ],
            'methods': ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            'allow_headers': [
                'Content-Type',
                'Authorization',
                'X-Requested-With',
                'Accept',
                'Origin'
            ],
            'supports_credentials': True,
            'send_wildcard': False
        }
    
    @classmethod
    def get_production_config(cls) -> Dict[str, Any]:
        """Get production CORS configuration"""
        return {
            'origins': [
                'https://admin.maomao.com',
                'https://maomao.com',
                'https://www.maomao.com'
            ],
            'methods': ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            'allow_headers': [
                'Content-Type',
                'Authorization',
                'X-Requested-With',
                'Accept',
                'Origin'
            ],
            'supports_credentials': True,
            'send_wildcard': False,
            'max_age': 86400
        }
    
    @classmethod
    def get_config_for_environment(cls, environment: str = None) -> Dict[str, Any]:
        """Get CORS configuration for specific environment"""
        if environment is None:
            environment = os.getenv('ENVIRONMENT', 'development')
        
        environment = environment.lower()
        
        if environment == 'production':
            return cls.get_production_config()
        elif environment == 'staging':
            return cls.get_staging_config()
        else:
            return cls.get_development_config()
