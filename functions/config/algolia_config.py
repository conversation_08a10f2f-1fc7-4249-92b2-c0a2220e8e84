"""
Algolia search configuration and client management
"""
import os
import logging
from typing import Optional, Dict, Any, List
from algoliasearch.search_client import SearchClient
from algoliasearch.search_index import SearchIndex

logger = logging.getLogger(__name__)

class AlgoliaConfig:
    """Algolia search configuration and client management"""
    
    _client: Optional[SearchClient] = None
    _indices: Dict[str, SearchIndex] = {}
    
    @classmethod
    def initialize(cls) -> None:
        """Initialize Algolia search client"""
        if cls._client is not None:
            logger.info("Algolia already initialized")
            return
            
        try:
            app_id = os.getenv('ALGOLIA_APP_ID')
            api_key = os.getenv('ALGOLIA_API_KEY')
            
            if not app_id or not api_key:
                raise ValueError("Missing Algolia configuration environment variables")
            
            cls._client = SearchClient.create(app_id, api_key)
            
            # Initialize indices
            products_index = os.getenv('ALGOLIA_PRODUCTS_INDEX', 'products')
            categories_index = os.getenv('ALGOLIA_CATEGORIES_INDEX', 'categories')
            
            cls._indices['products'] = cls._client.init_index(products_index)
            cls._indices['categories'] = cls._client.init_index(categories_index)
            
            logger.info("Algolia client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Algolia: {str(e)}")
            raise
    
    @classmethod
    def get_client(cls) -> SearchClient:
        """Get Algolia search client"""
        if cls._client is None:
            cls.initialize()
        return cls._client
    
    @classmethod
    def get_index(cls, index_name: str) -> SearchIndex:
        """Get Algolia search index"""
        if cls._client is None:
            cls.initialize()
        
        if index_name not in cls._indices:
            cls._indices[index_name] = cls._client.init_index(index_name)
        
        return cls._indices[index_name]
    
    @classmethod
    def get_products_index(cls) -> SearchIndex:
        """Get products search index"""
        return cls.get_index('products')
    
    @classmethod
    def get_categories_index(cls) -> SearchIndex:
        """Get categories search index"""
        return cls.get_index('categories')
    
    @classmethod
    def index_product(cls, product_data: Dict[str, Any]) -> None:
        """Index a single product"""
        try:
            products_index = cls.get_products_index()
            
            # Prepare product data for indexing
            search_data = cls._prepare_product_for_search(product_data)
            
            products_index.save_object(search_data)
            logger.info(f"Product {product_data.get('id')} indexed successfully")
            
        except Exception as e:
            logger.error(f"Failed to index product {product_data.get('id')}: {str(e)}")
            raise
    
    @classmethod
    def index_products_batch(cls, products: List[Dict[str, Any]]) -> None:
        """Index multiple products in batch"""
        try:
            products_index = cls.get_products_index()
            
            # Prepare products data for indexing
            search_data = [cls._prepare_product_for_search(product) for product in products]
            
            products_index.save_objects(search_data)
            logger.info(f"Batch indexed {len(products)} products successfully")
            
        except Exception as e:
            logger.error(f"Failed to batch index products: {str(e)}")
            raise
    
    @classmethod
    def delete_product(cls, product_id: str) -> None:
        """Delete product from search index"""
        try:
            products_index = cls.get_products_index()
            products_index.delete_object(product_id)
            logger.info(f"Product {product_id} deleted from search index")
            
        except Exception as e:
            logger.error(f"Failed to delete product {product_id} from search: {str(e)}")
            raise
    
    @classmethod
    def search_products(cls, query: str, filters: Optional[Dict[str, Any]] = None,
                       page: int = 0, per_page: int = 20,
                       include_facets: bool = False,
                       include_highlights: bool = True,
                       sort_by: Optional[str] = None) -> Dict[str, Any]:
        """Enhanced product search with advanced features"""
        try:
            products_index = cls.get_products_index()

            search_params = {
                'page': page,
                'hitsPerPage': per_page,
                'attributesToRetrieve': [
                    'objectID', 'name', 'description', 'category',
                    'coverImageUrl', 'status', 'createdAt', 'price'
                ],
                'typoTolerance': True,
                'minWordSizefor1Typo': 3,
                'minWordSizefor2Typos': 7,
                'removeStopWords': True
            }

            # Add highlighting if requested
            if include_highlights:
                search_params.update({
                    'attributesToHighlight': ['name', 'description', 'category'],
                    'highlightPreTag': '<mark>',
                    'highlightPostTag': '</mark>'
                })

            # Add faceting if requested
            if include_facets:
                search_params['facets'] = ['category', 'status', 'price_range']

            # Add sorting if specified
            if sort_by:
                sort_mapping = {
                    'price_asc': 'price:asc',
                    'price_desc': 'price:desc',
                    'name_asc': 'name:asc',
                    'name_desc': 'name:desc',
                    'newest': 'createdAt:desc',
                    'oldest': 'createdAt:asc'
                }
                if sort_by in sort_mapping:
                    search_params['ranking'] = [sort_mapping[sort_by]]

            # Add filters if provided
            if filters:
                filter_strings = []

                if 'category' in filters and filters['category']:
                    filter_strings.append(f"category:'{filters['category']}'")

                if 'status' in filters and filters['status']:
                    filter_strings.append(f"status:'{filters['status']}'")

                if 'price_min' in filters and filters['price_min'] is not None:
                    filter_strings.append(f"price >= {filters['price_min']}")

                if 'price_max' in filters and filters['price_max'] is not None:
                    filter_strings.append(f"price <= {filters['price_max']}")

                if filter_strings:
                    search_params['filters'] = ' AND '.join(filter_strings)

            result = products_index.search(query, search_params)

            return {
                'hits': result['hits'],
                'total': result['nbHits'],
                'page': result['page'],
                'pages': result['nbPages'],
                'per_page': result['hitsPerPage'],
                'processing_time': result['processingTimeMS'],
                'facets': result.get('facets', {}) if include_facets else {}
            }

        except Exception as e:
            logger.error(f"Product search failed: {str(e)}")
            raise
    
    @classmethod
    def get_search_suggestions(cls, query: str, limit: int = 5) -> List[str]:
        """Get search suggestions"""
        try:
            products_index = cls.get_products_index()

            result = products_index.search(query, {
                'hitsPerPage': limit,
                'attributesToRetrieve': ['name'],
                'typoTolerance': True,
                'minWordSizefor1Typo': 3,
                'minWordSizefor2Typos': 7
            })

            suggestions = []
            for hit in result['hits']:
                if hit['name'] not in suggestions:
                    suggestions.append(hit['name'])

            return suggestions[:limit]

        except Exception as e:
            logger.error(f"Failed to get search suggestions: {str(e)}")
            return []

    @classmethod
    def get_autocomplete_suggestions(cls, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get autocomplete suggestions with more details"""
        try:
            products_index = cls.get_products_index()

            result = products_index.search(query, {
                'hitsPerPage': limit,
                'attributesToRetrieve': ['objectID', 'name', 'category', 'coverImageUrl'],
                'typoTolerance': True,
                'minWordSizefor1Typo': 3,
                'minWordSizefor2Typos': 7,
                'attributesToHighlight': ['name'],
                'highlightPreTag': '<mark>',
                'highlightPostTag': '</mark>'
            })

            suggestions = []
            for hit in result['hits']:
                suggestions.append({
                    'id': hit['objectID'],
                    'name': hit.get('name', ''),
                    'highlighted_name': hit.get('_highlightResult', {}).get('name', {}).get('value', hit.get('name', '')),
                    'category': hit.get('category', ''),
                    'image': hit.get('coverImageUrl', '')
                })

            return suggestions

        except Exception as e:
            logger.error(f"Failed to get autocomplete suggestions: {str(e)}")
            return []

    @classmethod
    def search_with_facets(cls, query: str, facet_filters: Optional[Dict[str, List[str]]] = None,
                          page: int = 0, per_page: int = 20) -> Dict[str, Any]:
        """Search with faceted filtering"""
        try:
            products_index = cls.get_products_index()

            search_params = {
                'page': page,
                'hitsPerPage': per_page,
                'facets': ['category', 'status', 'price_range'],
                'attributesToRetrieve': [
                    'objectID', 'name', 'description', 'category',
                    'coverImageUrl', 'status', 'createdAt', 'price'
                ],
                'attributesToHighlight': ['name', 'description'],
                'highlightPreTag': '<mark>',
                'highlightPostTag': '</mark>'
            }

            # Apply facet filters
            if facet_filters:
                filter_strings = []
                for facet, values in facet_filters.items():
                    if values:
                        facet_filter = ' OR '.join([f"{facet}:'{value}'" for value in values])
                        filter_strings.append(f"({facet_filter})")

                if filter_strings:
                    search_params['filters'] = ' AND '.join(filter_strings)

            result = products_index.search(query, search_params)

            return {
                'hits': result['hits'],
                'total': result['nbHits'],
                'page': result['page'],
                'pages': result['nbPages'],
                'per_page': result['hitsPerPage'],
                'processing_time': result['processingTimeMS'],
                'facets': result.get('facets', {})
            }

        except Exception as e:
            logger.error(f"Faceted search failed: {str(e)}")
            raise

    @classmethod
    def get_popular_searches(cls, limit: int = 10) -> List[Dict[str, Any]]:
        """Get popular search terms (placeholder - would need analytics integration)"""
        try:
            # This would typically come from search analytics
            # For now, return static popular searches
            return [
                {'query': 'electronics', 'count': 150},
                {'query': 'fashion', 'count': 120},
                {'query': 'books', 'count': 90},
                {'query': 'sports', 'count': 75},
                {'query': 'home decor', 'count': 60}
            ][:limit]

        except Exception as e:
            logger.error(f"Failed to get popular searches: {str(e)}")
            return []

    @classmethod
    def index_products_batch(cls, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Index multiple products in batch"""
        try:
            products_index = cls.get_products_index()

            # Prepare products for indexing
            algolia_products = []
            for product in products:
                algolia_product = cls._prepare_product_for_algolia(product)
                if algolia_product:
                    algolia_products.append(algolia_product)

            if algolia_products:
                # Batch save objects
                result = products_index.save_objects(algolia_products)

                logger.info(f"Batch indexed {len(algolia_products)} products")
                return {
                    'indexed': len(algolia_products),
                    'task_ids': result.get('taskIDs', [])
                }
            else:
                return {'indexed': 0, 'task_ids': []}

        except Exception as e:
            logger.error(f"Batch product indexing failed: {str(e)}")
            raise

    @classmethod
    def delete_products_batch(cls, product_ids: List[str]) -> Dict[str, Any]:
        """Delete multiple products from index in batch"""
        try:
            products_index = cls.get_products_index()

            if product_ids:
                result = products_index.delete_objects(product_ids)

                logger.info(f"Batch deleted {len(product_ids)} products from index")
                return {
                    'deleted': len(product_ids),
                    'task_ids': result.get('taskIDs', [])
                }
            else:
                return {'deleted': 0, 'task_ids': []}

        except Exception as e:
            logger.error(f"Batch product deletion failed: {str(e)}")
            raise

    @classmethod
    def clear_index(cls, index_name: str = 'products') -> Dict[str, Any]:
        """Clear all objects from an index"""
        try:
            if index_name == 'products':
                index = cls.get_products_index()
            else:
                raise ValueError(f"Unknown index: {index_name}")

            result = index.clear_objects()

            logger.info(f"Cleared {index_name} index")
            return {
                'cleared': True,
                'task_id': result.get('taskID')
            }

        except Exception as e:
            logger.error(f"Failed to clear {index_name} index: {str(e)}")
            raise

    @classmethod
    def get_index_settings(cls, index_name: str = 'products') -> Dict[str, Any]:
        """Get index settings"""
        try:
            if index_name == 'products':
                index = cls.get_products_index()
            else:
                raise ValueError(f"Unknown index: {index_name}")

            settings = index.get_settings()
            return settings

        except Exception as e:
            logger.error(f"Failed to get {index_name} index settings: {str(e)}")
            raise

    @classmethod
    def update_index_settings(cls, settings: Dict[str, Any], index_name: str = 'products') -> Dict[str, Any]:
        """Update index settings"""
        try:
            if index_name == 'products':
                index = cls.get_products_index()
            else:
                raise ValueError(f"Unknown index: {index_name}")

            result = index.set_settings(settings)

            logger.info(f"Updated {index_name} index settings")
            return {
                'updated': True,
                'task_id': result.get('taskID')
            }

        except Exception as e:
            logger.error(f"Failed to update {index_name} index settings: {str(e)}")
            raise

    @classmethod
    def get_index_stats(cls, index_name: str = 'products') -> Dict[str, Any]:
        """Get index statistics"""
        try:
            if index_name == 'products':
                index = cls.get_products_index()
            else:
                raise ValueError(f"Unknown index: {index_name}")

            # Get basic stats
            settings = index.get_settings()

            # This would typically include more detailed stats
            # For now, return basic information
            return {
                'index_name': index_name,
                'searchable_attributes': settings.get('searchableAttributes', []),
                'facets': settings.get('attributesForFaceting', []),
                'ranking': settings.get('ranking', [])
            }

        except Exception as e:
            logger.error(f"Failed to get {index_name} index stats: {str(e)}")
            raise
    
    @classmethod
    def _prepare_product_for_search(cls, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare product data for Algolia indexing"""
        return {
            'objectID': product_data.get('id'),
            'name': product_data.get('name', ''),
            'description': product_data.get('description', ''),
            'category': product_data.get('category', ''),
            'coverImageUrl': product_data.get('coverImageUrl', ''),
            'status': product_data.get('status', 'Active'),
            'createdAt': product_data.get('createdAt', 0),
            'updatedAt': product_data.get('updatedAt', 0),
            # Add searchable text for better matching
            '_searchable_text': f"{product_data.get('name', '')} {product_data.get('description', '')} {product_data.get('category', '')}".lower()
        }
    
    @classmethod
    def configure_index_settings(cls) -> None:
        """Configure Algolia index settings"""
        try:
            products_index = cls.get_products_index()
            
            settings = {
                'searchableAttributes': [
                    'name',
                    'description',
                    'category',
                    '_searchable_text'
                ],
                'attributesForFaceting': [
                    'category',
                    'status'
                ],
                'customRanking': [
                    'desc(createdAt)'
                ],
                'typoTolerance': True,
                'minWordSizefor1Typo': 3,
                'minWordSizefor2Typos': 7,
                'allowTyposOnNumericTokens': False,
                'ignorePlurals': True,
                'removeStopWords': True
            }
            
            products_index.set_settings(settings)
            logger.info("Algolia index settings configured successfully")
            
        except Exception as e:
            logger.error(f"Failed to configure Algolia index settings: {str(e)}")
            raise

# Initialize Algolia on module import (optional, can fail silently)
try:
    AlgoliaConfig.initialize()
    AlgoliaConfig.configure_index_settings()
except Exception as e:
    logger.warning(f"Algolia initialization failed on import: {str(e)}")
    # Don't raise here to allow for manual initialization
