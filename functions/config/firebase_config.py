"""
Firebase configuration and initialization
"""
import os
import json
import logging
from typing import Optional
import firebase_admin
from firebase_admin import credentials, firestore, auth
from google.cloud.firestore import Client

logger = logging.getLogger(__name__)

class FirebaseConfig:
    """Firebase configuration and client management"""
    
    _app: Optional[firebase_admin.App] = None
    _db: Optional[Client] = None
    
    @classmethod
    def initialize(cls) -> None:
        """Initialize Firebase Admin SDK"""
        if cls._app is not None:
            logger.info("Firebase already initialized")
            return
            
        try:
            # Check if running in Firebase Functions environment
            if os.getenv('FUNCTIONS_EMULATOR') or os.getenv('FIREBASE_CONFIG'):
                # Use default credentials in Firebase environment
                cls._app = firebase_admin.initialize_app()
                logger.info("Firebase initialized with default credentials")
            else:
                # Use service account credentials for local development
                project_id = os.getenv('FIREBASE_PROJECT_ID')
                private_key = os.getenv('FIREBASE_PRIVATE_KEY')
                client_email = os.getenv('FIREBASE_CLIENT_EMAIL')
                
                if not all([project_id, private_key, client_email]):
                    raise ValueError("Missing Firebase configuration environment variables")
                
                # Parse private key (handle escaped newlines)
                private_key = private_key.replace('\\n', '\n')
                
                cred_dict = {
                    "type": "service_account",
                    "project_id": project_id,
                    "private_key": private_key,
                    "client_email": client_email,
                    "client_id": "",
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                    "client_x509_cert_url": f"https://www.googleapis.com/robot/v1/metadata/x509/{client_email}"
                }
                
                cred = credentials.Certificate(cred_dict)
                cls._app = firebase_admin.initialize_app(cred)
                logger.info("Firebase initialized with service account credentials")
                
            cls._db = firestore.client()
            logger.info("Firestore client initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Firebase: {str(e)}")
            raise
    
    @classmethod
    def get_db(cls) -> Client:
        """Get Firestore database client"""
        if cls._db is None:
            cls.initialize()
        return cls._db
    
    @classmethod
    def get_app(cls) -> firebase_admin.App:
        """Get Firebase app instance"""
        if cls._app is None:
            cls.initialize()
        return cls._app
    
    @classmethod
    def verify_id_token(cls, id_token: str) -> dict:
        """Verify Firebase ID token"""
        try:
            decoded_token = auth.verify_id_token(id_token)
            return decoded_token
        except Exception as e:
            logger.error(f"Token verification failed: {str(e)}")
            raise ValueError("Invalid authentication token")
    
    @classmethod
    def get_user(cls, uid: str) -> auth.UserRecord:
        """Get user by UID"""
        try:
            return auth.get_user(uid)
        except Exception as e:
            logger.error(f"Failed to get user {uid}: {str(e)}")
            raise ValueError("User not found")
    
    @classmethod
    def set_custom_claims(cls, uid: str, claims: dict) -> None:
        """Set custom claims for user"""
        try:
            auth.set_custom_user_claims(uid, claims)
            logger.info(f"Custom claims set for user {uid}")
        except Exception as e:
            logger.error(f"Failed to set custom claims for user {uid}: {str(e)}")
            raise
    
    @classmethod
    def is_admin(cls, decoded_token: dict) -> bool:
        """Check if user has admin privileges"""
        return decoded_token.get('admin', False) or decoded_token.get('isAdmin', False)
    
    @classmethod
    def is_super_admin(cls, decoded_token: dict) -> bool:
        """Check if user has super admin privileges"""
        email = decoded_token.get('email', '')
        super_admin_emails = os.getenv('SUPER_ADMIN_EMAILS', '').split(',')
        return (
            cls.is_admin(decoded_token) and 
            email in super_admin_emails
        )

# Collections constants
class Collections:
    """Firestore collection names"""
    USERS = 'users'
    PRODUCTS = 'products'
    PRODUCT_VARIANTS = 'productVariants'
    SOURCE_LINKS = 'sourceLinks'
    ORDERS = 'orders'
    ORDER_ITEMS = 'orderItems'
    ORDER_ISSUES = 'orderIssues'
    GROUP_BUYS = 'groupBuys'
    CATEGORIES = 'categories'
    SHIPPING_ADDRESSES = 'shippingAddresses'

# Initialize Firebase on module import
try:
    FirebaseConfig.initialize()
except Exception as e:
    logger.warning(f"Firebase initialization failed on import: {str(e)}")
    # Don't raise here to allow for manual initialization
