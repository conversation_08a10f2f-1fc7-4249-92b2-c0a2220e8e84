# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----<PERSON><PERSON> PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Algolia Search Configuration
ALGOLIA_APP_ID=your-algolia-app-id
ALGOLIA_API_KEY=your-algolia-admin-api-key
ALGOLIA_SEARCH_KEY=your-algolia-search-only-key
ALGOLIA_PRODUCTS_INDEX=products
ALGOLIA_CATEGORIES_INDEX=categories

# Stripe Payment Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,https://admin.maomao.com,https://maomao.com
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# Rate Limiting Configuration
REDIS_URL=redis://localhost:6379
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST_SIZE=10

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# API Configuration
API_VERSION=v1
API_BASE_PATH=/api

# Notification Configuration
FCM_SERVER_KEY=your-fcm-server-key

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
SUPER_ADMIN_EMAILS=<EMAIL>,<EMAIL>

# External Services
SHIPPING_API_KEY=your-shipping-api-key
CURRENCY_API_KEY=your-currency-api-key

# Security
JWT_SECRET=your-jwt-secret-for-additional-tokens
ENCRYPTION_KEY=your-encryption-key-for-sensitive-data

# Monitoring
SENTRY_DSN=your-sentry-dsn-for-error-tracking
ANALYTICS_API_KEY=your-analytics-api-key
