"""
Order service - Business logic for order operations
"""
import logging
import time
import uuid
from typing import Dict, List, Any, Optional
from google.cloud.firestore import Query
from google.cloud import firestore
from config.firebase_config import FirebaseConfig, Collections

logger = logging.getLogger(__name__)

class OrderService:
    """Service class for order operations"""
    
    def __init__(self):
        self.db = FirebaseConfig.get_db()
    
    def create_order(self, user_id: str, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new order"""
        try:
            # Validate order items
            items = order_data['items']
            if not items:
                raise ValueError("Order must contain at least one item")
            
            # Calculate total amount
            total_amount = sum(item['quantity'] * item['pricePerUnit'] for item in items)
            
            # Generate order number
            order_number = self._generate_order_number()
            
            # Prepare order document
            current_time = int(time.time() * 1000)
            order_doc = {
                'userId': user_id,
                'orderNumber': order_number,
                'status': 'Pending Payment',
                'totalAmount': total_amount,
                'currency': order_data.get('currency', 'USD'),
                'shippingAddress': order_data['shippingAddress'],
                'notes': order_data.get('notes', ''),
                'createdAt': current_time,
                'updatedAt': current_time,
                'paymentStatus': 'Pending',
                'paymentMethod': 'Manual Transfer'
            }
            
            # Use transaction to ensure consistency
            @firestore.transactional
            def create_order_transaction(transaction):
                # Create order document
                order_ref = self.db.collection(Collections.ORDERS).document()
                transaction.set(order_ref, order_doc)
                
                # Create order items as subcollection
                for item in items:
                    item_doc = {
                        'productVariantId': item['productVariantId'],
                        'quantity': item['quantity'],
                        'pricePerUnitPaid': item['pricePerUnit'],
                        'fulfillmentStatus': 'Pending Purchase',
                        'isGroupBuy': item.get('isGroupBuy', False),
                        'groupBuyId': item.get('groupBuyId'),
                        'createdAt': current_time,
                        'updatedAt': current_time
                    }
                    
                    # Get product snapshot for order item
                    product_snapshot = self._get_product_snapshot(item['productVariantId'])
                    if product_snapshot:
                        item_doc['productSnapshot'] = product_snapshot
                    
                    item_ref = order_ref.collection(Collections.ORDER_ITEMS).document()
                    transaction.set(item_ref, item_doc)
                
                return order_ref.id
            
            # Execute transaction
            transaction = self.db.transaction()
            order_id = create_order_transaction(transaction)
            
            # Get created order with items
            created_order = self.get_order_details(order_id, user_id)
            
            logger.info(f"Order created: {order_id} for user: {user_id}")
            return created_order
            
        except Exception as e:
            logger.error(f"Error creating order for user {user_id}: {str(e)}")
            raise
    
    def get_user_orders(self, 
                       user_id: str,
                       status: Optional[str] = None,
                       date_from: Optional[int] = None,
                       date_to: Optional[int] = None,
                       page: int = 1,
                       per_page: int = 20,
                       sort_by: str = 'createdAt',
                       sort_order: str = 'desc') -> Dict[str, Any]:
        """Get user's orders with filtering and pagination"""
        try:
            # Build query
            query = self.db.collection(Collections.ORDERS)
            query = query.where('userId', '==', user_id)
            
            # Apply filters
            if status:
                query = query.where('status', '==', status)
            
            if date_from:
                query = query.where('createdAt', '>=', date_from)
            
            if date_to:
                query = query.where('createdAt', '<=', date_to)
            
            # Apply sorting
            sort_direction = Query.DESCENDING if sort_order == 'desc' else Query.ASCENDING
            query = query.order_by(sort_by, direction=sort_direction)
            
            # Apply pagination
            offset = (page - 1) * per_page
            query = query.offset(offset).limit(per_page)
            
            # Execute query
            docs = query.stream()
            orders = []
            
            for doc in docs:
                order_data = doc.to_dict()
                order_data['id'] = doc.id
                orders.append(order_data)
            
            # Get total count (simplified for performance)
            total_query = self.db.collection(Collections.ORDERS)
            total_query = total_query.where('userId', '==', user_id)
            if status:
                total_query = total_query.where('status', '==', status)
            
            total_docs = total_query.stream()
            total_count = sum(1 for _ in total_docs)
            
            return {
                'orders': orders,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'pages': (total_count + per_page - 1) // per_page
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting orders for user {user_id}: {str(e)}")
            raise
    
    def get_order_details(self, order_id: str, user_id: str = None) -> Optional[Dict[str, Any]]:
        """Get order details with items"""
        try:
            # Get order document
            order_doc = self.db.collection(Collections.ORDERS).document(order_id).get()
            
            if not order_doc.exists:
                return None
            
            order_data = order_doc.to_dict()
            order_data['id'] = order_id
            
            # Check user access (if user_id provided)
            if user_id and order_data.get('userId') != user_id:
                return None
            
            # Get order items
            items_query = self.db.collection(Collections.ORDERS).document(order_id).collection(Collections.ORDER_ITEMS)
            items_docs = items_query.stream()
            
            items = []
            for item_doc in items_docs:
                item_data = item_doc.to_dict()
                item_data['id'] = item_doc.id
                items.append(item_data)
            
            order_data['items'] = items
            
            # Get order issues if any
            issues_query = self.db.collection(Collections.ORDER_ISSUES)
            issues_query = issues_query.where('orderId', '==', order_id)
            issues_docs = issues_query.stream()
            
            issues = []
            for issue_doc in issues_docs:
                issue_data = issue_doc.to_dict()
                issue_data['id'] = issue_doc.id
                issues.append(issue_data)
            
            order_data['issues'] = issues
            
            return order_data
            
        except Exception as e:
            logger.error(f"Error getting order details for order {order_id}: {str(e)}")
            raise
    
    def get_action_required_orders(self, user_id: str) -> List[Dict[str, Any]]:
        """Get orders requiring user action"""
        try:
            query = self.db.collection(Collections.ORDERS)
            query = query.where('userId', '==', user_id)
            query = query.where('status', '==', 'Action Required')
            query = query.order_by('updatedAt', direction=Query.DESCENDING)
            
            docs = query.stream()
            orders = []
            
            for doc in docs:
                order_data = doc.to_dict()
                order_data['id'] = doc.id
                
                # Get associated issues
                issues_query = self.db.collection(Collections.ORDER_ISSUES)
                issues_query = issues_query.where('orderId', '==', doc.id)
                issues_query = issues_query.where('status', '==', 'AwaitingCustomerResponse')
                issues_docs = issues_query.stream()
                
                issues = []
                for issue_doc in issues_docs:
                    issue_data = issue_doc.to_dict()
                    issue_data['id'] = issue_doc.id
                    issues.append(issue_data)
                
                order_data['pendingIssues'] = issues
                orders.append(order_data)
            
            return orders
            
        except Exception as e:
            logger.error(f"Error getting action required orders for user {user_id}: {str(e)}")
            raise
    
    def respond_to_order_issue(self, 
                              issue_id: str, 
                              order_id: str, 
                              user_id: str, 
                              response: str, 
                              notes: str = '') -> Optional[Dict[str, Any]]:
        """Respond to order issue"""
        try:
            # Get issue document
            issue_doc = self.db.collection(Collections.ORDER_ISSUES).document(issue_id).get()
            
            if not issue_doc.exists:
                return None
            
            issue_data = issue_doc.to_dict()
            
            # Verify issue belongs to the order and user
            if issue_data.get('orderId') != order_id:
                return None
            
            # Verify order belongs to user
            order_doc = self.db.collection(Collections.ORDERS).document(order_id).get()
            if not order_doc.exists or order_doc.to_dict().get('userId') != user_id:
                return None
            
            # Map response to status
            status_mapping = {
                'AcceptPriceIncrease': 'Resolved-PriceAccepted',
                'ChooseAlternative': 'Resolved-AlternativeAccepted',
                'CancelItem': 'Resolved-Cancelled',
                'ContactSupport': 'AwaitingAdminAction'
            }
            
            new_status = status_mapping.get(response)
            if not new_status:
                raise ValueError(f"Invalid response: {response}")
            
            # Update issue
            current_time = int(time.time() * 1000)
            update_data = {
                'status': new_status,
                'customerResponse': response,
                'customerNotes': notes,
                'respondedAt': current_time,
                'updatedAt': current_time
            }
            
            self.db.collection(Collections.ORDER_ISSUES).document(issue_id).update(update_data)
            
            # Check if all issues for this order are resolved
            self._check_and_update_order_status(order_id)
            
            # Get updated issue
            updated_issue = self.db.collection(Collections.ORDER_ISSUES).document(issue_id).get().to_dict()
            updated_issue['id'] = issue_id
            
            logger.info(f"Order issue {issue_id} responded to by user {user_id}")
            return updated_issue
            
        except Exception as e:
            logger.error(f"Error responding to order issue {issue_id}: {str(e)}")
            raise

    def get_payment_info(self, order_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Get payment information for manual payment"""
        try:
            # Get order details
            order_data = self.get_order_details(order_id, user_id)

            if not order_data or order_data.get('status') != 'Pending Payment':
                return None

            # Return payment information for manual transfer
            payment_info = {
                'orderId': order_id,
                'orderNumber': order_data.get('orderNumber'),
                'totalAmount': order_data.get('totalAmount'),
                'currency': order_data.get('currency', 'USD'),
                'paymentMethods': [
                    {
                        'type': 'bank_transfer',
                        'name': 'Bank Transfer',
                        'details': {
                            'bankName': 'Your Bank Name',
                            'accountName': 'Maomao E-commerce',
                            'accountNumber': '**********',
                            'routingNumber': '*********',
                            'reference': order_data.get('orderNumber')
                        }
                    },
                    {
                        'type': 'mobile_money',
                        'name': 'Mobile Money',
                        'details': {
                            'provider': 'MTN Mobile Money',
                            'phoneNumber': '+************',
                            'accountName': 'Maomao E-commerce',
                            'reference': order_data.get('orderNumber')
                        }
                    }
                ],
                'instructions': [
                    'Make payment using one of the methods above',
                    'Use the order number as payment reference',
                    'Submit payment confirmation after making payment',
                    'Payment will be verified within 24 hours'
                ]
            }

            return payment_info

        except Exception as e:
            logger.error(f"Error getting payment info for order {order_id}: {str(e)}")
            raise

    def submit_payment_confirmation(self, order_id: str, user_id: str, payment_reference: str = '') -> Optional[Dict[str, Any]]:
        """Submit payment confirmation"""
        try:
            # Get order details
            order_data = self.get_order_details(order_id, user_id)

            if not order_data or order_data.get('status') != 'Pending Payment':
                return None

            # Update order status to indicate payment confirmation submitted
            current_time = int(time.time() * 1000)
            update_data = {
                'status': 'Payment Confirmation Submitted',
                'paymentStatus': 'Confirmation Submitted',
                'paymentReference': payment_reference,
                'paymentConfirmedAt': current_time,
                'updatedAt': current_time
            }

            self.db.collection(Collections.ORDERS).document(order_id).update(update_data)

            # Get updated order
            updated_order = self.get_order_details(order_id, user_id)

            logger.info(f"Payment confirmation submitted for order {order_id}")
            return updated_order

        except Exception as e:
            logger.error(f"Error submitting payment confirmation for order {order_id}: {str(e)}")
            raise

    # Admin methods
    def get_all_orders(self,
                      status: Optional[str] = None,
                      date_from: Optional[int] = None,
                      date_to: Optional[int] = None,
                      page: int = 1,
                      per_page: int = 20,
                      sort_by: str = 'createdAt',
                      sort_order: str = 'desc') -> Dict[str, Any]:
        """Get all orders (Admin only)"""
        try:
            # Build query
            query = self.db.collection(Collections.ORDERS)

            # Apply filters
            if status:
                query = query.where('status', '==', status)

            if date_from:
                query = query.where('createdAt', '>=', date_from)

            if date_to:
                query = query.where('createdAt', '<=', date_to)

            # Apply sorting
            sort_direction = Query.DESCENDING if sort_order == 'desc' else Query.ASCENDING
            query = query.order_by(sort_by, direction=sort_direction)

            # Apply pagination
            offset = (page - 1) * per_page
            query = query.offset(offset).limit(per_page)

            # Execute query
            docs = query.stream()
            orders = []

            for doc in docs:
                order_data = doc.to_dict()
                order_data['id'] = doc.id
                orders.append(order_data)

            # Get total count (simplified for performance)
            total_query = self.db.collection(Collections.ORDERS)
            if status:
                total_query = total_query.where('status', '==', status)

            total_docs = total_query.stream()
            total_count = sum(1 for _ in total_docs)

            return {
                'orders': orders,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'pages': (total_count + per_page - 1) // per_page
                }
            }

        except Exception as e:
            logger.error(f"Error getting all orders: {str(e)}")
            raise

    def update_order_status(self, order_id: str, new_status: str, admin_id: str, notes: str = '') -> Optional[Dict[str, Any]]:
        """Update order status (Admin only)"""
        try:
            # Get order document
            order_doc = self.db.collection(Collections.ORDERS).document(order_id).get()

            if not order_doc.exists:
                return None

            # Update order status
            current_time = int(time.time() * 1000)
            update_data = {
                'status': new_status,
                'updatedAt': current_time,
                'lastUpdatedBy': admin_id
            }

            if notes:
                update_data['adminNotes'] = notes

            # Special handling for payment verification
            if new_status == 'Processing' and order_doc.to_dict().get('status') == 'Payment Confirmation Submitted':
                update_data['paymentStatus'] = 'Verified'
                update_data['paymentVerifiedAt'] = current_time
                update_data['paymentVerifiedBy'] = admin_id

            self.db.collection(Collections.ORDERS).document(order_id).update(update_data)

            # Get updated order
            updated_order = self.get_order_details(order_id)

            logger.info(f"Order {order_id} status updated to {new_status} by admin {admin_id}")
            return updated_order

        except Exception as e:
            logger.error(f"Error updating order status for order {order_id}: {str(e)}")
            raise

    def create_order_issue(self,
                          order_id: str,
                          order_item_id: str,
                          issue_type: str,
                          details: str,
                          proposed_solution: str,
                          admin_id: str) -> Optional[Dict[str, Any]]:
        """Create order issue (Admin only)"""
        try:
            # Verify order and order item exist
            order_doc = self.db.collection(Collections.ORDERS).document(order_id).get()
            if not order_doc.exists:
                return None

            item_doc = self.db.collection(Collections.ORDERS).document(order_id).collection(Collections.ORDER_ITEMS).document(order_item_id).get()
            if not item_doc.exists:
                return None

            # Create issue document
            current_time = int(time.time() * 1000)
            issue_data = {
                'orderId': order_id,
                'orderItemId': order_item_id,
                'issueType': issue_type,
                'details': details,
                'proposedSolution': proposed_solution,
                'status': 'AwaitingCustomerResponse',
                'createdBy': admin_id,
                'createdAt': current_time,
                'updatedAt': current_time
            }

            # Create issue
            issue_ref = self.db.collection(Collections.ORDER_ISSUES).document()
            issue_ref.set(issue_data)

            # Update order status to "Action Required"
            self.db.collection(Collections.ORDERS).document(order_id).update({
                'status': 'Action Required',
                'updatedAt': current_time,
                'lastUpdatedBy': admin_id
            })

            # Get created issue
            created_issue = issue_ref.get().to_dict()
            created_issue['id'] = issue_ref.id

            logger.info(f"Order issue created for order {order_id} by admin {admin_id}")
            return created_issue

        except Exception as e:
            logger.error(f"Error creating order issue for order {order_id}: {str(e)}")
            raise

    def verify_payment(self, order_id: str, admin_id: str, verification_notes: str = '') -> Optional[Dict[str, Any]]:
        """Verify manual payment (Admin only)"""
        try:
            # Get order document
            order_doc = self.db.collection(Collections.ORDERS).document(order_id).get()

            if not order_doc.exists:
                return None

            order_data = order_doc.to_dict()

            # Check if payment verification is allowed
            if order_data.get('status') != 'Payment Confirmation Submitted':
                raise ValueError("Payment verification not allowed for this order status")

            # Update order with payment verification
            current_time = int(time.time() * 1000)
            update_data = {
                'status': 'Processing',
                'paymentStatus': 'Verified',
                'paymentVerifiedAt': current_time,
                'paymentVerifiedBy': admin_id,
                'paymentVerificationNotes': verification_notes,
                'updatedAt': current_time,
                'lastUpdatedBy': admin_id
            }

            self.db.collection(Collections.ORDERS).document(order_id).update(update_data)

            # Get updated order
            updated_order = self.get_order_details(order_id)

            logger.info(f"Payment verified for order {order_id} by admin {admin_id}")
            return updated_order

        except Exception as e:
            logger.error(f"Error verifying payment for order {order_id}: {str(e)}")
            raise

    def get_pending_payment_orders(self) -> List[Dict[str, Any]]:
        """Get orders pending payment verification (Admin only)"""
        try:
            query = self.db.collection(Collections.ORDERS)
            query = query.where('status', '==', 'Payment Confirmation Submitted')
            query = query.order_by('paymentConfirmedAt', direction=Query.ASCENDING)

            docs = query.stream()
            orders = []

            for doc in docs:
                order_data = doc.to_dict()
                order_data['id'] = doc.id
                orders.append(order_data)

            return orders

        except Exception as e:
            logger.error(f"Error getting pending payment orders: {str(e)}")
            raise

    # Helper methods
    def _generate_order_number(self) -> str:
        """Generate unique order number"""
        timestamp = int(time.time())
        random_suffix = str(uuid.uuid4())[:8].upper()
        return f"CB-{timestamp}-{random_suffix}"

    def _get_product_snapshot(self, product_variant_id: str) -> Optional[Dict[str, Any]]:
        """Get product snapshot for order item"""
        try:
            # Get product variant
            variant_doc = self.db.collection(Collections.PRODUCT_VARIANTS).document(product_variant_id).get()

            if not variant_doc.exists:
                return None

            variant_data = variant_doc.to_dict()

            # Get product details
            product_id = variant_data.get('productId')
            if product_id:
                product_doc = self.db.collection(Collections.PRODUCTS).document(product_id).get()
                if product_doc.exists:
                    product_data = product_doc.to_dict()

                    return {
                        'name': product_data.get('name', ''),
                        'sku': variant_data.get('sku', ''),
                        'image': product_data.get('coverImageUrl', ''),
                        'attributes': variant_data.get('attributes', {})
                    }

            return None

        except Exception as e:
            logger.warning(f"Error getting product snapshot for variant {product_variant_id}: {str(e)}")
            return None

    def _check_and_update_order_status(self, order_id: str) -> None:
        """Check if all issues are resolved and update order status"""
        try:
            # Get all issues for this order
            issues_query = self.db.collection(Collections.ORDER_ISSUES)
            issues_query = issues_query.where('orderId', '==', order_id)
            issues_docs = issues_query.stream()

            pending_issues = []
            for issue_doc in issues_docs:
                issue_data = issue_doc.to_dict()
                if issue_data.get('status') == 'AwaitingCustomerResponse':
                    pending_issues.append(issue_data)

            # If no pending issues, update order status back to Processing
            if not pending_issues:
                current_time = int(time.time() * 1000)
                self.db.collection(Collections.ORDERS).document(order_id).update({
                    'status': 'Processing',
                    'updatedAt': current_time
                })

                logger.info(f"Order {order_id} status updated to Processing - all issues resolved")

        except Exception as e:
            logger.error(f"Error checking order status for order {order_id}: {str(e)}")

    def get_order_statistics(self) -> Dict[str, Any]:
        """Get order statistics for admin dashboard"""
        try:
            # Get orders from last 30 days
            thirty_days_ago = int((time.time() - 30 * 24 * 60 * 60) * 1000)

            # Total orders
            total_orders_query = self.db.collection(Collections.ORDERS)
            total_orders = sum(1 for _ in total_orders_query.stream())

            # Recent orders
            recent_orders_query = self.db.collection(Collections.ORDERS)
            recent_orders_query = recent_orders_query.where('createdAt', '>=', thirty_days_ago)
            recent_orders = sum(1 for _ in recent_orders_query.stream())

            # Orders by status
            status_counts = {}
            all_orders_query = self.db.collection(Collections.ORDERS)
            for doc in all_orders_query.stream():
                order_data = doc.to_dict()
                status = order_data.get('status', 'Unknown')
                status_counts[status] = status_counts.get(status, 0) + 1

            # Pending payment orders
            pending_payment = status_counts.get('Payment Confirmation Submitted', 0)

            # Action required orders
            action_required = status_counts.get('Action Required', 0)

            return {
                'totalOrders': total_orders,
                'recentOrders': recent_orders,
                'pendingPayment': pending_payment,
                'actionRequired': action_required,
                'statusBreakdown': status_counts
            }

        except Exception as e:
            logger.error(f"Error getting order statistics: {str(e)}")
            raise
