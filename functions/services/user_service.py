"""
User service - Business logic for user operations
"""
import logging
import time
from typing import Dict, List, Any, Optional
from google.cloud.firestore import Query
from google.cloud import firestore
from config.firebase_config import FirebaseConfig, Collections

logger = logging.getLogger(__name__)

class UserService:
    """Service class for user operations"""
    
    def __init__(self):
        self.db = FirebaseConfig.get_db()
    
    def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user profile"""
        try:
            doc = self.db.collection(Collections.USERS).document(user_id).get()
            
            if not doc.exists:
                return None
            
            user_data = doc.to_dict()
            user_data['id'] = doc.id
            
            # Remove sensitive fields
            user_data.pop('isAdmin', None)
            
            return user_data
            
        except Exception as e:
            logger.error(f"Error getting user profile for user {user_id}: {str(e)}")
            raise
    
    def update_user_profile(self, user_id: str, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update user profile"""
        try:
            doc_ref = self.db.collection(Collections.USERS).document(user_id)
            doc = doc_ref.get()
            
            if not doc.exists:
                return None
            
            # Add update timestamp
            update_data['updatedAt'] = int(time.time() * 1000)
            
            # Update document
            doc_ref.update(update_data)
            
            # Get updated profile
            updated_profile = self.get_user_profile(user_id)
            
            logger.info(f"User profile updated: {user_id}")
            return updated_profile
            
        except Exception as e:
            logger.error(f"Error updating user profile for user {user_id}: {str(e)}")
            raise
    
    def initialize_user_profile(self, user_id: str, email: str) -> Dict[str, Any]:
        """Initialize user profile after authentication"""
        try:
            # Check if profile already exists
            existing_profile = self.get_user_profile(user_id)
            if existing_profile:
                return existing_profile
            
            # Create new user profile
            current_time = int(time.time() * 1000)
            user_data = {
                'uid': user_id,
                'email': email,
                'displayName': '',
                'photoURL': '',
                'phoneNumber': '',
                'isAdmin': False,
                'createdAt': current_time,
                'updatedAt': current_time
            }
            
            # Create document
            self.db.collection(Collections.USERS).document(user_id).set(user_data)
            
            # Get created profile
            created_profile = self.get_user_profile(user_id)
            
            logger.info(f"User profile initialized: {user_id}")
            return created_profile
            
        except Exception as e:
            logger.error(f"Error initializing user profile for user {user_id}: {str(e)}")
            raise
    
    def get_shipping_addresses(self, user_id: str) -> List[Dict[str, Any]]:
        """Get user's shipping addresses"""
        try:
            query = self.db.collection(Collections.USERS).document(user_id).collection(Collections.SHIPPING_ADDRESSES)
            query = query.order_by('isDefault', direction=Query.DESCENDING)
            query = query.order_by('createdAt', direction=Query.DESCENDING)
            
            docs = query.stream()
            addresses = []
            
            for doc in docs:
                address_data = doc.to_dict()
                address_data['id'] = doc.id
                addresses.append(address_data)
            
            return addresses
            
        except Exception as e:
            logger.error(f"Error getting shipping addresses for user {user_id}: {str(e)}")
            raise
    
    def add_shipping_address(self, user_id: str, address_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add new shipping address"""
        try:
            # Check if this should be the default address
            existing_addresses = self.get_shipping_addresses(user_id)
            is_first_address = len(existing_addresses) == 0
            
            if is_first_address or address_data.get('isDefault', False):
                address_data['isDefault'] = True
                # If setting as default, unset other default addresses
                if not is_first_address:
                    self._unset_default_addresses(user_id)
            else:
                address_data['isDefault'] = False
            
            # Add timestamps
            current_time = int(time.time() * 1000)
            address_data['createdAt'] = current_time
            address_data['updatedAt'] = current_time
            
            # Create document
            doc_ref = self.db.collection(Collections.USERS).document(user_id).collection(Collections.SHIPPING_ADDRESSES).document()
            doc_ref.set(address_data)
            
            # Get created address
            created_address = doc_ref.get().to_dict()
            created_address['id'] = doc_ref.id
            
            logger.info(f"Shipping address added for user {user_id}")
            return created_address
            
        except Exception as e:
            logger.error(f"Error adding shipping address for user {user_id}: {str(e)}")
            raise
    
    def update_shipping_address(self, user_id: str, address_id: str, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update shipping address"""
        try:
            doc_ref = self.db.collection(Collections.USERS).document(user_id).collection(Collections.SHIPPING_ADDRESSES).document(address_id)
            doc = doc_ref.get()
            
            if not doc.exists:
                return None
            
            # Handle default address change
            if update_data.get('isDefault', False):
                self._unset_default_addresses(user_id)
            
            # Add update timestamp
            update_data['updatedAt'] = int(time.time() * 1000)
            
            # Update document
            doc_ref.update(update_data)
            
            # Get updated address
            updated_address = doc_ref.get().to_dict()
            updated_address['id'] = address_id
            
            logger.info(f"Shipping address {address_id} updated for user {user_id}")
            return updated_address
            
        except Exception as e:
            logger.error(f"Error updating shipping address {address_id} for user {user_id}: {str(e)}")
            raise
    
    def delete_shipping_address(self, user_id: str, address_id: str) -> bool:
        """Delete shipping address"""
        try:
            doc_ref = self.db.collection(Collections.USERS).document(user_id).collection(Collections.SHIPPING_ADDRESSES).document(address_id)
            doc = doc_ref.get()
            
            if not doc.exists:
                return False
            
            address_data = doc.to_dict()
            was_default = address_data.get('isDefault', False)
            
            # Delete document
            doc_ref.delete()
            
            # If deleted address was default, set another address as default
            if was_default:
                self._set_first_address_as_default(user_id)
            
            logger.info(f"Shipping address {address_id} deleted for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting shipping address {address_id} for user {user_id}: {str(e)}")
            raise
    
    def set_default_address(self, user_id: str, address_id: str) -> Optional[Dict[str, Any]]:
        """Set default shipping address"""
        try:
            doc_ref = self.db.collection(Collections.USERS).document(user_id).collection(Collections.SHIPPING_ADDRESSES).document(address_id)
            doc = doc_ref.get()
            
            if not doc.exists:
                return None
            
            # Use transaction to ensure consistency
            @firestore.transactional
            def set_default_transaction(transaction):
                # Unset all default addresses
                addresses_query = self.db.collection(Collections.USERS).document(user_id).collection(Collections.SHIPPING_ADDRESSES)
                addresses_docs = addresses_query.stream()
                
                for addr_doc in addresses_docs:
                    transaction.update(addr_doc.reference, {'isDefault': False})
                
                # Set the specified address as default
                transaction.update(doc_ref, {
                    'isDefault': True,
                    'updatedAt': int(time.time() * 1000)
                })
            
            # Execute transaction
            transaction = self.db.transaction()
            set_default_transaction(transaction)
            
            # Get updated address
            updated_address = doc_ref.get().to_dict()
            updated_address['id'] = address_id
            
            logger.info(f"Default address set to {address_id} for user {user_id}")
            return updated_address
            
        except Exception as e:
            logger.error(f"Error setting default address {address_id} for user {user_id}: {str(e)}")
            raise
    
    def get_default_address(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user's default shipping address"""
        try:
            query = self.db.collection(Collections.USERS).document(user_id).collection(Collections.SHIPPING_ADDRESSES)
            query = query.where('isDefault', '==', True)
            query = query.limit(1)
            
            docs = list(query.stream())
            
            if not docs:
                return None
            
            address_data = docs[0].to_dict()
            address_data['id'] = docs[0].id
            
            return address_data
            
        except Exception as e:
            logger.error(f"Error getting default address for user {user_id}: {str(e)}")
            raise
    
    # Helper methods
    def _unset_default_addresses(self, user_id: str) -> None:
        """Unset all default addresses for user"""
        try:
            query = self.db.collection(Collections.USERS).document(user_id).collection(Collections.SHIPPING_ADDRESSES)
            query = query.where('isDefault', '==', True)
            
            docs = query.stream()
            batch = self.db.batch()
            
            for doc in docs:
                batch.update(doc.reference, {'isDefault': False})
            
            batch.commit()
            
        except Exception as e:
            logger.error(f"Error unsetting default addresses for user {user_id}: {str(e)}")
    
    def _set_first_address_as_default(self, user_id: str) -> None:
        """Set first available address as default"""
        try:
            query = self.db.collection(Collections.USERS).document(user_id).collection(Collections.SHIPPING_ADDRESSES)
            query = query.limit(1)
            
            docs = list(query.stream())
            
            if docs:
                docs[0].reference.update({
                    'isDefault': True,
                    'updatedAt': int(time.time() * 1000)
                })
                
        except Exception as e:
            logger.error(f"Error setting first address as default for user {user_id}: {str(e)}")
