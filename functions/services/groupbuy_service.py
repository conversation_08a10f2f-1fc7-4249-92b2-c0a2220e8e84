"""
Group Buy service - Business logic for group buy operations
"""
import logging
import time
from typing import Dict, List, Any, Optional
from google.cloud.firestore import Query
from google.cloud import firestore
from config.firebase_config import FirebaseConfig, Collections

logger = logging.getLogger(__name__)

class GroupBuyService:
    """Service class for group buy operations"""
    
    def __init__(self):
        self.db = FirebaseConfig.get_db()
    
    def get_groupbuys(self, 
                     status: str = 'Active',
                     product_variant_id: Optional[str] = None,
                     category: Optional[str] = None,
                     expiring_soon: bool = False,
                     page: int = 1,
                     per_page: int = 20,
                     sort_by: str = 'createdAt',
                     sort_order: str = 'desc') -> Dict[str, Any]:
        """Get group buys with filtering and pagination"""
        try:
            # Build query
            query = self.db.collection(Collections.GROUP_BUYS)
            
            # Apply filters
            if status:
                query = query.where('status', '==', status)
            
            if product_variant_id:
                query = query.where('productVariantId', '==', product_variant_id)
            
            # Handle expiring soon filter
            if expiring_soon:
                # Group buys expiring within 24 hours
                twenty_four_hours = 24 * 60 * 60 * 1000
                expiry_threshold = int(time.time() * 1000) + twenty_four_hours
                query = query.where('expiresAt', '<=', expiry_threshold)
            
            # Apply sorting
            sort_direction = Query.DESCENDING if sort_order == 'desc' else Query.ASCENDING
            query = query.order_by(sort_by, direction=sort_direction)
            
            # Apply pagination
            offset = (page - 1) * per_page
            query = query.offset(offset).limit(per_page)
            
            # Execute query
            docs = query.stream()
            groupbuys = []
            
            for doc in docs:
                groupbuy_data = doc.to_dict()
                groupbuy_data['id'] = doc.id
                
                # Add calculated fields
                groupbuy_data = self._add_calculated_fields(groupbuy_data)
                
                # Filter by category if specified (requires product lookup)
                if category:
                    product_category = self._get_product_category(groupbuy_data.get('productVariantId'))
                    if product_category != category:
                        continue
                
                groupbuys.append(groupbuy_data)
            
            # Get total count (simplified for performance)
            total_query = self.db.collection(Collections.GROUP_BUYS)
            if status:
                total_query = total_query.where('status', '==', status)
            
            total_docs = total_query.stream()
            total_count = sum(1 for _ in total_docs)
            
            return {
                'groupbuys': groupbuys,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'pages': (total_count + per_page - 1) // per_page
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting group buys: {str(e)}")
            raise
    
    def get_groupbuy_details(self, groupbuy_id: str) -> Optional[Dict[str, Any]]:
        """Get group buy details with progress tracking"""
        try:
            doc = self.db.collection(Collections.GROUP_BUYS).document(groupbuy_id).get()
            
            if not doc.exists:
                return None
            
            groupbuy_data = doc.to_dict()
            groupbuy_data['id'] = doc.id
            
            # Add calculated fields
            groupbuy_data = self._add_calculated_fields(groupbuy_data)
            
            # Get product information
            product_info = self._get_product_info(groupbuy_data.get('productVariantId'))
            if product_info:
                groupbuy_data['productInfo'] = product_info
            
            # Get participant information (for admin/analytics)
            groupbuy_data['participantCount'] = self._get_participant_count(groupbuy_id)
            
            return groupbuy_data
            
        except Exception as e:
            logger.error(f"Error getting group buy details for {groupbuy_id}: {str(e)}")
            raise
    
    def get_trending_groupbuys(self, page: int = 1, per_page: int = 10) -> Dict[str, Any]:
        """Get trending group buys (high progress or recently successful)"""
        try:
            # Get active group buys ordered by current quantity (popularity)
            query = self.db.collection(Collections.GROUP_BUYS)
            query = query.where('status', '==', 'Active')
            query = query.order_by('currentQuantity', direction=Query.DESCENDING)
            
            # Apply pagination
            offset = (page - 1) * per_page
            query = query.offset(offset).limit(per_page)
            
            docs = query.stream()
            trending_groupbuys = []
            
            for doc in docs:
                groupbuy_data = doc.to_dict()
                groupbuy_data['id'] = doc.id
                
                # Add calculated fields
                groupbuy_data = self._add_calculated_fields(groupbuy_data)
                
                # Only include if progress > 10% or expiring soon
                progress = groupbuy_data.get('progressPercentage', 0)
                is_expiring_soon = groupbuy_data.get('isExpiringSoon', False)
                
                if progress > 10 or is_expiring_soon:
                    trending_groupbuys.append(groupbuy_data)
            
            return {
                'groupbuys': trending_groupbuys,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': len(trending_groupbuys),
                    'pages': 1  # Simplified for trending
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting trending group buys: {str(e)}")
            raise
    
    def join_groupbuy(self, groupbuy_id: str, user_id: str, quantity: int = 1) -> Optional[Dict[str, Any]]:
        """Join group buy with transaction safety"""
        try:
            groupbuy_ref = self.db.collection(Collections.GROUP_BUYS).document(groupbuy_id)
            
            # Use transaction to ensure consistency
            @firestore.transactional
            def join_transaction(transaction):
                # Get current group buy state
                groupbuy_doc = transaction.get(groupbuy_ref)
                
                if not groupbuy_doc.exists:
                    return None
                
                groupbuy_data = groupbuy_doc.to_dict()
                
                # Validate group buy status
                if groupbuy_data.get('status') != 'Active':
                    raise ValueError("Group buy is not active")
                
                # Check if expired
                current_time = int(time.time() * 1000)
                if groupbuy_data.get('expiresAt', 0) <= current_time:
                    raise ValueError("Group buy has expired")
                
                # Calculate new quantity
                current_quantity = groupbuy_data.get('currentQuantity', 0)
                target_quantity = groupbuy_data.get('targetQuantity', 0)
                new_quantity = current_quantity + quantity
                
                # Update group buy
                update_data = {
                    'currentQuantity': new_quantity,
                    'updatedAt': current_time
                }
                
                # Check if target reached
                if new_quantity >= target_quantity:
                    update_data['status'] = 'Successful'
                    update_data['completedAt'] = current_time
                
                transaction.update(groupbuy_ref, update_data)
                
                # Track user participation
                self._track_user_participation(transaction, groupbuy_id, user_id, quantity, 'join')
                
                return groupbuy_id
            
            # Execute transaction
            transaction = self.db.transaction()
            result = join_transaction(transaction)
            
            if result:
                # Get updated group buy details
                updated_groupbuy = self.get_groupbuy_details(groupbuy_id)
                logger.info(f"User {user_id} joined group buy {groupbuy_id} with quantity {quantity}")
                return updated_groupbuy
            
            return None
            
        except Exception as e:
            logger.error(f"Error joining group buy {groupbuy_id}: {str(e)}")
            raise
    
    def leave_groupbuy(self, groupbuy_id: str, user_id: str, quantity: int = 1) -> Optional[Dict[str, Any]]:
        """Leave group buy with transaction safety"""
        try:
            groupbuy_ref = self.db.collection(Collections.GROUP_BUYS).document(groupbuy_id)
            
            # Use transaction to ensure consistency
            @firestore.transactional
            def leave_transaction(transaction):
                # Get current group buy state
                groupbuy_doc = transaction.get(groupbuy_ref)
                
                if not groupbuy_doc.exists:
                    return None
                
                groupbuy_data = groupbuy_doc.to_dict()
                
                # Validate group buy status
                if groupbuy_data.get('status') not in ['Active', 'Successful']:
                    raise ValueError("Cannot leave this group buy")
                
                # Check user participation
                user_quantity = self._get_user_participation(groupbuy_id, user_id)
                if user_quantity < quantity:
                    raise ValueError("Cannot leave more than you joined")
                
                # Calculate new quantity
                current_quantity = groupbuy_data.get('currentQuantity', 0)
                target_quantity = groupbuy_data.get('targetQuantity', 0)
                new_quantity = max(0, current_quantity - quantity)
                
                # Update group buy
                current_time = int(time.time() * 1000)
                update_data = {
                    'currentQuantity': new_quantity,
                    'updatedAt': current_time
                }
                
                # If was successful but now below target, revert to active
                if groupbuy_data.get('status') == 'Successful' and new_quantity < target_quantity:
                    update_data['status'] = 'Active'
                    update_data.pop('completedAt', None)
                
                transaction.update(groupbuy_ref, update_data)
                
                # Track user participation
                self._track_user_participation(transaction, groupbuy_id, user_id, quantity, 'leave')
                
                return groupbuy_id
            
            # Execute transaction
            transaction = self.db.transaction()
            result = leave_transaction(transaction)
            
            if result:
                # Get updated group buy details
                updated_groupbuy = self.get_groupbuy_details(groupbuy_id)
                logger.info(f"User {user_id} left group buy {groupbuy_id} with quantity {quantity}")
                return updated_groupbuy
            
            return None
            
        except Exception as e:
            logger.error(f"Error leaving group buy {groupbuy_id}: {str(e)}")
            raise

    # Admin methods
    def create_groupbuy(self, groupbuy_data: Dict[str, Any], admin_id: str) -> Dict[str, Any]:
        """Create new group buy (Admin only)"""
        try:
            # Validate product variant exists
            product_variant_id = groupbuy_data['productVariantId']
            if not self._validate_product_variant(product_variant_id):
                raise ValueError("Product variant not found")

            # Validate expiration time
            expires_at = groupbuy_data['expiresAt']
            current_time = int(time.time() * 1000)
            if expires_at <= current_time:
                raise ValueError("Expiration time must be in the future")

            # Prepare group buy document
            current_time = int(time.time() * 1000)
            groupbuy_doc = {
                'productVariantId': product_variant_id,
                'targetQuantity': groupbuy_data['targetQuantity'],
                'currentQuantity': 0,
                'groupPrice': groupbuy_data['groupPrice'],
                'status': 'Active',
                'description': groupbuy_data.get('description', ''),
                'expiresAt': expires_at,
                'createdAt': current_time,
                'updatedAt': current_time,
                'createdBy': admin_id
            }

            # Create document
            doc_ref = self.db.collection(Collections.GROUP_BUYS).document()
            doc_ref.set(groupbuy_doc)

            # Get created group buy
            created_groupbuy = self.get_groupbuy_details(doc_ref.id)

            logger.info(f"Group buy created: {doc_ref.id} by admin: {admin_id}")
            return created_groupbuy

        except Exception as e:
            logger.error(f"Error creating group buy: {str(e)}")
            raise

    def update_groupbuy(self, groupbuy_id: str, update_data: Dict[str, Any], admin_id: str) -> Optional[Dict[str, Any]]:
        """Update group buy (Admin only)"""
        try:
            doc_ref = self.db.collection(Collections.GROUP_BUYS).document(groupbuy_id)
            doc = doc_ref.get()

            if not doc.exists:
                return None

            groupbuy_data = doc.to_dict()

            # Validate status for updates
            if groupbuy_data.get('status') not in ['Active']:
                raise ValueError("Can only update active group buys")

            # Validate expiration time if provided
            if 'expiresAt' in update_data:
                current_time = int(time.time() * 1000)
                if update_data['expiresAt'] <= current_time:
                    raise ValueError("Expiration time must be in the future")

            # Add update metadata
            update_data['updatedAt'] = int(time.time() * 1000)
            update_data['lastUpdatedBy'] = admin_id

            # Update document
            doc_ref.update(update_data)

            # Get updated group buy
            updated_groupbuy = self.get_groupbuy_details(groupbuy_id)

            logger.info(f"Group buy updated: {groupbuy_id} by admin: {admin_id}")
            return updated_groupbuy

        except Exception as e:
            logger.error(f"Error updating group buy {groupbuy_id}: {str(e)}")
            raise

    def update_groupbuy_status(self, groupbuy_id: str, new_status: str, admin_id: str, notes: str = '') -> Optional[Dict[str, Any]]:
        """Update group buy status (Admin only)"""
        try:
            doc_ref = self.db.collection(Collections.GROUP_BUYS).document(groupbuy_id)
            doc = doc_ref.get()

            if not doc.exists:
                return None

            # Update status
            current_time = int(time.time() * 1000)
            update_data = {
                'status': new_status,
                'updatedAt': current_time,
                'lastUpdatedBy': admin_id
            }

            if notes:
                update_data['adminNotes'] = notes

            # Add completion time for successful/failed status
            if new_status in ['Successful', 'Failed', 'Cancelled']:
                update_data['completedAt'] = current_time

            doc_ref.update(update_data)

            # Get updated group buy
            updated_groupbuy = self.get_groupbuy_details(groupbuy_id)

            logger.info(f"Group buy {groupbuy_id} status updated to {new_status} by admin {admin_id}")
            return updated_groupbuy

        except Exception as e:
            logger.error(f"Error updating group buy status for {groupbuy_id}: {str(e)}")
            raise

    def delete_groupbuy(self, groupbuy_id: str, admin_id: str) -> bool:
        """Delete/deactivate group buy (Admin only)"""
        try:
            doc_ref = self.db.collection(Collections.GROUP_BUYS).document(groupbuy_id)
            doc = doc_ref.get()

            if not doc.exists:
                return False

            groupbuy_data = doc.to_dict()

            # Only allow deletion of active group buys with no participants
            if groupbuy_data.get('currentQuantity', 0) > 0:
                raise ValueError("Cannot delete group buy with participants")

            # Soft delete by setting status to Cancelled
            current_time = int(time.time() * 1000)
            doc_ref.update({
                'status': 'Cancelled',
                'updatedAt': current_time,
                'lastUpdatedBy': admin_id,
                'deletedAt': current_time
            })

            logger.info(f"Group buy deleted: {groupbuy_id} by admin: {admin_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting group buy {groupbuy_id}: {str(e)}")
            raise

    def check_and_update_expired_groupbuys(self) -> int:
        """Check and update expired group buys"""
        try:
            current_time = int(time.time() * 1000)

            # Get expired active group buys
            query = self.db.collection(Collections.GROUP_BUYS)
            query = query.where('status', '==', 'Active')
            query = query.where('expiresAt', '<=', current_time)

            docs = query.stream()
            updated_count = 0

            batch = self.db.batch()

            for doc in docs:
                groupbuy_data = doc.to_dict()

                # Determine final status based on target achievement
                current_quantity = groupbuy_data.get('currentQuantity', 0)
                target_quantity = groupbuy_data.get('targetQuantity', 0)

                final_status = 'Successful' if current_quantity >= target_quantity else 'Failed'

                batch.update(doc.reference, {
                    'status': final_status,
                    'updatedAt': current_time,
                    'completedAt': current_time
                })

                updated_count += 1

            if updated_count > 0:
                batch.commit()
                logger.info(f"Updated {updated_count} expired group buys")

            return updated_count

        except Exception as e:
            logger.error(f"Error checking expired group buys: {str(e)}")
            raise

    # Helper methods
    def _add_calculated_fields(self, groupbuy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add calculated fields to group buy data"""
        try:
            current_quantity = groupbuy_data.get('currentQuantity', 0)
            target_quantity = groupbuy_data.get('targetQuantity', 1)
            expires_at = groupbuy_data.get('expiresAt', 0)
            current_time = int(time.time() * 1000)

            # Progress percentage
            progress_percentage = min(100, (current_quantity / target_quantity) * 100) if target_quantity > 0 else 0
            groupbuy_data['progressPercentage'] = round(progress_percentage, 1)

            # Time remaining
            time_remaining = max(0, expires_at - current_time)
            groupbuy_data['timeRemaining'] = time_remaining

            # Is expiring soon (within 24 hours)
            twenty_four_hours = 24 * 60 * 60 * 1000
            groupbuy_data['isExpiringSoon'] = time_remaining <= twenty_four_hours and time_remaining > 0

            # Is expired
            groupbuy_data['isExpired'] = time_remaining <= 0

            # Remaining quantity needed
            groupbuy_data['remainingQuantity'] = max(0, target_quantity - current_quantity)

            # Is target reached
            groupbuy_data['isTargetReached'] = current_quantity >= target_quantity

            return groupbuy_data

        except Exception as e:
            logger.warning(f"Error adding calculated fields: {str(e)}")
            return groupbuy_data

    def _get_product_info(self, product_variant_id: str) -> Optional[Dict[str, Any]]:
        """Get product information for group buy"""
        try:
            # Get product variant
            variant_doc = self.db.collection(Collections.PRODUCT_VARIANTS).document(product_variant_id).get()

            if not variant_doc.exists:
                return None

            variant_data = variant_doc.to_dict()

            # Get product details
            product_id = variant_data.get('productId')
            if product_id:
                product_doc = self.db.collection(Collections.PRODUCTS).document(product_id).get()
                if product_doc.exists:
                    product_data = product_doc.to_dict()

                    return {
                        'productId': product_id,
                        'productName': product_data.get('name', ''),
                        'productImage': product_data.get('coverImageUrl', ''),
                        'productCategory': product_data.get('category', ''),
                        'variantSku': variant_data.get('sku', ''),
                        'variantAttributes': variant_data.get('attributes', {}),
                        'originalPrice': variant_data.get('myPrice', 0)
                    }

            return None

        except Exception as e:
            logger.warning(f"Error getting product info for variant {product_variant_id}: {str(e)}")
            return None

    def _get_product_category(self, product_variant_id: str) -> Optional[str]:
        """Get product category for filtering"""
        try:
            product_info = self._get_product_info(product_variant_id)
            return product_info.get('productCategory') if product_info else None

        except Exception as e:
            logger.warning(f"Error getting product category: {str(e)}")
            return None

    def _validate_product_variant(self, product_variant_id: str) -> bool:
        """Validate that product variant exists and is active"""
        try:
            variant_doc = self.db.collection(Collections.PRODUCT_VARIANTS).document(product_variant_id).get()

            if not variant_doc.exists:
                return False

            variant_data = variant_doc.to_dict()
            return variant_data.get('status') == 'Active'

        except Exception as e:
            logger.warning(f"Error validating product variant {product_variant_id}: {str(e)}")
            return False

    def _track_user_participation(self, transaction, groupbuy_id: str, user_id: str, quantity: int, action: str):
        """Track user participation in group buy"""
        try:
            # This would typically track user participation for analytics
            # For now, we'll create a simple participation record
            participation_ref = self.db.collection('groupBuyParticipations').document()

            participation_data = {
                'groupBuyId': groupbuy_id,
                'userId': user_id,
                'quantity': quantity,
                'action': action,  # 'join' or 'leave'
                'timestamp': int(time.time() * 1000)
            }

            transaction.set(participation_ref, participation_data)

        except Exception as e:
            logger.warning(f"Error tracking user participation: {str(e)}")

    def _get_user_participation(self, groupbuy_id: str, user_id: str) -> int:
        """Get user's current participation quantity"""
        try:
            # Query participation records
            query = self.db.collection('groupBuyParticipations')
            query = query.where('groupBuyId', '==', groupbuy_id)
            query = query.where('userId', '==', user_id)
            query = query.order_by('timestamp', direction=Query.ASCENDING)

            docs = query.stream()
            total_quantity = 0

            for doc in docs:
                participation_data = doc.to_dict()
                action = participation_data.get('action')
                quantity = participation_data.get('quantity', 0)

                if action == 'join':
                    total_quantity += quantity
                elif action == 'leave':
                    total_quantity -= quantity

            return max(0, total_quantity)

        except Exception as e:
            logger.warning(f"Error getting user participation: {str(e)}")
            return 0

    def _get_participant_count(self, groupbuy_id: str) -> int:
        """Get number of unique participants"""
        try:
            # Query participation records
            query = self.db.collection('groupBuyParticipations')
            query = query.where('groupBuyId', '==', groupbuy_id)

            docs = query.stream()
            participants = set()

            for doc in docs:
                participation_data = doc.to_dict()
                user_id = participation_data.get('userId')
                if user_id:
                    participants.add(user_id)

            return len(participants)

        except Exception as e:
            logger.warning(f"Error getting participant count: {str(e)}")
            return 0

    def get_groupbuy_statistics(self) -> Dict[str, Any]:
        """Get group buy statistics for admin dashboard"""
        try:
            # Get group buys from last 30 days
            thirty_days_ago = int((time.time() - 30 * 24 * 60 * 60) * 1000)

            # Total group buys
            total_query = self.db.collection(Collections.GROUP_BUYS)
            total_groupbuys = sum(1 for _ in total_query.stream())

            # Recent group buys
            recent_query = self.db.collection(Collections.GROUP_BUYS)
            recent_query = recent_query.where('createdAt', '>=', thirty_days_ago)
            recent_groupbuys = sum(1 for _ in recent_query.stream())

            # Group buys by status
            status_counts = {}
            all_groupbuys_query = self.db.collection(Collections.GROUP_BUYS)
            for doc in all_groupbuys_query.stream():
                groupbuy_data = doc.to_dict()
                status = groupbuy_data.get('status', 'Unknown')
                status_counts[status] = status_counts.get(status, 0) + 1

            # Active group buys
            active_groupbuys = status_counts.get('Active', 0)

            # Successful group buys
            successful_groupbuys = status_counts.get('Successful', 0)

            return {
                'totalGroupBuys': total_groupbuys,
                'recentGroupBuys': recent_groupbuys,
                'activeGroupBuys': active_groupbuys,
                'successfulGroupBuys': successful_groupbuys,
                'statusBreakdown': status_counts
            }

        except Exception as e:
            logger.error(f"Error getting group buy statistics: {str(e)}")
            raise
