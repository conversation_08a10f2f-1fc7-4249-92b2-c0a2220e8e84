"""
Product service - Business logic for product operations
"""
import logging
import time
from typing import Dict, List, Any, Optional
from google.cloud.firestore import Query
from config.firebase_config import FirebaseConfig, Collections
from config.algolia_config import AlgoliaConfig

logger = logging.getLogger(__name__)

class ProductService:
    """Service class for product operations"""
    
    def __init__(self):
        self.db = FirebaseConfig.get_db()
    
    def get_products(self, 
                    category: Optional[str] = None,
                    status: str = 'Active',
                    search: Optional[str] = None,
                    page: int = 1,
                    per_page: int = 20,
                    sort_by: str = 'createdAt',
                    sort_order: str = 'desc') -> Dict[str, Any]:
        """Get products with filtering and pagination"""
        try:
            # If search query is provided, use Algolia
            if search and search.strip():
                return self._search_products_algolia(search, category, page, per_page)
            
            # Build Firestore query
            query = self.db.collection(Collections.PRODUCTS)
            
            # Apply filters
            if status:
                query = query.where('status', '==', status)
            
            if category:
                query = query.where('category', '==', category)
            
            # Apply sorting
            sort_direction = Query.DESCENDING if sort_order == 'desc' else Query.ASCENDING
            query = query.order_by(sort_by, direction=sort_direction)
            
            # Apply pagination
            offset = (page - 1) * per_page
            query = query.offset(offset).limit(per_page)
            
            # Execute query
            docs = query.stream()
            products = []
            
            for doc in docs:
                product_data = doc.to_dict()
                product_data['id'] = doc.id
                products.append(product_data)
            
            # Get total count for pagination (this is expensive, consider caching)
            total_query = self.db.collection(Collections.PRODUCTS)
            if status:
                total_query = total_query.where('status', '==', status)
            if category:
                total_query = total_query.where('category', '==', category)
            
            total_docs = total_query.stream()
            total_count = sum(1 for _ in total_docs)
            
            return {
                'products': products,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'pages': (total_count + per_page - 1) // per_page
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting products: {str(e)}")
            raise
    
    def _search_products_algolia(self, search: str, category: Optional[str], 
                                page: int, per_page: int) -> Dict[str, Any]:
        """Search products using Algolia"""
        try:
            filters = {}
            if category:
                filters['category'] = category
            
            result = AlgoliaConfig.search_products(
                query=search,
                filters=filters,
                page=page - 1,  # Algolia uses 0-based pagination
                per_page=per_page
            )
            
            return {
                'products': result['hits'],
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': result['total'],
                    'pages': result['pages']
                },
                'search_info': {
                    'query': search,
                    'processing_time': result['processing_time']
                }
            }
            
        except Exception as e:
            logger.error(f"Error searching products with Algolia: {str(e)}")
            # Fallback to Firestore search
            return self._search_products_firestore(search, category, page, per_page)
    
    def _search_products_firestore(self, search: str, category: Optional[str], 
                                  page: int, per_page: int) -> Dict[str, Any]:
        """Fallback search using Firestore (limited functionality)"""
        try:
            query = self.db.collection(Collections.PRODUCTS)
            query = query.where('status', '==', 'Active')
            
            if category:
                query = query.where('category', '==', category)
            
            # Simple prefix search on name field
            search_lower = search.lower()
            query = query.where('name', '>=', search_lower).where('name', '<=', search_lower + '\uf8ff')
            
            # Apply pagination
            offset = (page - 1) * per_page
            query = query.offset(offset).limit(per_page)
            
            docs = query.stream()
            products = []
            
            for doc in docs:
                product_data = doc.to_dict()
                product_data['id'] = doc.id
                products.append(product_data)
            
            return {
                'products': products,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': len(products),  # Approximate
                    'pages': 1  # Approximate
                },
                'search_info': {
                    'query': search,
                    'method': 'firestore_fallback'
                }
            }
            
        except Exception as e:
            logger.error(f"Error in Firestore search fallback: {str(e)}")
            raise
    
    def get_product_by_id(self, product_id: str) -> Optional[Dict[str, Any]]:
        """Get product by ID"""
        try:
            doc = self.db.collection(Collections.PRODUCTS).document(product_id).get()
            
            if not doc.exists:
                return None
            
            product_data = doc.to_dict()
            product_data['id'] = doc.id
            
            return product_data
            
        except Exception as e:
            logger.error(f"Error getting product {product_id}: {str(e)}")
            raise
    
    def get_product_variants(self, product_id: str) -> List[Dict[str, Any]]:
        """Get variants for a product"""
        try:
            query = self.db.collection(Collections.PRODUCT_VARIANTS)
            query = query.where('productId', '==', product_id)
            query = query.where('status', '==', 'Active')
            
            docs = query.stream()
            variants = []
            
            for doc in docs:
                variant_data = doc.to_dict()
                variant_data['id'] = doc.id
                variants.append(variant_data)
            
            return variants
            
        except Exception as e:
            logger.error(f"Error getting variants for product {product_id}: {str(e)}")
            raise
    
    def get_categories(self) -> List[str]:
        """Get all product categories"""
        try:
            query = self.db.collection(Collections.PRODUCTS)
            query = query.where('status', '==', 'Active')
            
            docs = query.stream()
            categories = set()
            
            for doc in docs:
                product_data = doc.to_dict()
                category = product_data.get('category')
                if category:
                    categories.add(category)
            
            return sorted(list(categories))
            
        except Exception as e:
            logger.error(f"Error getting categories: {str(e)}")
            raise
    
    def create_product(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create new product"""
        try:
            # Add timestamps
            current_time = int(time.time() * 1000)
            product_data['createdAt'] = current_time
            product_data['updatedAt'] = current_time
            
            # Create document
            doc_ref = self.db.collection(Collections.PRODUCTS).document()
            doc_ref.set(product_data)
            
            # Get created product
            created_product = doc_ref.get().to_dict()
            created_product['id'] = doc_ref.id
            
            # Index in Algolia
            try:
                AlgoliaConfig.index_product(created_product)
            except Exception as e:
                logger.warning(f"Failed to index product in Algolia: {str(e)}")
            
            logger.info(f"Product created: {doc_ref.id}")
            return created_product
            
        except Exception as e:
            logger.error(f"Error creating product: {str(e)}")
            raise
    
    def update_product(self, product_id: str, update_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update product"""
        try:
            doc_ref = self.db.collection(Collections.PRODUCTS).document(product_id)
            doc = doc_ref.get()
            
            if not doc.exists:
                return None
            
            # Add update timestamp
            update_data['updatedAt'] = int(time.time() * 1000)
            
            # Update document
            doc_ref.update(update_data)
            
            # Get updated product
            updated_product = doc_ref.get().to_dict()
            updated_product['id'] = product_id
            
            # Update in Algolia
            try:
                AlgoliaConfig.index_product(updated_product)
            except Exception as e:
                logger.warning(f"Failed to update product in Algolia: {str(e)}")
            
            logger.info(f"Product updated: {product_id}")
            return updated_product
            
        except Exception as e:
            logger.error(f"Error updating product {product_id}: {str(e)}")
            raise
    
    def delete_product(self, product_id: str) -> bool:
        """Delete product (soft delete by setting status to 'Discontinued')"""
        try:
            doc_ref = self.db.collection(Collections.PRODUCTS).document(product_id)
            doc = doc_ref.get()
            
            if not doc.exists:
                return False
            
            # Soft delete
            doc_ref.update({
                'status': 'Discontinued',
                'updatedAt': int(time.time() * 1000)
            })
            
            # Remove from Algolia
            try:
                AlgoliaConfig.delete_product(product_id)
            except Exception as e:
                logger.warning(f"Failed to delete product from Algolia: {str(e)}")
            
            logger.info(f"Product deleted: {product_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting product {product_id}: {str(e)}")
            raise
    
    def create_variant(self, variant_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create product variant"""
        try:
            # Add timestamps
            current_time = int(time.time() * 1000)
            variant_data['createdAt'] = current_time
            variant_data['updatedAt'] = current_time
            
            # Create document
            doc_ref = self.db.collection(Collections.PRODUCT_VARIANTS).document()
            doc_ref.set(variant_data)
            
            # Get created variant
            created_variant = doc_ref.get().to_dict()
            created_variant['id'] = doc_ref.id
            
            logger.info(f"Product variant created: {doc_ref.id}")
            return created_variant
            
        except Exception as e:
            logger.error(f"Error creating product variant: {str(e)}")
            raise
