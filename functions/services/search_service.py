"""
Search service - Enhanced search logic with Algolia integration
"""
import logging
import time
import hashlib
from typing import Dict, List, Any, Optional
from google.cloud.firestore import Query
from config.firebase_config import FirebaseConfig, Collections
from config.algolia_config import AlgoliaConfig

logger = logging.getLogger(__name__)

class SearchService:
    """Service class for enhanced search operations"""
    
    def __init__(self):
        self.db = FirebaseConfig.get_db()
        self.cache_ttl = 300  # 5 minutes cache TTL
        self._search_cache = {}
    
    def search_products(self, 
                       query: str,
                       filters: Dict[str, Any] = None,
                       sort_by: str = 'relevance',
                       page: int = 1,
                       per_page: int = 20,
                       include_facets: bool = True,
                       include_highlights: bool = True,
                       user_id: Optional[str] = None) -> Dict[str, Any]:
        """Advanced product search with filters and faceting"""
        try:
            # Track search query for analytics
            self._track_search_query(query, 'products', user_id)
            
            # Check cache first
            cache_key = self._generate_cache_key('products', query, filters, sort_by, page, per_page)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return cached_result
            
            # Prepare Algolia search parameters
            search_params = {
                'page': page - 1,  # Algolia uses 0-based pagination
                'hitsPerPage': per_page,
                'attributesToRetrieve': [
                    'objectID', 'name', 'description', 'category', 
                    'coverImageUrl', 'status', 'createdAt', 'price'
                ],
                'typoTolerance': True,
                'minWordSizefor1Typo': 3,
                'minWordSizefor2Typos': 7
            }
            
            # Add highlighting if requested
            if include_highlights:
                search_params.update({
                    'attributesToHighlight': ['name', 'description', 'category'],
                    'highlightPreTag': '<mark>',
                    'highlightPostTag': '</mark>'
                })
            
            # Add faceting if requested
            if include_facets:
                search_params['facets'] = ['category', 'status', 'price_range']
            
            # Apply filters
            filter_strings = []
            if filters:
                if filters.get('category'):
                    filter_strings.append(f"category:'{filters['category']}'")
                
                if filters.get('status'):
                    filter_strings.append(f"status:'{filters['status']}'")
                
                if filters.get('min_price') is not None:
                    filter_strings.append(f"price >= {filters['min_price']}")
                
                if filters.get('max_price') is not None:
                    filter_strings.append(f"price <= {filters['max_price']}")
            
            if filter_strings:
                search_params['filters'] = ' AND '.join(filter_strings)
            
            # Apply sorting
            if sort_by != 'relevance':
                sort_mapping = {
                    'price_asc': 'price:asc',
                    'price_desc': 'price:desc',
                    'name_asc': 'name:asc',
                    'name_desc': 'name:desc',
                    'newest': 'createdAt:desc',
                    'oldest': 'createdAt:asc'
                }
                if sort_by in sort_mapping:
                    search_params['ranking'] = [sort_mapping[sort_by]]
            
            # Execute search
            products_index = AlgoliaConfig.get_products_index()
            algolia_result = products_index.search(query, search_params)
            
            # Process results
            result = {
                'hits': algolia_result['hits'],
                'total': algolia_result['nbHits'],
                'page': page,
                'pages': algolia_result['nbPages'],
                'per_page': per_page,
                'processing_time': algolia_result['processingTimeMS'],
                'query': query,
                'filters_applied': filters or {}
            }
            
            # Add facets if requested
            if include_facets and 'facets' in algolia_result:
                result['facets'] = self._process_facets(algolia_result['facets'])
            
            # Add search suggestions if no results
            if algolia_result['nbHits'] == 0:
                result['suggestions'] = self.get_search_suggestions(query, limit=3)
            
            # Cache result
            self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in product search: {str(e)}")
            # Fallback to Firestore search
            return self._fallback_product_search(query, filters, page, per_page)
    
    def search_groupbuys(self,
                        query: str,
                        filters: Dict[str, Any] = None,
                        sort_by: str = 'relevance',
                        page: int = 1,
                        per_page: int = 20,
                        user_id: Optional[str] = None) -> Dict[str, Any]:
        """Search group buys with advanced filtering"""
        try:
            # Track search query for analytics
            self._track_search_query(query, 'groupbuys', user_id)
            
            # Check cache first
            cache_key = self._generate_cache_key('groupbuys', query, filters, sort_by, page, per_page)
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return cached_result
            
            # For now, use Firestore search for group buys (can be enhanced with Algolia later)
            result = self._search_groupbuys_firestore(query, filters, sort_by, page, per_page)
            
            # Cache result
            self._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error in group buy search: {str(e)}")
            raise
    
    def get_search_suggestions(self,
                              query: str,
                              limit: int = 5,
                              search_type: str = 'all',
                              user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get search autocomplete and suggestions"""
        try:
            suggestions = {
                'query_suggestions': [],
                'product_suggestions': [],
                'category_suggestions': [],
                'groupbuy_suggestions': []
            }
            
            # Get query suggestions from search history
            if search_type in ['all', 'queries']:
                suggestions['query_suggestions'] = self._get_query_suggestions(query, limit)
            
            # Get product suggestions
            if search_type in ['all', 'products']:
                suggestions['product_suggestions'] = self._get_product_suggestions(query, limit)
            
            # Get category suggestions
            if search_type in ['all', 'categories']:
                suggestions['category_suggestions'] = self._get_category_suggestions(query, limit)
            
            # Get group buy suggestions
            if search_type in ['all', 'groupbuys']:
                suggestions['groupbuy_suggestions'] = self._get_groupbuy_suggestions(query, limit)
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error getting search suggestions: {str(e)}")
            return {'query_suggestions': [], 'product_suggestions': [], 'category_suggestions': [], 'groupbuy_suggestions': []}
    
    def search_categories(self, query: str = '', include_counts: bool = True) -> Dict[str, Any]:
        """Search and filter product categories"""
        try:
            # Get all categories from products
            categories_query = self.db.collection(Collections.PRODUCTS)
            categories_query = categories_query.where('status', '==', 'Active')
            
            docs = categories_query.stream()
            category_counts = {}
            
            for doc in docs:
                product_data = doc.to_dict()
                category = product_data.get('category', '').strip()
                if category:
                    category_counts[category] = category_counts.get(category, 0) + 1
            
            # Filter categories by query if provided
            if query:
                query_lower = query.lower()
                filtered_categories = {
                    cat: count for cat, count in category_counts.items()
                    if query_lower in cat.lower()
                }
            else:
                filtered_categories = category_counts
            
            # Format results
            categories = []
            for category, count in sorted(filtered_categories.items()):
                category_data = {'name': category}
                if include_counts:
                    category_data['product_count'] = count
                categories.append(category_data)
            
            return {
                'categories': categories,
                'total': len(categories),
                'query': query
            }
            
        except Exception as e:
            logger.error(f"Error searching categories: {str(e)}")
            raise
    
    def get_trending_searches(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """Get trending search terms and popular queries"""
        try:
            # Get trending searches from the last 7 days
            seven_days_ago = int((time.time() - 7 * 24 * 60 * 60) * 1000)
            
            # Query search analytics
            query = self.db.collection('searchAnalytics')
            query = query.where('timestamp', '>=', seven_days_ago)
            query = query.order_by('timestamp', direction=Query.DESCENDING)
            query = query.limit(1000)  # Limit for performance
            
            docs = query.stream()
            query_counts = {}
            
            for doc in docs:
                search_data = doc.to_dict()
                search_query = search_data.get('query', '').strip().lower()
                if search_query and len(search_query) > 2:  # Filter out very short queries
                    query_counts[search_query] = query_counts.get(search_query, 0) + 1
            
            # Sort by frequency and get top trending
            trending_queries = sorted(query_counts.items(), key=lambda x: x[1], reverse=True)[:20]
            
            # Get popular categories
            popular_categories = self._get_popular_categories()
            
            # Get recent searches for user if authenticated
            recent_searches = []
            if user_id:
                recent_searches = self._get_user_recent_searches(user_id)
            
            return {
                'trending_queries': [{'query': q, 'count': c} for q, c in trending_queries],
                'popular_categories': popular_categories,
                'recent_searches': recent_searches,
                'period': '7_days'
            }
            
        except Exception as e:
            logger.error(f"Error getting trending searches: {str(e)}")
            return {'trending_queries': [], 'popular_categories': [], 'recent_searches': []}
    
    # Admin methods
    def reindex_search_data(self, index_type: str, force: bool = False, admin_id: str = None) -> Dict[str, Any]:
        """Manually trigger search index rebuild"""
        try:
            start_time = time.time()
            results = {'reindexed': 0, 'errors': 0, 'type': index_type}
            
            if index_type in ['products', 'all']:
                product_result = self._reindex_products(force)
                results['products'] = product_result
                results['reindexed'] += product_result.get('count', 0)
                results['errors'] += product_result.get('errors', 0)
            
            if index_type in ['groupbuys', 'all']:
                groupbuy_result = self._reindex_groupbuys(force)
                results['groupbuys'] = groupbuy_result
                results['reindexed'] += groupbuy_result.get('count', 0)
                results['errors'] += groupbuy_result.get('errors', 0)
            
            results['duration'] = round(time.time() - start_time, 2)
            
            # Log admin action
            if admin_id:
                self._log_admin_action(admin_id, 'reindex', {'type': index_type, 'force': force, 'results': results})
            
            logger.info(f"Search reindex completed: {results}")
            return results
            
        except Exception as e:
            logger.error(f"Error in search reindex: {str(e)}")
            raise

    def sync_search_data(self, data_type: str, ids: List[str], admin_id: str = None) -> Dict[str, Any]:
        """Sync specific products/group buys to search index"""
        try:
            results = {'synced': 0, 'errors': 0, 'type': data_type, 'ids': ids}

            if data_type == 'product':
                for product_id in ids:
                    try:
                        # Get product data
                        product_doc = self.db.collection(Collections.PRODUCTS).document(product_id).get()
                        if product_doc.exists:
                            product_data = product_doc.to_dict()
                            product_data['id'] = product_id

                            # Index in Algolia
                            AlgoliaConfig.index_product(product_data)
                            results['synced'] += 1
                        else:
                            results['errors'] += 1
                    except Exception as e:
                        logger.error(f"Error syncing product {product_id}: {str(e)}")
                        results['errors'] += 1

            elif data_type == 'groupbuy':
                for groupbuy_id in ids:
                    try:
                        # Get group buy data
                        groupbuy_doc = self.db.collection(Collections.GROUP_BUYS).document(groupbuy_id).get()
                        if groupbuy_doc.exists:
                            groupbuy_data = groupbuy_doc.to_dict()
                            groupbuy_data['id'] = groupbuy_id

                            # Index in search (implement group buy indexing)
                            self._index_groupbuy(groupbuy_data)
                            results['synced'] += 1
                        else:
                            results['errors'] += 1
                    except Exception as e:
                        logger.error(f"Error syncing group buy {groupbuy_id}: {str(e)}")
                        results['errors'] += 1

            # Log admin action
            if admin_id:
                self._log_admin_action(admin_id, 'sync', {'type': data_type, 'ids': ids, 'results': results})

            logger.info(f"Search sync completed: {results}")
            return results

        except Exception as e:
            logger.error(f"Error in search sync: {str(e)}")
            raise

    def get_search_analytics(self) -> Dict[str, Any]:
        """Get search performance metrics and analytics"""
        try:
            # Get analytics from the last 30 days
            thirty_days_ago = int((time.time() - 30 * 24 * 60 * 60) * 1000)

            # Query search analytics
            query = self.db.collection('searchAnalytics')
            query = query.where('timestamp', '>=', thirty_days_ago)

            docs = query.stream()

            total_searches = 0
            query_counts = {}
            type_counts = {}
            daily_counts = {}

            for doc in docs:
                search_data = doc.to_dict()
                total_searches += 1

                # Count by query
                search_query = search_data.get('query', '').strip().lower()
                if search_query:
                    query_counts[search_query] = query_counts.get(search_query, 0) + 1

                # Count by type
                search_type = search_data.get('type', 'unknown')
                type_counts[search_type] = type_counts.get(search_type, 0) + 1

                # Count by day
                timestamp = search_data.get('timestamp', 0)
                day = timestamp // (24 * 60 * 60 * 1000) * (24 * 60 * 60 * 1000)
                daily_counts[day] = daily_counts.get(day, 0) + 1

            # Get top queries
            top_queries = sorted(query_counts.items(), key=lambda x: x[1], reverse=True)[:20]

            # Get search performance metrics
            avg_searches_per_day = total_searches / 30 if total_searches > 0 else 0

            return {
                'total_searches': total_searches,
                'avg_searches_per_day': round(avg_searches_per_day, 2),
                'top_queries': [{'query': q, 'count': c} for q, c in top_queries],
                'search_types': type_counts,
                'daily_breakdown': daily_counts,
                'period': '30_days'
            }

        except Exception as e:
            logger.error(f"Error getting search analytics: {str(e)}")
            raise

    # Helper methods
    def _track_search_query(self, query: str, search_type: str, user_id: Optional[str] = None):
        """Track search query for analytics"""
        try:
            search_data = {
                'query': query.strip(),
                'type': search_type,
                'timestamp': int(time.time() * 1000),
                'user_id': user_id
            }

            # Store in Firestore for analytics
            self.db.collection('searchAnalytics').add(search_data)

        except Exception as e:
            logger.warning(f"Error tracking search query: {str(e)}")

    def _generate_cache_key(self, search_type: str, query: str, filters: Dict[str, Any],
                           sort_by: str, page: int, per_page: int) -> str:
        """Generate cache key for search results"""
        key_data = f"{search_type}:{query}:{filters}:{sort_by}:{page}:{per_page}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def _get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get cached search result"""
        try:
            if cache_key in self._search_cache:
                cached_data = self._search_cache[cache_key]
                if time.time() - cached_data['timestamp'] < self.cache_ttl:
                    return cached_data['result']
                else:
                    # Remove expired cache
                    del self._search_cache[cache_key]
            return None
        except Exception as e:
            logger.warning(f"Error getting cached result: {str(e)}")
            return None

    def _cache_result(self, cache_key: str, result: Dict[str, Any]):
        """Cache search result"""
        try:
            self._search_cache[cache_key] = {
                'result': result,
                'timestamp': time.time()
            }

            # Clean up old cache entries (simple cleanup)
            if len(self._search_cache) > 1000:
                # Remove oldest 20% of entries
                sorted_keys = sorted(self._search_cache.keys(),
                                   key=lambda k: self._search_cache[k]['timestamp'])
                for key in sorted_keys[:200]:
                    del self._search_cache[key]

        except Exception as e:
            logger.warning(f"Error caching result: {str(e)}")

    def _process_facets(self, algolia_facets: Dict[str, Any]) -> Dict[str, Any]:
        """Process Algolia facets for better presentation"""
        try:
            processed_facets = {}

            for facet_name, facet_values in algolia_facets.items():
                if facet_name == 'category':
                    processed_facets['categories'] = [
                        {'name': name, 'count': count}
                        for name, count in facet_values.items()
                    ]
                elif facet_name == 'status':
                    processed_facets['statuses'] = [
                        {'name': name, 'count': count}
                        for name, count in facet_values.items()
                    ]
                elif facet_name == 'price_range':
                    processed_facets['price_ranges'] = [
                        {'range': name, 'count': count}
                        for name, count in facet_values.items()
                    ]

            return processed_facets

        except Exception as e:
            logger.warning(f"Error processing facets: {str(e)}")
            return {}

    def _fallback_product_search(self, query: str, filters: Dict[str, Any],
                                page: int, per_page: int) -> Dict[str, Any]:
        """Fallback product search using Firestore"""
        try:
            query_lower = query.lower()

            # Build Firestore query
            firestore_query = self.db.collection(Collections.PRODUCTS)
            firestore_query = firestore_query.where('status', '==', 'Active')

            # Apply category filter if provided
            if filters and filters.get('category'):
                firestore_query = firestore_query.where('category', '==', filters['category'])

            # Simple text search on name field
            firestore_query = firestore_query.where('name', '>=', query_lower)
            firestore_query = firestore_query.where('name', '<=', query_lower + '\uf8ff')

            # Apply pagination
            offset = (page - 1) * per_page
            firestore_query = firestore_query.offset(offset).limit(per_page)

            docs = firestore_query.stream()
            products = []

            for doc in docs:
                product_data = doc.to_dict()
                product_data['id'] = doc.id
                products.append(product_data)

            return {
                'hits': products,
                'total': len(products),
                'page': page,
                'pages': 1,
                'per_page': per_page,
                'processing_time': 0,
                'query': query,
                'method': 'firestore_fallback'
            }

        except Exception as e:
            logger.error(f"Error in fallback product search: {str(e)}")
            return {'hits': [], 'total': 0, 'page': page, 'pages': 0, 'per_page': per_page}

    def _search_groupbuys_firestore(self, query: str, filters: Dict[str, Any],
                                   sort_by: str, page: int, per_page: int) -> Dict[str, Any]:
        """Search group buys using Firestore"""
        try:
            # Build query
            firestore_query = self.db.collection(Collections.GROUP_BUYS)

            # Apply status filter
            status = filters.get('status', 'Active') if filters else 'Active'
            firestore_query = firestore_query.where('status', '==', status)

            # Apply expiring soon filter
            if filters and filters.get('expiring_soon'):
                twenty_four_hours = 24 * 60 * 60 * 1000
                expiry_threshold = int(time.time() * 1000) + twenty_four_hours
                firestore_query = firestore_query.where('expiresAt', '<=', expiry_threshold)

            # Apply sorting
            if sort_by == 'progress_desc':
                firestore_query = firestore_query.order_by('currentQuantity', direction=Query.DESCENDING)
            elif sort_by == 'expiring_soon':
                firestore_query = firestore_query.order_by('expiresAt', direction=Query.ASCENDING)
            elif sort_by == 'newest':
                firestore_query = firestore_query.order_by('createdAt', direction=Query.DESCENDING)
            else:
                firestore_query = firestore_query.order_by('createdAt', direction=Query.DESCENDING)

            # Apply pagination
            offset = (page - 1) * per_page
            firestore_query = firestore_query.offset(offset).limit(per_page)

            docs = firestore_query.stream()
            groupbuys = []

            for doc in docs:
                groupbuy_data = doc.to_dict()
                groupbuy_data['id'] = doc.id

                # Add calculated fields
                groupbuy_data = self._add_groupbuy_calculated_fields(groupbuy_data)

                # Filter by query (simple text matching)
                if query.lower() in groupbuy_data.get('description', '').lower():
                    groupbuys.append(groupbuy_data)

            return {
                'hits': groupbuys,
                'total': len(groupbuys),
                'page': page,
                'pages': 1,
                'per_page': per_page,
                'processing_time': 0,
                'query': query,
                'method': 'firestore'
            }

        except Exception as e:
            logger.error(f"Error in group buy Firestore search: {str(e)}")
            return {'hits': [], 'total': 0, 'page': page, 'pages': 0, 'per_page': per_page}

    def _get_query_suggestions(self, query: str, limit: int) -> List[str]:
        """Get query suggestions from search history"""
        try:
            query_lower = query.lower()

            # Get recent search queries
            recent_query = self.db.collection('searchAnalytics')
            recent_query = recent_query.order_by('timestamp', direction=Query.DESCENDING)
            recent_query = recent_query.limit(1000)

            docs = recent_query.stream()
            suggestions = set()

            for doc in docs:
                search_data = doc.to_dict()
                search_query = search_data.get('query', '').strip()

                if (search_query and
                    len(search_query) > len(query) and
                    search_query.lower().startswith(query_lower)):
                    suggestions.add(search_query)

                    if len(suggestions) >= limit:
                        break

            return list(suggestions)[:limit]

        except Exception as e:
            logger.warning(f"Error getting query suggestions: {str(e)}")
            return []

    def _get_product_suggestions(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Get product suggestions"""
        try:
            # Use Algolia for product suggestions
            products_index = AlgoliaConfig.get_products_index()

            result = products_index.search(query, {
                'hitsPerPage': limit,
                'attributesToRetrieve': ['objectID', 'name', 'coverImageUrl', 'category'],
                'typoTolerance': True
            })

            return [
                {
                    'id': hit['objectID'],
                    'name': hit.get('name', ''),
                    'image': hit.get('coverImageUrl', ''),
                    'category': hit.get('category', '')
                }
                for hit in result['hits']
            ]

        except Exception as e:
            logger.warning(f"Error getting product suggestions: {str(e)}")
            return []

    def _get_category_suggestions(self, query: str, limit: int) -> List[str]:
        """Get category suggestions"""
        try:
            query_lower = query.lower()

            # Get categories from products
            categories_query = self.db.collection(Collections.PRODUCTS)
            categories_query = categories_query.where('status', '==', 'Active')

            docs = categories_query.stream()
            categories = set()

            for doc in docs:
                product_data = doc.to_dict()
                category = product_data.get('category', '').strip()

                if category and query_lower in category.lower():
                    categories.add(category)

                    if len(categories) >= limit:
                        break

            return list(categories)[:limit]

        except Exception as e:
            logger.warning(f"Error getting category suggestions: {str(e)}")
            return []

    def _get_groupbuy_suggestions(self, query: str, limit: int) -> List[Dict[str, Any]]:
        """Get group buy suggestions"""
        try:
            query_lower = query.lower()

            # Search active group buys
            groupbuys_query = self.db.collection(Collections.GROUP_BUYS)
            groupbuys_query = groupbuys_query.where('status', '==', 'Active')
            groupbuys_query = groupbuys_query.limit(50)  # Limit for performance

            docs = groupbuys_query.stream()
            suggestions = []

            for doc in docs:
                groupbuy_data = doc.to_dict()
                description = groupbuy_data.get('description', '').lower()

                if query_lower in description:
                    suggestions.append({
                        'id': doc.id,
                        'description': groupbuy_data.get('description', ''),
                        'progress': self._calculate_progress(groupbuy_data),
                        'expires_at': groupbuy_data.get('expiresAt', 0)
                    })

                    if len(suggestions) >= limit:
                        break

            return suggestions

        except Exception as e:
            logger.warning(f"Error getting group buy suggestions: {str(e)}")
            return []

    def _get_popular_categories(self) -> List[Dict[str, Any]]:
        """Get popular categories based on search frequency"""
        try:
            # Get search analytics for categories
            seven_days_ago = int((time.time() - 7 * 24 * 60 * 60) * 1000)

            query = self.db.collection('searchAnalytics')
            query = query.where('timestamp', '>=', seven_days_ago)

            docs = query.stream()
            category_counts = {}

            for doc in docs:
                search_data = doc.to_dict()
                # This would need to be enhanced to track category searches
                # For now, return static popular categories
                pass

            # Return static popular categories for now
            return [
                {'name': 'Electronics', 'searches': 150},
                {'name': 'Fashion', 'searches': 120},
                {'name': 'Home & Garden', 'searches': 90},
                {'name': 'Sports', 'searches': 75},
                {'name': 'Books', 'searches': 60}
            ]

        except Exception as e:
            logger.warning(f"Error getting popular categories: {str(e)}")
            return []

    def _get_user_recent_searches(self, user_id: str) -> List[str]:
        """Get user's recent search queries"""
        try:
            query = self.db.collection('searchAnalytics')
            query = query.where('user_id', '==', user_id)
            query = query.order_by('timestamp', direction=Query.DESCENDING)
            query = query.limit(10)

            docs = query.stream()
            recent_searches = []

            for doc in docs:
                search_data = doc.to_dict()
                search_query = search_data.get('query', '').strip()
                if search_query and search_query not in recent_searches:
                    recent_searches.append(search_query)

            return recent_searches

        except Exception as e:
            logger.warning(f"Error getting user recent searches: {str(e)}")
            return []

    def _reindex_products(self, force: bool = False) -> Dict[str, Any]:
        """Reindex all products in Algolia"""
        try:
            products_query = self.db.collection(Collections.PRODUCTS)
            if not force:
                products_query = products_query.where('status', '==', 'Active')

            docs = products_query.stream()
            products = []

            for doc in docs:
                product_data = doc.to_dict()
                product_data['id'] = doc.id
                products.append(product_data)

            # Batch index in Algolia
            if products:
                AlgoliaConfig.index_products_batch(products)

            return {'count': len(products), 'errors': 0}

        except Exception as e:
            logger.error(f"Error reindexing products: {str(e)}")
            return {'count': 0, 'errors': 1}

    def _reindex_groupbuys(self, force: bool = False) -> Dict[str, Any]:
        """Reindex all group buys"""
        try:
            groupbuys_query = self.db.collection(Collections.GROUP_BUYS)
            if not force:
                groupbuys_query = groupbuys_query.where('status', '==', 'Active')

            docs = groupbuys_query.stream()
            count = 0

            for doc in docs:
                groupbuy_data = doc.to_dict()
                groupbuy_data['id'] = doc.id

                # Index group buy (implement group buy indexing)
                self._index_groupbuy(groupbuy_data)
                count += 1

            return {'count': count, 'errors': 0}

        except Exception as e:
            logger.error(f"Error reindexing group buys: {str(e)}")
            return {'count': 0, 'errors': 1}

    def _index_groupbuy(self, groupbuy_data: Dict[str, Any]):
        """Index a single group buy (placeholder for future Algolia integration)"""
        try:
            # For now, this is a placeholder
            # In the future, we could create a separate Algolia index for group buys
            logger.info(f"Group buy {groupbuy_data.get('id')} indexed (placeholder)")

        except Exception as e:
            logger.warning(f"Error indexing group buy: {str(e)}")

    def _add_groupbuy_calculated_fields(self, groupbuy_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add calculated fields to group buy data"""
        try:
            current_quantity = groupbuy_data.get('currentQuantity', 0)
            target_quantity = groupbuy_data.get('targetQuantity', 1)
            expires_at = groupbuy_data.get('expiresAt', 0)
            current_time = int(time.time() * 1000)

            # Progress percentage
            progress_percentage = min(100, (current_quantity / target_quantity) * 100) if target_quantity > 0 else 0
            groupbuy_data['progressPercentage'] = round(progress_percentage, 1)

            # Time remaining
            time_remaining = max(0, expires_at - current_time)
            groupbuy_data['timeRemaining'] = time_remaining

            # Is expiring soon (within 24 hours)
            twenty_four_hours = 24 * 60 * 60 * 1000
            groupbuy_data['isExpiringSoon'] = time_remaining <= twenty_four_hours and time_remaining > 0

            return groupbuy_data

        except Exception as e:
            logger.warning(f"Error adding calculated fields: {str(e)}")
            return groupbuy_data

    def _calculate_progress(self, groupbuy_data: Dict[str, Any]) -> float:
        """Calculate group buy progress percentage"""
        try:
            current_quantity = groupbuy_data.get('currentQuantity', 0)
            target_quantity = groupbuy_data.get('targetQuantity', 1)
            return min(100, (current_quantity / target_quantity) * 100) if target_quantity > 0 else 0
        except Exception as e:
            logger.warning(f"Error calculating progress: {str(e)}")
            return 0

    def _log_admin_action(self, admin_id: str, action: str, details: Dict[str, Any]):
        """Log admin action for audit trail"""
        try:
            log_data = {
                'admin_id': admin_id,
                'action': action,
                'details': details,
                'timestamp': int(time.time() * 1000),
                'service': 'search'
            }

            self.db.collection('adminLogs').add(log_data)

        except Exception as e:
            logger.warning(f"Error logging admin action: {str(e)}")

    def get_search_statistics(self) -> Dict[str, Any]:
        """Get search statistics for admin dashboard"""
        try:
            # Get searches from last 30 days
            thirty_days_ago = int((time.time() - 30 * 24 * 60 * 60) * 1000)

            # Total searches
            total_query = self.db.collection('searchAnalytics')
            total_query = total_query.where('timestamp', '>=', thirty_days_ago)
            total_searches = sum(1 for _ in total_query.stream())

            # Searches by type
            type_counts = {'products': 0, 'groupbuys': 0, 'categories': 0}

            # Average searches per day
            avg_searches_per_day = total_searches / 30 if total_searches > 0 else 0

            # Top search terms (simplified)
            top_terms = self._get_top_search_terms(limit=10)

            return {
                'totalSearches': total_searches,
                'avgSearchesPerDay': round(avg_searches_per_day, 2),
                'searchesByType': type_counts,
                'topSearchTerms': top_terms,
                'period': '30_days'
            }

        except Exception as e:
            logger.error(f"Error getting search statistics: {str(e)}")
            raise

    def _get_top_search_terms(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top search terms"""
        try:
            # This would be implemented with proper aggregation
            # For now, return placeholder data
            return [
                {'term': 'electronics', 'count': 45},
                {'term': 'fashion', 'count': 38},
                {'term': 'books', 'count': 32},
                {'term': 'sports', 'count': 28},
                {'term': 'home', 'count': 25}
            ]

        except Exception as e:
            logger.warning(f"Error getting top search terms: {str(e)}")
            return []
