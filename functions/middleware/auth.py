"""
Authentication middleware for Firebase Cloud Functions
"""
import logging
import functools
from typing import Callable, Any, Dict, Optional
from flask import request, jsonify, g
from config.firebase_config import FirebaseConfig

logger = logging.getLogger(__name__)

class AuthError(Exception):
    """Authentication error exception"""
    def __init__(self, message: str, code: str = "AUTH_ERROR", status_code: int = 401):
        self.message = message
        self.code = code
        self.status_code = status_code
        super().__init__(self.message)

class AuthMiddleware:
    """Authentication middleware class"""
    
    @staticmethod
    def extract_token_from_request() -> Optional[str]:
        """Extract authentication token from request"""
        # Check Authorization header
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            return auth_header.split(' ')[1]
        
        # Check query parameter (for development/testing)
        token = request.args.get('token')
        if token:
            return token
        
        # Check form data
        token = request.form.get('token')
        if token:
            return token
        
        # Check JSON body
        if request.is_json:
            json_data = request.get_json(silent=True)
            if json_data and 'token' in json_data:
                return json_data['token']
        
        return None
    
    @staticmethod
    def verify_token(token: str) -> Dict[str, Any]:
        """Verify Firebase ID token"""
        try:
            decoded_token = FirebaseConfig.verify_id_token(token)
            return decoded_token
        except Exception as e:
            logger.error(f"Token verification failed: {str(e)}")
            raise AuthError("Invalid authentication token", "INVALID_TOKEN")
    
    @staticmethod
    def check_admin_privileges(decoded_token: Dict[str, Any]) -> bool:
        """Check if user has admin privileges"""
        return FirebaseConfig.is_admin(decoded_token)
    
    @staticmethod
    def check_super_admin_privileges(decoded_token: Dict[str, Any]) -> bool:
        """Check if user has super admin privileges"""
        return FirebaseConfig.is_super_admin(decoded_token)

def auth_required(f: Callable) -> Callable:
    """Decorator to require authentication"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Extract token
            token = AuthMiddleware.extract_token_from_request()
            if not token:
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'MISSING_TOKEN',
                        'message': 'Authentication token is required'
                    }
                }), 401
            
            # Verify token
            decoded_token = AuthMiddleware.verify_token(token)
            
            # Store user info in Flask's g object for use in the request
            g.current_user = decoded_token
            g.user_id = decoded_token.get('uid')
            g.user_email = decoded_token.get('email')
            g.is_admin = AuthMiddleware.check_admin_privileges(decoded_token)
            g.is_super_admin = AuthMiddleware.check_super_admin_privileges(decoded_token)
            
            logger.info(f"Authenticated user: {g.user_id}")
            
            return f(*args, **kwargs)
            
        except AuthError as e:
            logger.warning(f"Authentication failed: {e.message}")
            return jsonify({
                'success': False,
                'error': {
                    'code': e.code,
                    'message': e.message
                }
            }), e.status_code
            
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return jsonify({
                'success': False,
                'error': {
                    'code': 'AUTH_ERROR',
                    'message': 'Authentication failed'
                }
            }), 401
    
    return decorated_function

def admin_required(f: Callable) -> Callable:
    """Decorator to require admin privileges"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Check if user is authenticated (should be called after @auth_required)
            if not hasattr(g, 'current_user'):
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'MISSING_AUTH',
                        'message': 'Authentication required before admin check'
                    }
                }), 401
            
            # Check admin privileges
            if not g.is_admin:
                logger.warning(f"Admin access denied for user: {g.user_id}")
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'INSUFFICIENT_PRIVILEGES',
                        'message': 'Admin privileges required'
                    }
                }), 403
            
            logger.info(f"Admin access granted for user: {g.user_id}")
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Admin check error: {str(e)}")
            return jsonify({
                'success': False,
                'error': {
                    'code': 'ADMIN_CHECK_ERROR',
                    'message': 'Failed to verify admin privileges'
                }
            }), 500
    
    return decorated_function

def super_admin_required(f: Callable) -> Callable:
    """Decorator to require super admin privileges"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Check if user is authenticated (should be called after @auth_required)
            if not hasattr(g, 'current_user'):
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'MISSING_AUTH',
                        'message': 'Authentication required before super admin check'
                    }
                }), 401
            
            # Check super admin privileges
            if not g.is_super_admin:
                logger.warning(f"Super admin access denied for user: {g.user_id}")
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'INSUFFICIENT_PRIVILEGES',
                        'message': 'Super admin privileges required'
                    }
                }), 403
            
            logger.info(f"Super admin access granted for user: {g.user_id}")
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Super admin check error: {str(e)}")
            return jsonify({
                'success': False,
                'error': {
                    'code': 'SUPER_ADMIN_CHECK_ERROR',
                    'message': 'Failed to verify super admin privileges'
                }
            }), 500
    
    return decorated_function

def optional_auth(f: Callable) -> Callable:
    """Decorator for optional authentication (doesn't fail if no token)"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            # Extract token
            token = AuthMiddleware.extract_token_from_request()
            
            if token:
                try:
                    # Verify token if present
                    decoded_token = AuthMiddleware.verify_token(token)
                    
                    # Store user info in Flask's g object
                    g.current_user = decoded_token
                    g.user_id = decoded_token.get('uid')
                    g.user_email = decoded_token.get('email')
                    g.is_admin = AuthMiddleware.check_admin_privileges(decoded_token)
                    g.is_super_admin = AuthMiddleware.check_super_admin_privileges(decoded_token)
                    g.is_authenticated = True
                    
                    logger.info(f"Optional auth - authenticated user: {g.user_id}")
                    
                except Exception as e:
                    logger.warning(f"Optional auth - token verification failed: {str(e)}")
                    g.is_authenticated = False
            else:
                g.is_authenticated = False
            
            return f(*args, **kwargs)
            
        except Exception as e:
            logger.error(f"Optional auth error: {str(e)}")
            g.is_authenticated = False
            return f(*args, **kwargs)
    
    return decorated_function

def get_current_user() -> Optional[Dict[str, Any]]:
    """Get current authenticated user from Flask g object"""
    return getattr(g, 'current_user', None)

def get_current_user_id() -> Optional[str]:
    """Get current authenticated user ID"""
    return getattr(g, 'user_id', None)

def is_authenticated() -> bool:
    """Check if current request is authenticated"""
    return getattr(g, 'is_authenticated', False) or hasattr(g, 'current_user')

def is_admin() -> bool:
    """Check if current user is admin"""
    return getattr(g, 'is_admin', False)

def is_super_admin() -> bool:
    """Check if current user is super admin"""
    return getattr(g, 'is_super_admin', False)
