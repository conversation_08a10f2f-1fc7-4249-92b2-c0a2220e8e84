"""
Rate limiting middleware for Firebase Cloud Functions
"""
import os
import time
import logging
import functools
import hashlib
from typing import Callable, Optional, Dict, Any
from flask import request, jsonify, g
import redis
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class RateLimitError(Exception):
    """Rate limit exceeded exception"""
    def __init__(self, message: str, retry_after: int = None):
        self.message = message
        self.retry_after = retry_after
        super().__init__(self.message)

class RateLimiter:
    """Rate limiting implementation using Redis"""
    
    def __init__(self):
        self.redis_client = None
        self._initialize_redis()
    
    def _initialize_redis(self):
        """Initialize Redis connection"""
        try:
            redis_url = os.getenv('REDIS_URL')
            if redis_url:
                self.redis_client = redis.from_url(redis_url, decode_responses=True)
                # Test connection
                self.redis_client.ping()
                logger.info("Redis connection established for rate limiting")
            else:
                logger.warning("Redis URL not configured, rate limiting will use in-memory fallback")
                self.redis_client = None
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            self.redis_client = None
    
    def _get_client_identifier(self) -> str:
        """Get unique identifier for the client"""
        # Try to get user ID if authenticated
        user_id = getattr(g, 'user_id', None)
        if user_id:
            return f"user:{user_id}"
        
        # Fall back to IP address
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if client_ip:
            # Hash IP for privacy
            return f"ip:{hashlib.sha256(client_ip.encode()).hexdigest()[:16]}"
        
        return "unknown"
    
    def _get_rate_limit_key(self, identifier: str, endpoint: str, window: str) -> str:
        """Generate rate limit key for Redis"""
        return f"rate_limit:{identifier}:{endpoint}:{window}"
    
    def check_rate_limit(self, 
                        requests_per_minute: int = 60,
                        requests_per_hour: int = 1000,
                        requests_per_day: int = 10000,
                        endpoint: str = None) -> Dict[str, Any]:
        """Check if request is within rate limits"""
        
        if not self.redis_client:
            # If Redis is not available, allow all requests
            logger.warning("Rate limiting disabled - Redis not available")
            return {
                'allowed': True,
                'remaining': requests_per_minute,
                'reset_time': int(time.time()) + 60
            }
        
        try:
            identifier = self._get_client_identifier()
            endpoint = endpoint or request.endpoint or 'unknown'
            current_time = int(time.time())
            
            # Check minute window
            minute_window = current_time // 60
            minute_key = self._get_rate_limit_key(identifier, endpoint, f"minute:{minute_window}")
            
            # Check hour window
            hour_window = current_time // 3600
            hour_key = self._get_rate_limit_key(identifier, endpoint, f"hour:{hour_window}")
            
            # Check day window
            day_window = current_time // 86400
            day_key = self._get_rate_limit_key(identifier, endpoint, f"day:{day_window}")
            
            # Use Redis pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            
            # Increment counters
            pipe.incr(minute_key)
            pipe.expire(minute_key, 120)  # Keep for 2 minutes
            pipe.incr(hour_key)
            pipe.expire(hour_key, 7200)  # Keep for 2 hours
            pipe.incr(day_key)
            pipe.expire(day_key, 172800)  # Keep for 2 days
            
            results = pipe.execute()
            
            minute_count = results[0]
            hour_count = results[2]
            day_count = results[4]
            
            # Check limits
            if minute_count > requests_per_minute:
                reset_time = (minute_window + 1) * 60
                raise RateLimitError(
                    f"Rate limit exceeded: {requests_per_minute} requests per minute",
                    retry_after=reset_time - current_time
                )
            
            if hour_count > requests_per_hour:
                reset_time = (hour_window + 1) * 3600
                raise RateLimitError(
                    f"Rate limit exceeded: {requests_per_hour} requests per hour",
                    retry_after=reset_time - current_time
                )
            
            if day_count > requests_per_day:
                reset_time = (day_window + 1) * 86400
                raise RateLimitError(
                    f"Rate limit exceeded: {requests_per_day} requests per day",
                    retry_after=reset_time - current_time
                )
            
            return {
                'allowed': True,
                'remaining': requests_per_minute - minute_count,
                'reset_time': (minute_window + 1) * 60,
                'hour_remaining': requests_per_hour - hour_count,
                'day_remaining': requests_per_day - day_count
            }
            
        except RateLimitError:
            raise
        except Exception as e:
            logger.error(f"Rate limit check failed: {str(e)}")
            # On error, allow the request
            return {
                'allowed': True,
                'remaining': requests_per_minute,
                'reset_time': int(time.time()) + 60
            }
    
    def get_rate_limit_headers(self, rate_info: Dict[str, Any]) -> Dict[str, str]:
        """Get rate limit headers for response"""
        headers = {}
        
        if 'remaining' in rate_info:
            headers['X-RateLimit-Remaining'] = str(rate_info['remaining'])
        
        if 'reset_time' in rate_info:
            headers['X-RateLimit-Reset'] = str(rate_info['reset_time'])
        
        if 'hour_remaining' in rate_info:
            headers['X-RateLimit-Hour-Remaining'] = str(rate_info['hour_remaining'])
        
        if 'day_remaining' in rate_info:
            headers['X-RateLimit-Day-Remaining'] = str(rate_info['day_remaining'])
        
        return headers

# Global rate limiter instance
rate_limiter = RateLimiter()

def rate_limit(requests_per_minute: int = 60,
               requests_per_hour: int = 1000,
               requests_per_day: int = 10000):
    """Decorator to apply rate limiting to endpoints"""
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # Check rate limit
                rate_info = rate_limiter.check_rate_limit(
                    requests_per_minute=requests_per_minute,
                    requests_per_hour=requests_per_hour,
                    requests_per_day=requests_per_day,
                    endpoint=f.__name__
                )
                
                # Store rate info for response headers
                g.rate_limit_info = rate_info
                
                # Call the original function
                response = f(*args, **kwargs)
                
                # Add rate limit headers to response
                if hasattr(response, 'headers'):
                    headers = rate_limiter.get_rate_limit_headers(rate_info)
                    for key, value in headers.items():
                        response.headers[key] = value
                
                return response
                
            except RateLimitError as e:
                logger.warning(f"Rate limit exceeded for {rate_limiter._get_client_identifier()}: {e.message}")
                
                response_data = {
                    'success': False,
                    'error': {
                        'code': 'RATE_LIMIT_EXCEEDED',
                        'message': e.message
                    }
                }
                
                headers = {'Content-Type': 'application/json'}
                if e.retry_after:
                    headers['Retry-After'] = str(e.retry_after)
                
                return jsonify(response_data), 429, headers
                
            except Exception as e:
                logger.error(f"Rate limiting error: {str(e)}")
                # On error, allow the request to proceed
                return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def strict_rate_limit(requests_per_minute: int = 10,
                     requests_per_hour: int = 100,
                     requests_per_day: int = 1000):
    """Decorator for strict rate limiting (for sensitive endpoints)"""
    return rate_limit(requests_per_minute, requests_per_hour, requests_per_day)

def lenient_rate_limit(requests_per_minute: int = 120,
                      requests_per_hour: int = 5000,
                      requests_per_day: int = 50000):
    """Decorator for lenient rate limiting (for public endpoints)"""
    return rate_limit(requests_per_minute, requests_per_hour, requests_per_day)

# In-memory fallback for when Redis is not available
class InMemoryRateLimiter:
    """Simple in-memory rate limiter as fallback"""
    
    def __init__(self):
        self.requests = {}
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()
    
    def _cleanup_old_entries(self):
        """Remove old entries to prevent memory leaks"""
        current_time = time.time()
        if current_time - self.last_cleanup < self.cleanup_interval:
            return
        
        cutoff_time = current_time - 86400  # Keep last 24 hours
        
        for key in list(self.requests.keys()):
            self.requests[key] = [
                timestamp for timestamp in self.requests[key]
                if timestamp > cutoff_time
            ]
            if not self.requests[key]:
                del self.requests[key]
        
        self.last_cleanup = current_time
    
    def check_rate_limit(self, identifier: str, requests_per_minute: int) -> bool:
        """Check rate limit using in-memory storage"""
        self._cleanup_old_entries()
        
        current_time = time.time()
        minute_ago = current_time - 60
        
        if identifier not in self.requests:
            self.requests[identifier] = []
        
        # Remove requests older than 1 minute
        self.requests[identifier] = [
            timestamp for timestamp in self.requests[identifier]
            if timestamp > minute_ago
        ]
        
        # Check if limit exceeded
        if len(self.requests[identifier]) >= requests_per_minute:
            return False
        
        # Add current request
        self.requests[identifier].append(current_time)
        return True

# Fallback rate limiter
memory_rate_limiter = InMemoryRateLimiter()
