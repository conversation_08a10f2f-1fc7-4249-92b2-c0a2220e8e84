"""
Request validation middleware for Firebase Cloud Functions
"""
import logging
import functools
from typing import Callable, Any, Dict, Optional, Type, Union
from flask import request, jsonify
from marshmallow import Schema, ValidationError, fields
import json

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Validation error exception"""
    def __init__(self, message: str, errors: Dict[str, Any] = None, status_code: int = 400):
        self.message = message
        self.errors = errors or {}
        self.status_code = status_code
        super().__init__(self.message)

class RequestValidator:
    """Request validation utilities"""
    
    @staticmethod
    def validate_json_request(schema: Type[Schema], data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Validate JSON request data against schema"""
        try:
            if data is None:
                if not request.is_json:
                    raise ValidationError("Request must be JSO<PERSON>", {"content_type": "application/json required"})
                data = request.get_json()
            
            if data is None:
                raise ValidationError("Invalid JSON data", {"json": "Valid JSON required"})
            
            # Validate against schema
            schema_instance = schema()
            validated_data = schema_instance.load(data)
            
            return validated_data
            
        except ValidationError as e:
            logger.warning(f"Validation error: {e.message}")
            raise
        except Exception as e:
            logger.error(f"JSON validation error: {str(e)}")
            raise ValidationError("Invalid request data", {"validation": str(e)})
    
    @staticmethod
    def validate_query_params(schema: Type[Schema]) -> Dict[str, Any]:
        """Validate query parameters against schema"""
        try:
            # Convert query parameters to dict
            query_data = request.args.to_dict()
            
            # Validate against schema
            schema_instance = schema()
            validated_data = schema_instance.load(query_data)
            
            return validated_data
            
        except ValidationError as e:
            logger.warning(f"Query validation error: {e.message}")
            raise
        except Exception as e:
            logger.error(f"Query parameter validation error: {str(e)}")
            raise ValidationError("Invalid query parameters", {"validation": str(e)})
    
    @staticmethod
    def validate_path_params(**param_schemas) -> Dict[str, Any]:
        """Validate path parameters"""
        try:
            validated_params = {}
            
            for param_name, schema_field in param_schemas.items():
                param_value = request.view_args.get(param_name)
                
                if param_value is None:
                    raise ValidationError(f"Missing path parameter: {param_name}")
                
                # Validate using marshmallow field
                try:
                    validated_params[param_name] = schema_field.deserialize(param_value)
                except ValidationError as e:
                    raise ValidationError(f"Invalid path parameter {param_name}: {str(e)}")
            
            return validated_params
            
        except ValidationError as e:
            logger.warning(f"Path parameter validation error: {e.message}")
            raise
        except Exception as e:
            logger.error(f"Path parameter validation error: {str(e)}")
            raise ValidationError("Invalid path parameters", {"validation": str(e)})

def validate_json(schema: Type[Schema]):
    """Decorator to validate JSON request body"""
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                validated_data = RequestValidator.validate_json_request(schema)
                
                # Add validated data to kwargs
                kwargs['validated_data'] = validated_data
                
                return f(*args, **kwargs)
                
            except ValidationError as e:
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'VALIDATION_ERROR',
                        'message': e.message,
                        'details': e.errors
                    }
                }), e.status_code
                
            except Exception as e:
                logger.error(f"Validation decorator error: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'VALIDATION_ERROR',
                        'message': 'Request validation failed'
                    }
                }), 400
        
        return decorated_function
    return decorator

def validate_query(schema: Type[Schema]):
    """Decorator to validate query parameters"""
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                validated_params = RequestValidator.validate_query_params(schema)
                
                # Add validated params to kwargs
                kwargs['query_params'] = validated_params
                
                return f(*args, **kwargs)
                
            except ValidationError as e:
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'VALIDATION_ERROR',
                        'message': e.message,
                        'details': e.errors
                    }
                }), e.status_code
                
            except Exception as e:
                logger.error(f"Query validation decorator error: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'VALIDATION_ERROR',
                        'message': 'Query parameter validation failed'
                    }
                }), 400
        
        return decorated_function
    return decorator

def validate_path(**param_schemas):
    """Decorator to validate path parameters"""
    def decorator(f: Callable) -> Callable:
        @functools.wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                validated_params = RequestValidator.validate_path_params(**param_schemas)
                
                # Add validated params to kwargs
                kwargs['path_params'] = validated_params
                
                return f(*args, **kwargs)
                
            except ValidationError as e:
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'VALIDATION_ERROR',
                        'message': e.message,
                        'details': e.errors
                    }
                }), e.status_code
                
            except Exception as e:
                logger.error(f"Path validation decorator error: {str(e)}")
                return jsonify({
                    'success': False,
                    'error': {
                        'code': 'VALIDATION_ERROR',
                        'message': 'Path parameter validation failed'
                    }
                }), 400
        
        return decorated_function
    return decorator

# Common validation schemas
class PaginationSchema(Schema):
    """Schema for pagination parameters"""
    page = fields.Integer(missing=1, validate=lambda x: x >= 1)
    per_page = fields.Integer(missing=20, validate=lambda x: 1 <= x <= 100)
    sort_by = fields.String(missing='created_at')
    sort_order = fields.String(missing='desc', validate=lambda x: x in ['asc', 'desc'])

class SearchSchema(Schema):
    """Schema for search parameters"""
    query = fields.String(required=True, validate=lambda x: len(x.strip()) >= 1)
    category = fields.String(missing=None)
    min_price = fields.Float(missing=None, validate=lambda x: x >= 0)
    max_price = fields.Float(missing=None, validate=lambda x: x >= 0)
    page = fields.Integer(missing=1, validate=lambda x: x >= 1)
    per_page = fields.Integer(missing=20, validate=lambda x: 1 <= x <= 100)

class IDSchema(Schema):
    """Schema for ID validation"""
    id = fields.String(required=True, validate=lambda x: len(x.strip()) > 0)

# Common field validators
def validate_email(email: str) -> bool:
    """Validate email format"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """Validate phone number format"""
    import re
    # Basic phone validation - can be enhanced based on requirements
    pattern = r'^\+?[\d\s\-\(\)]{10,}$'
    return re.match(pattern, phone) is not None

def validate_url(url: str) -> bool:
    """Validate URL format"""
    import re
    pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
    return re.match(pattern, url) is not None

# Custom field types
class EmailField(fields.String):
    """Email validation field"""
    def _validate(self, value, attr, data, **kwargs):
        super()._validate(value, attr, data, **kwargs)
        if value and not validate_email(value):
            raise ValidationError("Invalid email format")

class PhoneField(fields.String):
    """Phone validation field"""
    def _validate(self, value, attr, data, **kwargs):
        super()._validate(value, attr, data, **kwargs)
        if value and not validate_phone(value):
            raise ValidationError("Invalid phone number format")

class URLField(fields.String):
    """URL validation field"""
    def _validate(self, value, attr, data, **kwargs):
        super()._validate(value, attr, data, **kwargs)
        if value and not validate_url(value):
            raise ValidationError("Invalid URL format")
