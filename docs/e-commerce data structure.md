Absolutely. This is the blueprint for your Firestore database. Defining this clearly is the most critical step in ensuring the backend can support the complex workflows we've outlined.

Here are the detailed data models, designed to be implemented as top-level collections in Firestore.

---

## Firestore Data Models: "The Curated Bridge"

This document specifies the schema for each collection in the Firestore database. The structure is designed to be normalized, enabling efficient queries and scalable management of products, suppliers, and order exceptions.

### 1. `users` Collection

Stores public-facing and non-sensitive user data. Private data like addresses are in a subcollection.

*   **Document ID:** Firebase Auth `uid`
*   **Fields:**
    *   `email`: (string) - User's email address.
    *   `displayName`: (string) - User's display name.
    *   `photoURL`: (string) - URL to the user's profile picture.
    *   `createdAt`: (timestamp) - Timestamp of user creation.
    *   `isAdmin`: (boolean) - `true` if the user is an administrator. Used in Firestore Rules for access control. Defaults to `false`.

#### 1.1. Subcollection: `shippingAddresses`

*   **Location:** `users/{userId}/shippingAddresses`
*   **Document ID:** Auto-generated ID
*   **Fields:**
    *   `addressLine1`: (string)
    *   `addressLine2`: (string, optional)
    *   `city`: (string)
    *   `stateOrProvince`: (string)
    *   `postalCode`: (string)
    *   `country`: (string)
    *   `isDefault`: (boolean) - `true` if this is the user's default address.

### 2. `products` Collection

Stores the core, curated product information that is presented to the customer. This is the "what" you are selling.

*   **Document ID:** Auto-generated ID
*   **Fields:**
    *   `name`: (string) - The user-friendly name of the product (e.g., "Handmade Ceramic Matcha Bowl").
    *   `description`: (string) - The "Curator's Note". Your detailed, enticing description of the product.
    *   `coverImageUrl`: (string) - URL to the primary image for the product, stored in Firebase Storage.
    *   `images`: (array of strings) - A list of additional image URLs.
    *   `category`: (string) - The product's category (e.g., "Home Goods", "Kitchenware").
    *   `status`: (string) - Enum: `'Active'`, `'Archived'`, `'Discontinued'`. Controls visibility in the app.
    *   `createdAt`: (timestamp)
    *   `updatedAt`: (timestamp)

### 3. `productVariants` Collection

Represents a specific, purchasable version of a product (e.g., the red, size M version of a t-shirt).

*   **Document ID:** Auto-generated ID
*   **Fields:**
    *   `productId`: (string) - **Reference/ID** to the parent document in the `products` collection.
    *   `sku`: (string) - Your unique, internal Stock Keeping Unit for this specific variant.
    *   `name`: (string) - Descriptive name for the variant (e.g., "Red / Medium").
    *   `attributes`: (map) - A map of variant properties (e.g., `{ "color": "Red", "size": "M" }`).
    *   `myPrice`: (number) - **The price you charge the customer.**
    *   `currency`: (string) - The currency for `myPrice` (e.g., "USD").
    *   `mainImageUrl`: (string, optional) - URL to an image specific to this variant. If null, use `products.coverImageUrl`.
    *   `weight`: (number) - Weight of the item.
    *   `weightUnit`: (string) - e.g., 'g', 'kg', 'lb'.
    *   `virtualStock`: (number) - A non-binding stock number for display purposes (e.g., 999). Actual availability is checked at fulfillment.

### 4. `sourceLinks` Collection

**The heart of your sourcing operation.** Links one of your `productVariants` to an actual supplier listing. A single variant can have multiple source links, prioritized.

*   **Document ID:** Auto-generated ID
*   **Fields:**
    *   `productVariantId`: (string) - **Reference/ID** to the document in the `productVariants` collection.
    *   `sourcePlatform`: (string) - Enum: `'Pinduoduo'`, `'Taobao'`, `'1688.com'`, `'Other'`.
    *   `sourceUrl`: (string) - The direct URL to the supplier's product page.
    *   `sourcePrice`: (number) - The price you pay the supplier.
    *   `sourceCurrency`: (string) - The currency of the `sourcePrice` (e.g., "CNY").
    *   `priority`: (number) - The order in which to check this supplier (1 = primary, 2 = backup, etc.).
    *   `notes`: (string) - Admin-only notes (e.g., "Supplier ships fast," "Check for holiday discounts").
    *   `lastCheckedAt`: (timestamp) - When an admin last manually verified this link and price.

### 5. `groupBuys` Collection

Manages the state of active and completed group-buying campaigns.

*   **Document ID:** Auto-generated ID
*   **Fields:**
    *   `productVariantId`: (string) - **Reference/ID** to the document in `productVariants`.
    *   `targetQuantity`: (number) - The minimum number of participants required for success.
    *   `currentQuantity`: (number) - The current number of participants who have joined.
    *   `groupPrice`: (number) - The discounted price per unit for participants.
    *   `status`: (string) - Enum: `'Active'`, `'Successful'`, `'Failed'`, `'Fulfilled'`.
    *   `expiresAt`: (timestamp) - The deadline for the group buy.
    *   `createdAt`: (timestamp)

### 6. `orders` Collection

Contains all customer orders. Items are in a subcollection for scalability.

*   **Document ID:** Auto-generated ID
*   **Fields:**
    *   `userId`: (string) - **Reference/ID** to the user in the `users` collection.
    *   `orderNumber`: (string) - A human-readable order number (e.g., "CB-1001").
    *   `status`: (string) - Enum: `'Pending Payment'`, `'Processing'`, `'Action Required'`, `'Partially Shipped'`, `'Shipped'`, `'Delivered'`, `'Cancelled'`.
    *   `totalAmount`: (number) - The total amount paid by the customer.
    *   `currency`: (string)
    *   `shippingAddress`: (map) - A copy of the selected shipping address at the time of order.
    *   `createdAt`: (timestamp)

#### 6.1. Subcollection: `orderItems`

*   **Location:** `orders/{orderId}/orderItems`
*   **Document ID:** Auto-generated ID
*   **Fields:**
    *   `productVariantId`: (string) - **Reference/ID** to the `productVariants` document.
    *   `productSnapshot`: (map) - A copy of key product details (`name`, `sku`, `image`) at the time of purchase to protect against future edits.
    *   `quantity`: (number)
    *   `pricePerUnitPaid`: (number) - The exact price the customer paid per unit.
    *   `isGroupBuy`: (boolean) - `true` if this item was part of a group buy.
    *   `fulfillmentStatus`: (string) - Enum: `'Pending Purchase'`, `'Purchased in China'`, `'Awaiting Resolution'`, `'Ready for Int'l Shipping'`, `'Shipped'`.
    *   `sourceLinkUsedId`: (string, nullable) - **Reference/ID** to the `sourceLinks` document used for fulfillment.
    *   `orderIssueId`: (string, nullable) - **Reference/ID** to a document in `orderIssues` if a problem arises.

### 7. `orderIssues` Collection

**Your problem-tracking system.** A dedicated log for every fulfillment exception, creating a clear audit trail.

*   **Document ID:** Auto-generated ID
*   **Fields:**
    *   `orderId`: (string) - **Reference/ID** to the parent `orders` document.
    *   `orderItemId`: (string) - **Reference/ID** to the specific `orderItems` document.
    *   `userId`: (string) - **Reference/ID** to the `users` document.
    *   `issueType`: (string) - Enum: `'OutOfStock'`, `'PriceIncrease'`, `'SupplierQualityIssue'`, `'ShippingDelay'`.
    *   `status`: (string) - Enum: `'PendingCustomerResponse'`, `'CustomerResponded'`, `'Resolved-PriceAccepted'`, `'Resolved-AlternativeAccepted'`, `'Resolved-Cancelled'`, `'AwaitingAdminAction'`.
    *   `details`: (string) - The detailed message from the admin to the customer explaining the issue and options.
    *   `resolution`: (string, nullable) - A summary of how the issue was resolved.
    *   `priceDifference`: (number, nullable) - The monetary difference if the issue is a price increase.
    *   `createdAt`: (timestamp)
    *   `resolvedAt`: (timestamp, nullable)