## Detailed Application Requirements: "The Curated Bridge"

**Document Version:** 1.0
**Date:** October 26, 2023
**Author:** [Your Name/Company Name]

### 1. Introduction

This document provides the detailed functional and non-functional requirements for the "Curated Bridge" application suite. Its purpose is to guide the design, development, and testing phases. All requirements are derived from the **App Vision** document and are designed to build a trusted, service-first platform for sourcing curated products from China.

The application suite consists of three main components:
1.  **iOS Application:** A fully native customer-facing app.
2.  **Android Application:** A fully native customer-facing app.
3.  **Web Platform:** Consisting of a customer-facing responsive website and a comprehensive Admin Dashboard.

### 2. Global Requirements & Design Decisions

These requirements apply across all platforms unless otherwise specified.

**2.1. Technology Stack**
*   **Backend:** Firebase (Firestore, Firebase Authentication, Firebase Functions, Firebase Storage).
*   **iOS App:** Swift with SwiftUI. UI components will adhere to Apple's Human Interface Guidelines.
*   **Android App:** Kotlin with Jetpack Compose. UI components will adhere to Google's Material Design 3 guidelines.
*   **Web App:** A modern JavaScript framework (e.g., React/Next.js or Vue/Nuxt.js) for a fast, single-page application experience.
*   **API Layer:** A well-documented API will be implicitly created via Firebase Functions callable by the clients to ensure business logic is centralized and not duplicated across platforms.

**2.2. Design Philosophy: "Native First"**
The user experience must feel native to each platform.
*   **No Web Views:** The iOS and Android apps will not use web views for core functionality. Navigation, lists, and forms will use native components.
*   **Platform Conventions:** The apps will respect platform-specific navigation patterns (e.g., tab bars on iOS, navigation drawers/bottom navigation on Android), gestures, and notification styles.
*   **Unified Design Language:** A "Curated Bridge Design System" will be established, defining colors, typography, spacing, and iconography. This system will be translated into native components for each platform to ensure brand consistency.

**2.3. User Authentication**
*   Authentication will be managed by Firebase Authentication.
*   Users can sign up/log in via:
    *   Email and Password.
    *   Google Sign-In (all platforms).
    *   Sign in with Apple (iOS and Web).
*   A user profile section will allow users to manage their name, shipping addresses, and payment methods.

### 3. Module: Customer-Facing Application (iOS, Android, Web)

**3.1. Product Discovery & Browsing**
*   **Home Screen:** Must reflect our "curated" philosophy. It will not be an endless grid. It will feature sections like:
    *   Hero banner for promotions or featured collections.
    *   "Curator's Picks" (manually selected products).
    *   "New Arrivals".
    *   "Trending Group Buys".
*   **Category Pages:** Users can browse products by categories defined in the Admin Dashboard.
*   **Search:** A robust search functionality that queries product names and descriptions from the `products` collection.
*   **Product Cards:** List items will display the product's main image, name, and `myPrice`. A special badge will indicate if a "Group Buy" price is available.

**3.2. Product Detail Page (PDP)**
*   **Visuals:** A gallery of high-resolution images and/or video.
*   **Core Information:**
    *   Product Name (`products.name`).
    *   "The Curator's Note": Our custom, trust-building description (`products.description`).
    *   Price (`productVariants.myPrice`): This must update dynamically as the user selects different variants.
*   **Variant Selection:** A user-friendly interface (e.g., color swatches, size buttons) to select a `ProductVariant`. Selections must be validated against available options.
*   **Group Buy Section:** If a `GroupBuy` is active for the selected variant, a distinct section will display:
    *   The discounted `groupPrice`.
    *   A progress bar showing `currentQuantity` vs. `targetQuantity`.
    *   The `expiresAt` date/time.
    *   A clear Call-to-Action (CTA) like "Join Group Buy" instead of "Add to Cart".
*   **Product Attributes:** A section for detailed specifications (e.g., weight, materials) from `productVariants.attributes`.

**3.3. Shopping Cart & Checkout**
*   Standard cart functionality: add, remove, update quantity.
*   The cart must clearly distinguish between regular purchases and group buy commitments.
*   Checkout process will be a multi-step flow: Shipping Address -> Shipping Method -> Payment -> Review & Place Order.
*   Payment integration will be handled via Stripe/Braintree for international credit cards.

**3.4. Order Management ("My Orders")**
*   This is a critical trust-building feature.
*   Users can view a list of their past and current orders.
*   Each order item will have a clear `fulfillmentStatus`.
*   **The "Action Required" Workflow:**
    *   If an `Order.status` is updated to `'Action Required'`, the order must be visually highlighted in the user's order list (e.g., with a red/yellow banner and badge).
    *   The app will trigger a push notification to the user.
    *   Tapping the order will take the user to a special detail view that displays the `details` from the `OrderIssues` document in a clean, readable format.
    *   The user will be presented with clear buttons to make a decision (e.g., "Accept Price Increase," "Choose Alternative," "Cancel Item").
    *   This interaction will be tracked and update the `OrderIssues` document.

### 4. Module: Admin Dashboard (Web Only)

This is the control center for the business. It must be secure, efficient, and data-rich.

**4.1. Dashboard & Analytics**
*   A home screen displaying Key Performance Indicators (KPIs):
    *   Pending Orders, Orders with Issues.
    *   Sales (24h, 7d, 30d).
    *   Most popular products.
    *   `Order Issue Rate` (percentage of items that require intervention).
    *   Data visualizations on supplier performance (which suppliers lead to the most issues).

**4.2. Product Management**
*   Full CRUD (Create, Read, Update, Delete) functionality for `products`, `productVariants`, and `sourceLinks`.
*   The UI must enforce the relational structure. E.g., to add a `sourceLink`, you first select the `productVariant` it belongs to.
*   **Migration Tool:** A dedicated page to facilitate the translation of data from the old Firestore structure to the new, normalized structure as outlined in the Strategy Document.

**4.3. Order Fulfillment**
*   A dedicated section to manage orders, filterable by `status`.
*   An "Order Processing" view that shows an order's items. For each item, it will display the prioritized `SourceLinks` with clickable URLs.
*   **The "Create Issue" Button:** This is the core of the exception-handling workflow. If a product cannot be purchased as expected, the admin clicks this button next to the order item.
    *   A modal/form appears to create a new `OrderIssues` document.
    *   The admin selects an `issueType`, writes a clear `details` message for the customer, and proposes solutions.
    *   Saving this form automatically updates the main `Order.status` to `'Action Required'` and triggers the customer-facing workflow.

**4.4. Group Buy Management**
*   A section to create and monitor `GroupBuys`.
*   Admins can view the progress of active group buys and manually trigger their `status` to 'Successful' or 'Failed' based on the outcome.

### 5. Backend Requirements (Firebase)

*   **Firestore Security Rules:** Must be meticulously configured.
    *   Users can only read/write their own `orders`, `user profile`, etc.
    *   Admins must have a custom claim (`isAdmin: true`) in their Firebase Auth token to access admin-level data.
    *   Public product data is readable by anyone, but write access is restricted to admins.
*   **Firebase Functions:**
    *   **HTTP Callable Functions:** To handle complex actions like placing an order, processing payments, and responding to an `OrderIssue`.
    *   **Trigger Functions:**
        *   On `OrderIssue` creation: Send a push notification to the user.
        *   On `GroupBuy` success/failure: Notify all participants.
        *   Scheduled functions: To clean up expired group buys or run nightly profit calculation reports.

### 6. Non-Functional Requirements

*   **Performance:** All images will be optimized and served via a CDN (Firebase Storage does this automatically). Lazy loading will be implemented on all product lists. Firestore queries must be indexed for performance.
*   **Scalability:** The architecture is designed on serverless components (Firebase) and is inherently scalable.
*   **Security:** In addition to Firestore Rules, all sensitive data (like payment details) will be handled by a PCI-compliant third-party (Stripe/Braintree). Admin access will be strictly controlled.