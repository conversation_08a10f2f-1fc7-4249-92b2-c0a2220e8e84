## Application Screen & Responsibility Matrix

### Part 1: Customer-Facing Application (iOS, Android, Responsive Web)

These screens form the user's direct experience with the brand. The design must be clean, intuitive, and trust-inspiring, with a native feel on each platform.

#### 1.1. Onboarding & Authentication

| Screen Name | Responsibilities | Platform Notes |
| :--- | :--- | :--- |
| **Splash Screen** | Display brand logo while the app initializes and checks the user's authentication state. | - |
| **Welcome / Onboarding** | (Optional but recommended) A series of 2-3 screens that introduce new users to the value proposition: Curation, Full-Service Logistics, and Group Buys. | Utilizes native carousel/pager components. |
| **Sign Up / Log In** | A single, unified screen for user authentication. Provides options for: Email/Password, Google Sign-In, Sign in with Apple. Includes links for "Forgot Password" and to the Terms of Service/Privacy Policy. | Apple Sign-In is mandatory for iOS. |
| **Forgot Password** | A simple form for the user to enter their email to receive a password reset link. | - |

#### 1.2. Product Discovery & Shopping

| Screen Name | Responsibilities | Platform Notes |
| :--- | :--- | :--- |
| **Home Screen** | The app's main entry point. Must be visually engaging and reflect the "curated" philosophy. Displays sections like: Hero Banner, Curator's Picks, New Arrivals, and Trending Group Buys. | This is the main tab/destination. |
| **Product Listing Page (PLP)** | Displays a grid/list of products for a specific category or search result. Must include functionality for sorting and filtering (e.g., by price, popularity). | Utilizes native `CollectionView` (iOS) or `LazyGrid` (Android). |
| **Product Detail Page (PDP)** | The most information-dense screen. Displays all information for a single `Product`. Responsibilities include: Image gallery, product name, price, Curator's Note (description), variant selectors (color/size), dynamically updating the price and availability based on variant selection. **Crucially**, it displays the Group Buy details (progress bar, discount price) if applicable. CTA is either "Add to Cart" or "Join Group Buy". | - |
| **Search** | A dedicated screen with a prominent search bar. May show recent searches or suggested categories. | - |
| **Search Results** | Displays the results from a search query in the same format as the PLP. | - |

#### 1.3. Checkout Flow

| Screen Name | Responsibilities | Platform Notes |
| :--- | :--- | :--- |
| **Shopping Cart** | Displays all items the user has added. Allows users to change quantities or remove items. Clearly shows the subtotal, taxes/fees, and total. Prominent CTA to proceed to checkout. | - |
| **Checkout: Shipping** | Allows the user to select a shipping address from their saved list or add a new one. | - |
| **Checkout: Payment** | A secure form to enter payment information. Integrates with Stripe/Braintree. Offers quick-pay options like Apple Pay or Google Pay. Allows users to select from saved payment methods. | Apple/Google Pay buttons should be displayed prominently. |
| **Checkout: Review** | The final step. Summarizes the entire order: items, shipping address, payment method, and final cost. Requires explicit confirmation from the user ("Place Order"). | - |
| **Order Confirmation** | The "Thank You" screen. Confirms the order was successful and displays the `orderNumber`. Sets user expectations for what happens next (email confirmation, processing time). | - |

#### 1.4. User Account & Order Management

| Screen Name | Responsibilities | Platform Notes |
| :--- | :--- | :--- |
| **Account / Profile** | Central hub for the user. Displays their name and provides navigation to My Orders, Shipping Addresses, Payment Methods, and Settings. | This is a primary tab in the main navigation. |
| **My Orders List** | Lists all of the user's past and current orders, showing the `orderNumber`, date, total amount, and `status`. **Critically**, it must visually highlight any order with the status `'Action Required'`. | - |
| **Order Details** | Shows the complete details of a single order, including the shipping address, payment summary, and a list of all `orderItems` with their individual `fulfillmentStatus`. | - |
| **Order Issue Resolution** | **A core feature screen.** This is a special state of the Order Details screen, triggered when an order's status is `'Action Required'`. It must clearly display the admin's message from the `OrderIssues` document and present simple, actionable buttons for the user to resolve the issue (e.g., "Accept Price Increase," "Cancel Item"). | This screen is key to building trust. UI must be exceptionally clear and reassuring. |
| **Manage Addresses** | Allows for full CRUD (Create, Read, Update, Delete) operations on the user's saved shipping addresses. | - |
| **Settings** | Allows the user to manage notification preferences, contact support, and view legal documents (Terms of Service, Privacy Policy). | - |

---

### Part 2: Admin Dashboard (Web Application Only)

This is the internal mission control for the business. It must be data-rich, efficient, and optimized for operational tasks.

| Screen Name | Responsibilities | Platform Notes |
| :--- | :--- | :--- |
| **Admin Login** | A separate, secure login page for administrators. | Checks for `isAdmin: true` custom claim. |
| **Main Dashboard** | The admin's home screen. Displays high-level KPIs: pending orders, orders requiring action, recent sales volume, top-selling products, and key analytics like the Order Issue Rate. | Data visualization (charts) is key here. |
| **Order Management** | A powerful table displaying all orders. Must have advanced filtering (by status, date range, user) and search. The most important filters are "Processing" and "Action Required". | The primary workbench for daily operations. |
| **Admin Order Details** | A detailed view of a single order. Shows all customer and order info. For each `orderItem`, it displays the prioritized list of `SourceLinks` with clickable URLs. **Crucially**, it features the **"Create Issue"** button next to each item. | This screen directly facilitates the fulfillment workflow. |
| **Create/Edit Order Issue** | A modal or dedicated form triggered by the "Create Issue" button. Allows the admin to select an `issueType` and write the `details` message that will be shown to the customer. Saving this form creates the `OrderIssues` document and updates the order status. | - |
| **Product Management** | A table listing all products. Allows for creating new products and editing/archiving existing ones. | - |
| **Product Create/Edit Form** | A comprehensive form for CRUD operations on a single product. It must allow the admin to manage the base `Product` info, and also add, edit, or remove its associated `ProductVariants` and their `SourceLinks` in a structured way. | This UI must correctly represent the normalized data structure. |
| **Group Buy Management** | A dashboard to create new group buys and monitor the progress of active ones. Allows the admin to manually mark a group buy as `'Successful'` or `'Failed'`. | - |
| **User Management** | A list of all users. Allows admins to view a user's order history or grant/revoke admin privileges. | - |
| **Migration Tool** | A special-purpose page with an input field for an old product ID. On submission, it fetches the old data and populates the "Product Create/Edit Form" for the admin to review and save into the new, normalized structure. | This is a utility screen, essential for the transition phase. |