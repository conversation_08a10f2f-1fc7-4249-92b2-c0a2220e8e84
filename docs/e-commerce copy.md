## E-commerce Sourcing Agent: Application Build Strategy

### Executive Summary

This document outlines the strategy for building a robust e-commerce platform for our "shopping agent" business model. Our core value proposition is not just reselling products from China, but providing a curated selection and expert handling of complex international logistics.

Our primary challenge is managing a volatile supply chain where we don't own the product data. This strategy addresses this by moving from our current single-document data model in Firestore to a more structured, relational-style architecture. This new architecture will enable us to:

1.  Manage multiple suppliers for a single product.
2.  Gracefully handle fulfillment exceptions (out-of-stock, price changes).
3.  Implement group-buy functionality.
4.  Accurately track profitability and operational KPIs.

This will all be managed through a powerful Admin Dashboard, which will serve as the mission control for our business.

### Part 1: Analysis of Current vs. Target State

#### 1.1. Current Data Structure (The "As-Is")

Your current model stores one product per Firestore document.

*   **Strengths:** Simple to understand, fast to load a complete product page.
*   **Weaknesses:**
    *   **Single Point of Failure:** The single `url` field ties one of our products to one specific supplier listing. If that listing goes down, is out of stock, or has a price hike, our entire product offering is broken.
    *   **Difficult to Query:** Answering questions like "Which orders are awaiting customer action?" or "Which supplier has the most out-of-stock issues?" is extremely difficult and inefficient.
    *   **Inflexible:** The `offers` array for group buys is rigid and hard to manage state for (e.g., tracking how many people have joined).

#### 1.2. Target Architecture: A Normalized Firestore Model

We will evolve our data structure within Firestore to mimic a relational database. This gives us the query power of a relational model with the scalability of Firestore. We will use top-level collections for our main data entities.

*   **`products` Collection:** Stores your *curated* product information.
*   **`productVariants` Collection:** Stores the different variations (color, size) of a product.
*   **`sourceLinks` Collection:** **(The Core Solution)** Links a `productVariant` to one or more actual supplier listings on Pinduoduo, Taobao, etc.
*   **`groupBuys` Collection:** Manages active and past group-buy campaigns.
*   **`orders` Collection:** Stores customer orders. We will use a subcollection for order items.
*   **`orderIssues` Collection:** **(The Tracking System)** A dedicated log for every fulfillment problem, linking it to the customer, order, and resolution path.

### Part 2: The Target Firestore Data Structure

Here is the detailed schema for our new Firestore collections.

**1. `products` collection**
   *   `docId`: (auto-generated)
   *   `name`: string
   *   `description`: string
   *   `category`: string
   *   `coverImageUrl`: string
   *   `status`: string ('Active', 'Discontinued')

**2. `productVariants` collection**
   *   `docId`: (auto-generated)
   *   `productId`: string (reference to `products` collection)
   *   `name`: string (e.g., "Red - M")
   *   `sku`: string (your internal SKU)
   *   `myPrice`: number (The price you sell at)
   *   `weight`: number
   *   `weightUnit`: string
   *   `attributes`: map (e.g., `{ "color": "Red", "size": "M" }`)
   *   `mainImageUrl`: string

**3. `sourceLinks` collection (Your Supplier Database)**
   *   `docId`: (auto-generated)
   *   `productVariantId`: string (reference to `productVariants`)
   *   `sourcePlatform`: string ('Pinduoduo', 'Taobao', etc.)
   *   `sourceUrl`: string
   *   `sourcePrice`: number (The price you pay, in CNY)
   *   `currency`: string ('CNY')
   *   `priority`: number (1=primary, 2=backup, etc.)
   *   `notes`: string
   *   `lastCheckedAt`: timestamp

**4. `groupBuys` collection**
   *   `docId`: (auto-generated)
   *   `productVariantId`: string (reference to `productVariants`)
   *   `targetQuantity`: number
   *   `currentQuantity`: number
   *   `groupPrice`: number (The discounted price)
   *   `status`: string ('Active', 'Successful', 'Failed')
   *   `expiresAt`: timestamp

**5. `orders` collection**
   *   `docId`: (auto-generated)
   *   `userId`: string
   *   `status`: string ('Pending Payment', 'Processing', **'Action Required'**, 'Shipped', 'Delivered', 'Cancelled')
   *   `totalAmount`: number
   *   ... (shipping info, etc.)
   *   **Subcollection: `orderItems`**
       *   `docId`: (auto-generated)
       *   `productVariantId`: string
       *   `quantity`: number
       *   `pricePerUnitPaid`: number (The price the customer paid)
       *   `fulfillmentStatus`: string ('Pending Purchase', 'Purchased', 'Awaiting Resolution')

**6. `orderIssues` collection (Your Problem-Solving Hub)**
   *   `docId`: (auto-generated)
   *   `orderId`: string
   *   `orderItemId`: string
   *   `issueType`: string ('OutOfStock', 'PriceIncrease')
   *   `status`: string ('PendingCustomerResponse', 'Resolved-PriceAccepted', 'Resolved-NewProduct', 'Resolved-Cancelled')
   *   `details`: string (Your message to the customer)
   *   `resolution`: string (The outcome)
   *   `createdAt`: timestamp

### Part 3: The Migration & Implementation Plan

This will be a phased approach, managed through the Admin Dashboard we will build.

#### Phase 1: Build the Foundation (Backend & Admin Dashboard)

1.  **Setup Firebase:** Define the new Firestore collection rules based on the structure above.
2.  **Build the Admin Dashboard:** This is your first and most critical tool. Use a framework like React, Vue, or Angular with a UI component library (e.g., MUI, Ant Design).
3.  **Create "New Product" Forms:** The dashboard must allow you to create products *using the new, normalized structure*. This means separate forms/sections for creating a Product, its Variants, and its Source Links.
4.  **Build the Migration Tool:** This is a special page in your Admin Dashboard.
    *   **Input:** It will have a field to enter the Document ID of a product from your *old* data structure.
    *   **Functionality:** On clicking "Translate", the tool will:
        *   Read the old, flat product document from Firestore.
        *   Parse the data:
            *   `name`, `attributes` -> Prefill the `products` form.
            *   `variants` array -> Prefill multiple `productVariants` forms.
            *   `url`, `offers` -> Prefill the `sourceLinks` and `groupBuys` forms.
            *   `customServices` -> This can be added as a special attribute in the `productVariants` map for now, or you can create a new `customServices` collection if it's complex.
    *   **Action:** The tool presents you with the pre-filled forms for the new structure. You review, adjust, and click "Save" to create the new entries in the respective collections.

#### Phase 2: Develop the Customer-Facing Application

1.  **Read-Only App:** Build the mobile/web app to read data *only* from the new, normalized collections. Focus on displaying products, variants, and prices correctly.
2.  **Implement Ordering Logic:** Build the shopping cart and checkout flow. When an order is placed, it must create documents in the `orders` collection and its `orderItems` subcollection.
3.  **Build the "My Orders" Section:** This section is crucial. It must listen for changes to the `Order.status`. If the status changes to `'Action Required'`, the UI must prominently display this and allow the user to click in to see the details from the corresponding `orderIssues` document.
4.  **Implement Group Buys:** Build the UI for joining a group buy, which updates the `currentQuantity` on the relevant `groupBuys` document.

#### Phase 3: Operational Rollout

1.  **Migrate Top Products:** Use your new Migration Tool to translate your top 20-50 selling products into the new structure.
2.  **Go Live:** Launch the new app. All new orders will use the new system.
3.  **"Just-in-Time" Migration:** For any order that comes in for a product still in the old format, your first step will be to use the Migration Tool to convert it before proceeding with fulfillment. This ensures you gradually migrate your entire catalog without massive upfront effort.

### Part 4: Key Workflows in the New System

*   **Fulfillment:** As described in the first response, your admin dashboard will be where you manage orders. You'll check `SourceLinks` by priority, and if there's a problem, you create an `OrderIssue`.
*   **Profit Calculation:** With the new structure, calculating profit is straightforward.
    *   `Revenue = OrderItem.pricePerUnitPaid * quantity`
    *   `Cost = SourceLink.sourcePrice (at time of purchase) + shipping + fees`
    *   You can run a Firebase Function at the end of each month to aggregate this data for all fulfilled orders.
*   **Monitoring & Analytics:** Your Admin Dashboard will have a "Statistics" page. Because your data is now structured, you can easily answer critical questions:
    *   What is our `Order Issue Rate`? (`count(OrderIssues) / count(OrderItems)`)
    *   Which supplier (identified by `SourceLink.sourceUrl`) has the highest `OutOfStock` rate? (Query `OrderIssues` where `issueType` is 'OutOfStock' and group by the `SourceLink` used).
    *   What's our average time to resolve an issue? (Analyze timestamps in the `orderIssues` collection).

This structured approach transforms your business from a reactive one (dealing with problems as they come) to a proactive, data-driven operation. You will have a solid, scalable foundation to grow your business.