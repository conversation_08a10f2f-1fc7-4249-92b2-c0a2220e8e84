# Maomao E-commerce UI Component System Documentation

## Overview

This document provides comprehensive documentation for the Maomao e-commerce Android app UI component system. The components are built using Jetpack Compose and follow Material Design 3 guidelines to create a professional, cohesive user experience.

## Design System Foundation

### Theme Configuration
- **Colors**: Extended color palette with primary, secondary, status, and e-commerce specific colors
- **Typography**: Consistent text styles following Material Design 3 typography scale
- **Spacing**: Systematic spacing tokens for consistent layouts
- **Elevation**: Standardized elevation levels for depth and hierarchy
- **Shapes**: Rounded corner system for consistent visual language

### Key Design Tokens
```kotlin
// Spacing
Spacing.small = 8.dp
Spacing.medium = 16.dp
Spacing.large = 24.dp

// Colors
PinduoduoRed = Color(0xFFE02E24)
PriceRed = Color(0xFFE53E3E)
DiscountGreen = Color(0xFF38A169)
GroupBuyPrimary = Color(0xFFFF6B35)

// Sizes
Size.buttonMedium = 40.dp
Size.iconMedium = 24.dp
Size.productImageMedium = 120.dp
```

## Component Categories

### 1. Button Components

#### PrimaryButton
Primary action button with consistent styling and loading states.

**Usage:**
```kotlin
PrimaryButton(
    text = "Add to Cart",
    onClick = { /* handle click */ },
    size = ButtonSize.Large,
    isLoading = false,
    leadingIcon = Icons.Default.ShoppingCart,
    fullWidth = true
)
```

**Props:**
- `text`: Button text
- `onClick`: Click handler
- `size`: ButtonSize (Small, Medium, Large)
- `isLoading`: Shows loading indicator
- `enabled`: Button enabled state
- `leadingIcon`/`trailingIcon`: Optional icons
- `fullWidth`: Expand to full width

#### E-commerce Specific Buttons
- `AddToCartButton`: Pre-configured for cart actions
- `BuyNowButton`: High-priority purchase button
- `JoinGroupBuyButton`: Group buy participation
- `WishlistButton`: Favorite toggle button

### 2. Input Components

#### MaomaoTextField
Enhanced text field with validation and error handling.

**Usage:**
```kotlin
MaomaoTextField(
    value = email,
    onValueChange = { email = it },
    label = "Email",
    variant = TextFieldVariant.Outlined,
    isError = emailError != null,
    errorMessage = emailError,
    keyboardType = KeyboardType.Email
)
```

**Specialized Text Fields:**
- `PasswordTextField`: With visibility toggle
- `EmailTextField`: Email-specific keyboard
- `PhoneTextField`: Phone number input
- `SearchTextField`: Search functionality
- `MultilineTextField`: For descriptions/reviews

#### Selection Components
- `DropdownSelector`: Dropdown selection
- `LabeledCheckbox`: Checkbox with label
- `LabeledRadioButton`: Radio button with label
- `RadioButtonGroup`: Group of radio buttons
- `CheckboxGroup`: Multi-select checkboxes

### 3. Card Components

#### MaomaoCard
Base card component with consistent styling.

**Usage:**
```kotlin
MaomaoCard(
    variant = CardVariant.Filled,
    onClick = { /* handle click */ }
) {
    // Card content
}
```

**Specialized Cards:**
- `ContentCard`: Title, description, and actions
- `InfoCard`: Icon, title, and description
- `SectionCard`: Grouped content with header
- `ExpandableCard`: Collapsible content

### 4. Image Components

#### MaomaoImage
Enhanced image component with loading, error, and placeholder states.

**Usage:**
```kotlin
MaomaoImage(
    imageUrl = product.imageUrl,
    contentDescription = product.name,
    shape = RoundedCornerShape(8.dp),
    showLoadingIndicator = true,
    onError = { /* handle error */ }
)
```

**Specialized Images:**
- `ProductImage`: Product-specific styling
- `AvatarImage`: Circular user avatars
- `BannerImage`: Promotional banners
- `ThumbnailImage`: Small previews

### 5. Product Components

#### EnhancedProductCard
Modern product card with comprehensive features.

**Usage:**
```kotlin
EnhancedProductCard(
    product = product,
    productVariant = variant,
    onProductClick = { productId -> /* navigate */ },
    onAddToCart = { /* add to cart */ },
    onToggleWishlist = { /* toggle favorite */ },
    groupBuy = groupBuy,
    rating = 4.5f,
    discountPercentage = 20,
    isOnSale = true
)
```

**Features:**
- Product images with badges
- Rating and review display
- Price with discounts
- Group buy progress
- Action buttons
- Wishlist functionality

#### ProductListing
Grid and list view layouts for product collections.

**Usage:**
```kotlin
ProductListing(
    products = productList,
    viewMode = ProductListingViewMode.Grid,
    onProductClick = { /* navigate */ },
    onAddToCart = { /* add to cart */ },
    onToggleWishlist = { /* toggle favorite */ },
    isLoading = false,
    title = "Featured Products"
)
```

### 6. Navigation Components

#### MaomaoBottomNavigation
Enhanced bottom navigation with badge support.

**Usage:**
```kotlin
MaomaoBottomNavigation(
    currentRoute = currentRoute,
    onNavigate = { route -> /* navigate */ },
    cartItemCount = 3,
    items = defaultBottomNavItems
)
```

#### MaomaoTopAppBar
Flexible top app bar with multiple variants.

**Usage:**
```kotlin
MaomaoTopAppBar(
    title = "Products",
    size = AppBarSize.Large,
    navigationIcon = Icons.Default.ArrowBack,
    onNavigationClick = { /* go back */ },
    actions = {
        IconButton(onClick = { /* search */ }) {
            Icon(Icons.Default.Search, "Search")
        }
    }
)
```

### 7. Loading Components

#### EnhancedLoadingIndicator
Customizable loading states.

**Usage:**
```kotlin
EnhancedLoadingIndicator(
    size = Size.iconLarge,
    message = "Loading products...",
    showMessage = true
)
```

**Loading States:**
- `FullScreenLoading`: Overlay loading
- `ProductCardSkeleton`: Shimmer placeholders
- `LoadingDots`: Animated dots
- `ProgressBarWithPercentage`: Progress tracking

### 8. Dialog Components

#### ConfirmationDialog
Confirmation dialogs with customizable actions.

**Usage:**
```kotlin
ConfirmationDialog(
    title = "Delete Item",
    message = "Are you sure you want to remove this item?",
    onConfirm = { /* delete */ },
    onDismiss = { /* cancel */ },
    type = DialogType.Warning,
    isDestructive = true
)
```

**Dialog Types:**
- `AlertMaomaoDialog`: Simple alerts
- `LoadingDialog`: Progress dialogs
- `SelectionDialog`: Option selection
- `InputDialog`: Text input

## Usage Guidelines

### 1. Consistency
- Use design tokens for spacing, colors, and sizes
- Follow established patterns for similar components
- Maintain consistent interaction patterns

### 2. Accessibility
- Provide meaningful content descriptions
- Ensure minimum touch target sizes (48dp)
- Support screen readers and accessibility services
- Use appropriate color contrast ratios

### 3. Performance
- Use lazy loading for large lists
- Implement proper state management
- Optimize image loading with placeholders
- Use skeleton loaders for better perceived performance

### 4. Responsive Design
- Components adapt to different screen sizes
- Use flexible layouts with proper constraints
- Consider landscape and portrait orientations

## Examples

### Complete Product Listing Screen
```kotlin
@Composable
fun ProductListingScreen() {
    Column {
        MaomaoTopAppBar(
            title = "Products",
            size = AppBarSize.Large,
            actions = {
                IconButton(onClick = { /* search */ }) {
                    Icon(Icons.Default.Search, "Search")
                }
            }
        )
        
        ProductListing(
            products = productList,
            viewMode = viewMode,
            onProductClick = { productId -> /* navigate */ },
            onAddToCart = { product -> /* add to cart */ },
            onToggleWishlist = { product -> /* toggle favorite */ },
            isLoading = isLoading,
            title = "Featured Products",
            onFilterClick = { /* show filters */ },
            onSortClick = { /* show sort options */ }
        )
    }
}
```

### Product Detail Actions
```kotlin
@Composable
fun ProductActions() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        AddToCartButton(
            onClick = { /* add to cart */ },
            modifier = Modifier.weight(1f),
            isLoading = isAddingToCart
        )
        
        WishlistButton(
            isFavorited = isFavorited,
            onToggle = { /* toggle wishlist */ }
        )
    }
}
```

## Best Practices

1. **State Management**: Use StateFlow for reactive state updates
2. **Error Handling**: Provide clear error messages and recovery options
3. **Loading States**: Show appropriate loading indicators
4. **Validation**: Implement real-time form validation
5. **Navigation**: Use consistent navigation patterns
6. **Testing**: Write comprehensive tests for components
7. **Documentation**: Keep component documentation up to date

## Component Testing

Each component should include:
- Unit tests for logic
- UI tests for interactions
- Accessibility tests
- Performance tests for complex components

### 9. Shopping Cart Components

#### CartItemCard
Shopping cart item with quantity controls and pricing.

**Usage:**
```kotlin
CartItemCard(
    cartItem = cartItem,
    onQuantityChange = { newQuantity -> /* update quantity */ },
    onRemoveItem = { /* remove item */ },
    isLoading = false
)
```

**Features:**
- Product image and details
- Quantity selector with +/- buttons
- Price display with discounts
- Remove item functionality
- Loading states

#### CartSummary
Order summary with totals and checkout button.

**Usage:**
```kotlin
CartSummary(
    subtotal = 99.99,
    shipping = 5.99,
    tax = 8.50,
    discount = 10.00,
    total = 104.48,
    currency = "USD",
    onCheckout = { /* proceed to checkout */ },
    promoCode = "SAVE10",
    onApplyPromoCode = { code -> /* apply promo */ }
)
```

### 10. User Profile Components

#### ProfileHeader
User profile header with avatar and basic information.

**Usage:**
```kotlin
ProfileHeader(
    user = user,
    onEditProfile = { /* edit profile */ },
    showEditButton = true
)
```

#### ProfileStats
Statistics display for user activity.

**Usage:**
```kotlin
ProfileStats(
    totalOrders = 25,
    totalReviews = 12,
    averageRating = 4.5f,
    totalSavings = 150.00,
    currency = "USD"
)
```

#### ProfileMenuItem
Menu item for profile navigation.

**Usage:**
```kotlin
ProfileMenuItem(
    title = "My Orders",
    subtitle = "View order history",
    icon = Icons.Default.ShoppingBag,
    onClick = { /* navigate to orders */ },
    badge = "3"
)
```

### 11. Authentication Components

#### LoginForm
Complete login form with validation.

**Usage:**
```kotlin
LoginForm(
    email = email,
    password = password,
    onEmailChange = { email = it },
    onPasswordChange = { password = it },
    onLoginClick = { /* perform login */ },
    onForgotPasswordClick = { /* forgot password */ },
    emailError = emailError,
    passwordError = passwordError,
    isLoading = isLoading
)
```

#### RegistrationForm
Registration form with all required fields.

**Usage:**
```kotlin
RegistrationForm(
    fullName = fullName,
    email = email,
    phoneNumber = phoneNumber,
    password = password,
    confirmPassword = confirmPassword,
    onFullNameChange = { fullName = it },
    onEmailChange = { email = it },
    onPhoneNumberChange = { phoneNumber = it },
    onPasswordChange = { password = it },
    onConfirmPasswordChange = { confirmPassword = it },
    onRegisterClick = { /* perform registration */ },
    isLoading = isLoading
)
```

#### SocialLoginSection
Social media login options.

**Usage:**
```kotlin
SocialLoginSection(
    onGoogleLogin = { /* Google login */ },
    onFacebookLogin = { /* Facebook login */ },
    enabled = true,
    isLoading = false
)
```

### 12. Group Buy Components

#### GroupBuyProgressIndicator
Visual progress indicator for group buy participation.

**Usage:**
```kotlin
GroupBuyProgressIndicator(
    currentParticipants = 8,
    maxParticipants = 10,
    showNumbers = true,
    animated = true
)
```

#### GroupBuyCountdown
Countdown timer for group buy expiration.

**Usage:**
```kotlin
GroupBuyCountdown(
    endTime = LocalDateTime.now().plusHours(24),
    onExpired = { /* handle expiration */ }
)
```

#### GroupBuyParticipantList
List of group buy participants.

**Usage:**
```kotlin
GroupBuyParticipantList(
    participants = participantList,
    maxParticipants = 10,
    showAll = false,
    maxVisible = 5
)
```

#### GroupBuyStatusCard
Complete group buy status with join functionality.

**Usage:**
```kotlin
GroupBuyStatusCard(
    groupBuy = groupBuy,
    onJoinClick = { /* join group buy */ },
    isUserParticipating = false,
    isLoading = false
)
```

### 13. Search Components

#### EnhancedSearchBar
Advanced search bar with suggestions and recent searches.

**Usage:**
```kotlin
EnhancedSearchBar(
    query = searchQuery,
    onQueryChange = { searchQuery = it },
    onSearch = { query -> /* perform search */ },
    showSuggestions = true,
    suggestions = suggestionList,
    recentSearches = recentList,
    onSuggestionClick = { suggestion -> /* use suggestion */ }
)
```

#### SearchFiltersPanel
Comprehensive filtering options.

**Usage:**
```kotlin
SearchFiltersPanel(
    filters = currentFilters,
    onFiltersChange = { filters = it },
    onApplyFilters = { /* apply filters */ },
    onClearFilters = { /* clear filters */ },
    categories = categoryList,
    brands = brandList,
    maxPrice = 1000f
)
```

#### SortOptionsSelector
Sort options for search results.

**Usage:**
```kotlin
SortOptionsSelector(
    selectedSortOption = currentSort,
    sortOptions = sortOptionsList,
    onSortOptionSelected = { option -> /* apply sort */ }
)
```

## Complete Component List

### ✅ Implemented Components

**Foundation:**
- Enhanced theme system with Material Design 3
- Comprehensive spacing and sizing tokens
- Extended color palette for e-commerce

**Buttons:**
- PrimaryButton, SecondaryButton, OutlinedButton, TextButton
- AddToCartButton, BuyNowButton, JoinGroupBuyButton
- WishlistButton, QuantityButton, ProductActionButtons

**Inputs:**
- MaomaoTextField with variants (Password, Email, Phone, Search, Multiline)
- DropdownSelector, LabeledCheckbox, LabeledRadioButton
- RadioButtonGroup, CheckboxGroup, SizeSelector, ColorSelector

**Cards:**
- MaomaoCard with variants (Filled, Outlined, Elevated)
- ContentCard, InfoCard, SectionCard, ExpandableCard

**Images:**
- MaomaoImage with loading/error states
- ProductImage, AvatarImage, BannerImage, ThumbnailImage

**Product Components:**
- EnhancedProductCard, CompactProductCard
- ProductListing with grid/list views
- ProductListingLayouts with filtering

**Navigation:**
- MaomaoBottomNavigation with badges
- MaomaoTopAppBar with multiple variants
- HomeAppBar, DetailAppBar, SearchAppBar, ActionAppBar

**Loading:**
- EnhancedLoadingIndicator, FullScreenLoading
- ProductCardSkeleton, ListItemSkeleton, TextSkeleton
- LoadingDots, PulsingLoadingIndicator, Shimmer effects

**Dialogs:**
- ConfirmationDialog, AlertDialog, LoadingDialog
- CustomDialog, SelectionDialog, InputDialog

**Cart Components:**
- CartItemCard, QuantitySelector, CartSummary
- EmptyCartState, CartBadge

**Profile Components:**
- ProfileHeader, ProfileStats, ProfileMenuItem
- SettingsToggleItem, OrderHistoryItem

**Authentication:**
- LoginForm, RegistrationForm, SocialLoginSection
- AuthModeToggle, TermsAndPrivacyText

**Group Buy:**
- GroupBuyProgressIndicator, GroupBuyCountdown
- GroupBuyParticipantList, GroupBuyStatusCard

**Search:**
- EnhancedSearchBar, SearchFiltersPanel
- SortOptionsSelector, SearchResultsHeader

## Future Enhancements

- Dark mode support improvements
- Animation enhancements
- Voice search integration
- AR product preview components
- Social sharing components
- Advanced analytics components
