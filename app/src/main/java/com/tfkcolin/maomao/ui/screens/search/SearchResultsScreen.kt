package com.tfkcolin.maomao.ui.screens.search

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.models.ProductVariant
import com.tfkcolin.maomao.ui.components.ProductCard

@Composable
fun SearchResultsScreen(
    query: String?,
    onNavigateToProductDetail: (productId: String) -> Unit
) {
    // Mock data for search results using the new data models
    val searchResults = remember {
        listOf(
            Product(id = "searchRes1", name = "Vintage Camera", description = "Classic film camera.", coverImageUrl = "url_camera.jpg"),
            Product(id = "searchRes2", name = "Leather Wallet", description = "Handmade leather wallet.", coverImageUrl = "url_wallet.jpg")
        )
    }

    // Mock product variants for displaying prices in ProductCard
    val searchResultVariant1 = remember { ProductVariant(id = "pv_search1", productId = "searchRes1", name = "Standard", myPrice = 30.00, currency = "$") }
    val searchResultVariant2 = remember { ProductVariant(id = "pv_search2", productId = "searchRes2", name = "Brown", myPrice = 60.00, currency = "$") }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item { Text(text = "Search Results Screen for Query: $query", modifier = Modifier.padding(16.dp)) }

        items(searchResults) { product ->
            val variant = when (product.id) {
                "searchRes1" -> searchResultVariant1
                "searchRes2" -> searchResultVariant2
                else -> ProductVariant(id = "default", productId = product.id, name = "Default Variant", myPrice = 0.0, currency = "$") // Provide a default non-null variant
            }
            ProductCard(product = product, productVariant = variant, onProductClick = onNavigateToProductDetail)
        }
    }
}
