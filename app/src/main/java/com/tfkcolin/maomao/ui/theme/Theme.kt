package com.tfkcolin.maomao.ui.theme

import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext

private val DarkColorScheme = darkColorScheme(
    primary = PinduoduoRed,
    onPrimary = Color.White,
    primaryContainer = PinduoduoRedDark,
    onPrimaryContainer = Color.White,
    secondary = PinduoduoYellow,
    onSecondary = Color.Black,
    secondaryContainer = PinduoduoOrange,
    onSecondaryContainer = Color.Black,
    tertiary = SecondaryBlue,
    onTertiary = Color.White,
    tertiaryContainer = SecondaryPurple,
    onTertiaryContainer = Color.White,
    background = DarkBackground,
    onBackground = DarkTextPrimary,
    surface = DarkSurface,
    onSurface = DarkTextPrimary,
    surfaceVariant = DarkSurfaceVariant,
    onSurfaceVariant = DarkTextSecondary,
    outline = DarkOutline,
    outlineVariant = DarkOutlineVariant,
    error = ErrorRed,
    onError = Color.White,
    errorContainer = ErrorRedLight,
    onErrorContainer = Color.Black
)

private val LightColorScheme = lightColorScheme(
    primary = PinduoduoRed,
    onPrimary = Color.White,
    primaryContainer = PinduoduoRedLight,
    onPrimaryContainer = Color.Black,
    secondary = PinduoduoYellow,
    onSecondary = Color.Black,
    secondaryContainer = PinduoduoOrange,
    onSecondaryContainer = Color.Black,
    tertiary = SecondaryBlue,
    onTertiary = Color.White,
    tertiaryContainer = InfoBlueLight,
    onTertiaryContainer = Color.Black,
    background = LightBackground,
    onBackground = TextPrimary,
    surface = LightSurface,
    onSurface = TextPrimary,
    surfaceVariant = LightSurfaceVariant,
    onSurfaceVariant = TextSecondary,
    outline = LightOutline,
    outlineVariant = LightOutlineVariant,
    error = ErrorRed,
    onError = Color.White,
    errorContainer = ErrorRedLight,
    onErrorContainer = Color.Black
)

@Composable
fun MaomaoTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        shapes = Shapes,
        content = content
    )
}