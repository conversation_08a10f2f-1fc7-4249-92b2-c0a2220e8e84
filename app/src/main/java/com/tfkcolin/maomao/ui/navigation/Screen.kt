package com.tfkcolin.maomao.ui.navigation

sealed class Screen(val route: String) {
    // Onboarding & Authentication
    object Splash : Screen("splash_screen")
    object Welcome : Screen("welcome_screen")
    object Auth : Screen("auth_screen") // Unified Sign Up / Log In
    object ForgotPassword : Screen("forgot_password_screen")

    // Product Discovery & Shopping
    object Home : Screen("home_screen")
    object ProductListing : Screen("product_listing_screen/{categoryId}") {
        fun createRoute(categoryId: String) = "product_listing_screen/$categoryId"
    }
    object ProductDetail : Screen("product_detail_screen/{productId}") {
        fun createRoute(productId: String) = "product_detail_screen/$productId"
    }
    object Search : Screen("search_screen")
    object SearchResults : Screen("search_results_screen/{query}") {
        fun createRoute(query: String) = "search_results_screen/$query"
    }

    // Checkout Flow
    object ShoppingCart : Screen("shopping_cart_screen")
    object CheckoutShipping : Screen("checkout_shipping_screen")
    object CheckoutPayment : Screen("checkout_payment_screen")
    object CheckoutReview : Screen("checkout_review_screen")
    object OrderConfirmation : Screen("order_confirmation_screen/{orderNumber}") {
        fun createRoute(orderNumber: String) = "order_confirmation_screen/$orderNumber"
    }

    // User Account & Order Management
    object Account : Screen("account_screen")
    object MyOrders : Screen("my_orders_screen")
    object OrderDetails : Screen("order_details_screen/{orderId}") {
        fun createRoute(orderId: String) = "order_details_screen/$orderId"
    }
    object OrderIssueResolution : Screen("order_issue_resolution_screen/{orderIssueId}") {
        fun createRoute(orderIssueId: String) = "order_issue_resolution_screen/$orderIssueId"
    }
    object ManageAddresses : Screen("manage_addresses_screen")
    object Settings : Screen("settings_screen")
}
