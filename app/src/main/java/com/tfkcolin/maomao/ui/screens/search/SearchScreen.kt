package com.tfkcolin.maomao.ui.screens.search

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.ui.components.SearchBar

@Composable
fun SearchScreen(onNavigateToSearchResults: (query: String) -> Unit) {
    var searchQuery by remember { mutableStateOf("") }

    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = "Search Screen", modifier = Modifier.padding(16.dp))
        SearchBar(
            query = searchQuery,
            onQueryChange = { searchQuery = it },
            onSearch = onNavigateToSearchResults
        )
        // Optional: Display recent searches or suggested categories here
        Text(text = "Recent Searches / Suggested Categories Placeholder", modifier = Modifier.padding(8.dp))
    }
}
