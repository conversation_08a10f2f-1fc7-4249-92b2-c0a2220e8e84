package com.tfkcolin.maomao.ui.components.product

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.GridView
import androidx.compose.material.icons.filled.ViewList
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.GroupBuy
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.models.ProductVariant
import com.tfkcolin.maomao.ui.components.buttons.FilterSortButtons
import com.tfkcolin.maomao.ui.components.LoadingIndicator
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * Product listing view modes
 */
enum class ProductListingViewMode {
    Grid, List
}

/**
 * Data class for product listing item
 */
data class ProductListingItem(
    val product: Product,
    val productVariant: ProductVariant,
    val groupBuy: GroupBuy? = null,
    val isFavorited: Boolean = false,
    val rating: Float? = null,
    val reviewCount: Int = 0,
    val discountPercentage: Int? = null,
    val isOnSale: Boolean = false
)

/**
 * Product listing component with grid and list view support
 */
@Composable
fun ProductListing(
    products: List<ProductListingItem>,
    onProductClick: (productId: String) -> Unit,
    onAddToCart: (ProductListingItem) -> Unit,
    onToggleWishlist: (ProductListingItem) -> Unit,
    modifier: Modifier = Modifier,
    viewMode: ProductListingViewMode = ProductListingViewMode.Grid,
    onViewModeChange: ((ProductListingViewMode) -> Unit)? = null,
    isLoading: Boolean = false,
    loadingItemCount: Int = 6,
    onFilterClick: (() -> Unit)? = null,
    onSortClick: (() -> Unit)? = null,
    hasActiveFilters: Boolean = false,
    title: String? = null,
    subtitle: String? = null,
    contentPadding: PaddingValues = PaddingValues(Spacing.medium)
) {
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        // Header with title and view mode toggle
        if (title != null || onViewModeChange != null) {
            ProductListingHeader(
                title = title,
                subtitle = subtitle,
                viewMode = viewMode,
                onViewModeChange = onViewModeChange,
                modifier = Modifier.padding(horizontal = Spacing.medium)
            )
        }
        
        // Filter and Sort buttons
        if (onFilterClick != null || onSortClick != null) {
            FilterSortButtons(
                onFilterClick = onFilterClick ?: {},
                onSortClick = onSortClick ?: {},
                hasActiveFilters = hasActiveFilters,
                modifier = Modifier.padding(horizontal = Spacing.medium, vertical = Spacing.small)
            )
        }
        
        // Product listing content
        when {
            isLoading -> {
                ProductListingLoading(
                    viewMode = viewMode,
                    itemCount = loadingItemCount,
                    contentPadding = contentPadding
                )
            }
            products.isEmpty() -> {
                EmptyProductListing(
                    modifier = Modifier.fillMaxSize()
                )
            }
            else -> {
                when (viewMode) {
                    ProductListingViewMode.Grid -> {
                        ProductGrid(
                            products = products,
                            onProductClick = onProductClick,
                            onAddToCart = onAddToCart,
                            onToggleWishlist = onToggleWishlist,
                            contentPadding = contentPadding
                        )
                    }
                    ProductListingViewMode.List -> {
                        ProductList(
                            products = products,
                            onProductClick = onProductClick,
                            onAddToCart = onAddToCart,
                            onToggleWishlist = onToggleWishlist,
                            contentPadding = contentPadding
                        )
                    }
                }
            }
        }
    }
}

/**
 * Product listing header with title and view mode toggle
 */
@Composable
private fun ProductListingHeader(
    title: String?,
    subtitle: String?,
    viewMode: ProductListingViewMode,
    onViewModeChange: ((ProductListingViewMode) -> Unit)?,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = Spacing.medium),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            title?.let { titleText ->
                Text(
                    text = titleText,
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
            
            subtitle?.let { subtitleText ->
                Text(
                    text = subtitleText,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        // View mode toggle
        onViewModeChange?.let { onModeChange ->
            Row {
                IconButton(
                    onClick = { onModeChange(ProductListingViewMode.Grid) }
                ) {
                    Icon(
                        imageVector = Icons.Default.GridView,
                        contentDescription = "Grid view",
                        tint = if (viewMode == ProductListingViewMode.Grid) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
                
                IconButton(
                    onClick = { onModeChange(ProductListingViewMode.List) }
                ) {
                    Icon(
                        imageVector = Icons.Default.ViewList,
                        contentDescription = "List view",
                        tint = if (viewMode == ProductListingViewMode.List) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        }
                    )
                }
            }
        }
    }
}

/**
 * Product grid layout
 */
@Composable
private fun ProductGrid(
    products: List<ProductListingItem>,
    onProductClick: (productId: String) -> Unit,
    onAddToCart: (ProductListingItem) -> Unit,
    onToggleWishlist: (ProductListingItem) -> Unit,
    contentPadding: PaddingValues
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        contentPadding = contentPadding,
        horizontalArrangement = Arrangement.spacedBy(Spacing.small),
        verticalArrangement = Arrangement.spacedBy(Spacing.small)
    ) {
        items(products) { item ->
            EnhancedProductCard(
                product = item.product,
                productVariant = item.productVariant,
                onProductClick = onProductClick,
                onAddToCart = { onAddToCart(item) },
                onToggleWishlist = { onToggleWishlist(item) },
                groupBuy = item.groupBuy,
                isFavorited = item.isFavorited,
                rating = item.rating,
                reviewCount = item.reviewCount,
                discountPercentage = item.discountPercentage,
                isOnSale = item.isOnSale
            )
        }
    }
}

/**
 * Product list layout
 */
@Composable
private fun ProductList(
    products: List<ProductListingItem>,
    onProductClick: (productId: String) -> Unit,
    onAddToCart: (ProductListingItem) -> Unit,
    onToggleWishlist: (ProductListingItem) -> Unit,
    contentPadding: PaddingValues
) {
    LazyColumn(
        contentPadding = contentPadding,
        verticalArrangement = Arrangement.spacedBy(Spacing.small)
    ) {
        items(products) { item ->
            CompactProductCard(
                product = item.product,
                productVariant = item.productVariant,
                onProductClick = onProductClick,
                onAddToCart = { onAddToCart(item) },
                onToggleWishlist = { onToggleWishlist(item) },
                groupBuy = item.groupBuy,
                isFavorited = item.isFavorited,
                rating = item.rating,
                reviewCount = item.reviewCount
            )
        }
    }
}

/**
 * Loading state for product listing
 */
@Composable
private fun ProductListingLoading(
    viewMode: ProductListingViewMode,
    itemCount: Int,
    contentPadding: PaddingValues
) {
    when (viewMode) {
        ProductListingViewMode.Grid -> {
            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                contentPadding = contentPadding,
                horizontalArrangement = Arrangement.spacedBy(Spacing.small),
                verticalArrangement = Arrangement.spacedBy(Spacing.small)
            ) {
                items(itemCount) {
                    ProductCardSkeleton()
                }
            }
        }
        ProductListingViewMode.List -> {
            LazyColumn(
                contentPadding = contentPadding,
                verticalArrangement = Arrangement.spacedBy(Spacing.small)
            ) {
                items(itemCount) {
                    CompactProductCardSkeleton()
                }
            }
        }
    }
}

/**
 * Empty state for product listing
 */
@Composable
private fun EmptyProductListing(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            Text(
                text = "No products found",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "Try adjusting your filters or search terms",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * Skeleton loading card for grid view
 */
@Composable
private fun ProductCardSkeleton() {
    // Implementation would use shimmer effect
    LoadingIndicator()
}

/**
 * Skeleton loading card for list view
 */
@Composable
private fun CompactProductCardSkeleton() {
    // Implementation would use shimmer effect
    LoadingIndicator()
}
