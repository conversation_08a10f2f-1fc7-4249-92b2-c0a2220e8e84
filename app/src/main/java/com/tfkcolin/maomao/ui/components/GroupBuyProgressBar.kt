package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.GroupBuy
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * A reusable composable that visually represents the progress of a group buy campaign.
 * It is primarily used on the Product Detail Page.
 *
 * @param groupBuy The GroupBuy data model containing all necessary information.
 *
 * Usage:
 * ```
 * GroupBuyProgressBar(
 *     groupBuy = GroupBuy(
 *         id = "gb1",
 *         productVariantId = "pv1",
 *         targetQuantity = 10,
 *         currentQuantity = 5,
 *         groupPrice = 15.99,
 *         status = "Active",
 *         expiresAt = System.currentTimeMillis() + 86400000
 *     )
 * )
 * ```
 */
@Composable
fun GroupBuyProgressBar(groupBuy: GroupBuy) {
    val progress = if (groupBuy.targetQuantity > 0) {
        groupBuy.currentQuantity.toFloat() / groupBuy.targetQuantity.toFloat()
    } else {
        0f
    }

    val dateFormatter = remember { SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault()) }
    val expiryDate = remember(groupBuy.expiresAt) {
        if (groupBuy.expiresAt > 0) dateFormatter.format(Date(groupBuy.expiresAt)) else "N/A"
    }

    Column(modifier = Modifier.padding(8.dp)) {
        Text(text = "Group Buy Progress:")
        LinearProgressIndicator(
            progress = progress,
            modifier = Modifier.fillMaxWidth()
        )
        Text(text = "Joined: ${groupBuy.currentQuantity} / ${groupBuy.targetQuantity}")
        Text(text = "Group Price: ${groupBuy.groupPrice}") // Assuming currency is handled elsewhere or added to GroupBuy model
        Text(text = "Expires: $expiryDate")
        Text(text = "Status: ${groupBuy.status}")
    }
}
