package com.tfkcolin.maomao.ui.components.groupbuy

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Group
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.GroupBuy
import com.tfkcolin.maomao.ui.components.StatusBadge
import com.tfkcolin.maomao.ui.components.StatusType
import com.tfkcolin.maomao.ui.components.buttons.JoinGroupBuyButton
import com.tfkcolin.maomao.ui.components.cards.MaomaoCard
import com.tfkcolin.maomao.ui.components.images.AvatarImage
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.DiscountGreen
import com.tfkcolin.maomao.ui.theme.GroupBuyPrimary
import com.tfkcolin.maomao.ui.theme.GroupBuyProgress
import com.tfkcolin.maomao.ui.theme.GroupBuySecondary
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing
import kotlinx.coroutines.delay
import java.time.Duration
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

/**
 * Group buy progress indicator with visual progress bar
 */
@Composable
fun GroupBuyProgressIndicator(
    currentParticipants: Int,
    maxParticipants: Int,
    modifier: Modifier = Modifier,
    showNumbers: Boolean = true,
    animated: Boolean = true
) {
    val progress = (currentParticipants.toFloat() / maxParticipants.toFloat()).coerceIn(0f, 1f)
    val isComplete = currentParticipants >= maxParticipants
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.small)
    ) {
        if (showNumbers) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Group Buy Progress",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "$currentParticipants / $maxParticipants",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = if (isComplete) DiscountGreen else GroupBuyPrimary
                )
            }
        }
        
        // Progress bar
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .clip(RoundedCornerShape(4.dp))
        ) {
            // Background
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(8.dp)
                    .background(MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
            )
            
            // Progress
            Box(
                modifier = Modifier
                    .fillMaxWidth(progress)
                    .height(8.dp)
                    .background(
                        if (isComplete) DiscountGreen else GroupBuyProgress,
                        RoundedCornerShape(4.dp)
                    )
            )
        }
        
        // Status text
        Text(
            text = when {
                isComplete -> "Group buy completed! 🎉"
                progress >= 0.8f -> "Almost there! ${maxParticipants - currentParticipants} more needed"
                progress >= 0.5f -> "Halfway there! ${maxParticipants - currentParticipants} more to go"
                else -> "${maxParticipants - currentParticipants} more participants needed"
            },
            style = MaterialTheme.typography.bodyMedium,
            color = if (isComplete) DiscountGreen else MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * Countdown timer for group buy expiration
 */
@Composable
fun GroupBuyCountdown(
    endTime: LocalDateTime,
    modifier: Modifier = Modifier,
    onExpired: (() -> Unit)? = null
) {
    var timeRemaining by remember { mutableLongStateOf(0L) }
    
    LaunchedEffect(endTime) {
        while (true) {
            val now = LocalDateTime.now()
            val duration = Duration.between(now, endTime)
            timeRemaining = duration.toMillis()
            
            if (timeRemaining <= 0) {
                onExpired?.invoke()
                break
            }
            
            delay(1000) // Update every second
        }
    }
    
    if (timeRemaining > 0) {
        val days = timeRemaining / (24 * 60 * 60 * 1000)
        val hours = (timeRemaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
        val minutes = (timeRemaining % (60 * 60 * 1000)) / (60 * 1000)
        val seconds = (timeRemaining % (60 * 1000)) / 1000
        
        MaomaoCard(
            modifier = modifier.fillMaxWidth(),
            containerColor = GroupBuySecondary.copy(alpha = 0.1f)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(Spacing.medium),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Spacing.small)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(Spacing.small)
                ) {
                    Icon(
                        imageVector = Icons.Default.Schedule,
                        contentDescription = "Time remaining",
                        tint = GroupBuyPrimary,
                        modifier = Modifier.size(Size.iconMedium)
                    )
                    
                    Text(
                        text = "Time Remaining",
                        style = MaterialTheme.typography.titleMedium.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
                ) {
                    if (days > 0) {
                        TimeUnit(value = days, unit = "Days")
                    }
                    if (hours > 0 || days > 0) {
                        TimeUnit(value = hours, unit = "Hours")
                    }
                    TimeUnit(value = minutes, unit = "Min")
                    TimeUnit(value = seconds, unit = "Sec")
                }
            }
        }
    } else {
        MaomaoCard(
            modifier = modifier.fillMaxWidth(),
            containerColor = MaterialTheme.colorScheme.errorContainer
        ) {
            Text(
                text = "Group Buy Expired",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onErrorContainer,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(Spacing.medium)
            )
        }
    }
}

/**
 * Time unit display for countdown
 */
@Composable
private fun TimeUnit(
    value: Long,
    unit: String,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = String.format("%02d", value),
            style = MaterialTheme.typography.headlineSmall.copy(
                fontWeight = FontWeight.Bold
            ),
            color = GroupBuyPrimary
        )
        
        Text(
            text = unit,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * Empty participant slot
 */
@Composable
private fun EmptyParticipantSlot(
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        Box(
            modifier = Modifier
                .size(Size.avatarSmall)
                .clip(CircleShape)
                .background(MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = "Empty slot",
                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f),
                modifier = Modifier.size(Size.iconSmall)
            )
        }
        
        Text(
            text = "Waiting for participant...",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
            modifier = Modifier.weight(1f)
        )
    }
}