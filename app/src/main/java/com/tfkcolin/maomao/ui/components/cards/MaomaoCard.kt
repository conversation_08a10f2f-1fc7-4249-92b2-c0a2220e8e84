package com.tfkcolin.maomao.ui.components.cards

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExpandLess
import androidx.compose.material.icons.filled.ExpandMore
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.Elevation
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * Card variants for different use cases
 */
enum class CardVariant {
    Filled,      // Standard filled card with elevation
    Outlined,    // Outlined card with border
    Elevated     // Card with higher elevation
}

/**
 * Base card component with consistent styling
 */
@Composable
fun MaomaoCard(
    modifier: Modifier = Modifier,
    variant: CardVariant = CardVariant.Filled,
    onClick: (() -> Unit)? = null,
    enabled: Boolean = true,
    shape: Shape = RoundedCornerShape(BorderRadius.card),
    elevation: Dp = when (variant) {
        CardVariant.Filled -> Elevation.card
        CardVariant.Outlined -> Elevation.none
        CardVariant.Elevated -> Elevation.cardHovered
    },
    border: BorderStroke = if (variant == CardVariant.Outlined) {
        BorderStroke(1.dp, MaterialTheme.colorScheme.outline)
    } else BorderStroke(0.dp, Color.Transparent),
    containerColor: Color = MaterialTheme.colorScheme.surface,
    contentColor: Color = MaterialTheme.colorScheme.onSurface,
    content: @Composable ColumnScope.() -> Unit
) {
    when (variant) {
        CardVariant.Outlined -> {
            OutlinedCard(
                modifier = modifier.then(
                    if (onClick != null) Modifier.clickable(enabled = enabled) { onClick() }
                    else Modifier
                ),
                shape = shape,
                border = border,
                colors = CardDefaults.outlinedCardColors(
                    containerColor = containerColor,
                    contentColor = contentColor
                ),
                content = content
            )
        }
        else -> {
            Card(
                modifier = modifier.then(
                    if (onClick != null) Modifier.clickable(enabled = enabled) { onClick() }
                    else Modifier
                ),
                shape = shape,
                elevation = CardDefaults.cardElevation(defaultElevation = elevation),
                colors = CardDefaults.cardColors(
                    containerColor = containerColor,
                    contentColor = contentColor
                ),
                content = content
            )
        }
    }
}

/**
 * Content card with title and description
 */
@Composable
fun ContentCard(
    title: String,
    modifier: Modifier = Modifier,
    description: String? = null,
    variant: CardVariant = CardVariant.Filled,
    onClick: (() -> Unit)? = null,
    enabled: Boolean = true,
    actions: (@Composable RowScope.() -> Unit)? = null,
    content: (@Composable ColumnScope.() -> Unit)? = null
) {
    MaomaoCard(
        modifier = modifier,
        variant = variant,
        onClick = onClick,
        enabled = enabled
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.cardPadding),
            verticalArrangement = Arrangement.spacedBy(Spacing.small)
        ) {
            // Header
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.onSurface
            )

            description?.let { desc ->
                Text(
                    text = desc,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // Custom content
            content?.invoke(this)
            
            // Actions
            actions?.let { actionContent ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End,
                    content = actionContent
                )
            }
        }
    }
}

/**
 * Information card with icon support
 */
@Composable
fun InfoCard(
    title: String,
    description: String,
    modifier: Modifier = Modifier,
    variant: CardVariant = CardVariant.Filled,
    onClick: (() -> Unit)? = null,
    enabled: Boolean = true,
    icon: (@Composable () -> Unit)? = null,
    containerColor: Color = MaterialTheme.colorScheme.surface,
    contentColor: Color = MaterialTheme.colorScheme.onSurface
) {
    MaomaoCard(
        modifier = modifier,
        variant = variant,
        onClick = onClick,
        enabled = enabled,
        containerColor = containerColor,
        contentColor = contentColor
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.cardPadding),
            horizontalArrangement = Arrangement.spacedBy(Spacing.medium),
            verticalAlignment = Alignment.CenterVertically
        ) {
            icon?.invoke()
            
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = contentColor
                )

                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = contentColor.copy(alpha = 0.7f)
                )
            }
        }
    }
}

/**
 * Section card for grouping related content
 */
@Composable
fun SectionCard(
    title: String,
    modifier: Modifier = Modifier,
    variant: CardVariant = CardVariant.Filled,
    headerActions: (@Composable RowScope.() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    MaomaoCard(
        modifier = modifier,
        variant = variant
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            // Header
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(Spacing.cardPadding),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleLarge,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                headerActions?.invoke(this)
            }
            
            // Content
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Spacing.cardPadding)
                    .padding(bottom = Spacing.cardPadding),
                content = content
            )
        }
    }
}

/**
 * Container for grouping multiple cards or content
 */
@Composable
fun CardContainer(
    modifier: Modifier = Modifier,
    spacing: Dp = Spacing.medium,
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(spacing),
        content = content
    )
}

/**
 * Expandable card that can show/hide content
 */
@Composable
fun ExpandableCard(
    title: String,
    isExpanded: Boolean,
    onToggleExpanded: () -> Unit,
    modifier: Modifier = Modifier,
    variant: CardVariant = CardVariant.Filled,
    description: String? = null,
    expandedContent: @Composable ColumnScope.() -> Unit
) {
    MaomaoCard(
        modifier = modifier,
        variant = variant,
        onClick = onToggleExpanded
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.cardPadding)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    description?.let { desc ->
                        Text(
                            text = desc,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }

                Icon(
                    imageVector = if (isExpanded) {
                        Icons.Default.ExpandLess
                    } else {
                        Icons.Default.ExpandMore
                    },
                    contentDescription = if (isExpanded) "Collapse" else "Expand"
                )
            }
            
            if (isExpanded) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = Spacing.medium),
                    content = expandedContent
                )
            }
        }
    }
}
