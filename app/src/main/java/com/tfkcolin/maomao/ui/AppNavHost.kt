package com.tfkcolin.maomao.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.tfkcolin.maomao.data.repository.AuthRepository
import com.tfkcolin.maomao.ui.navigation.Screen
import com.tfkcolin.maomao.ui.screens.about.AboutScreen
import com.tfkcolin.maomao.ui.screens.account.AccountScreen
import com.tfkcolin.maomao.ui.screens.account.ManageAddressesScreen
import com.tfkcolin.maomao.ui.screens.account.MyOrdersScreen
import com.tfkcolin.maomao.ui.screens.account.OrderDetailsScreen
import com.tfkcolin.maomao.ui.screens.account.OrderIssueResolutionScreen
import com.tfkcolin.maomao.ui.screens.account.SettingsScreen
import com.tfkcolin.maomao.ui.screens.auth.AuthScreen
import com.tfkcolin.maomao.ui.screens.auth.AuthViewModel
import com.tfkcolin.maomao.ui.screens.auth.ForgotPasswordScreen
import com.tfkcolin.maomao.ui.screens.checkout.CheckoutPaymentScreen
import com.tfkcolin.maomao.ui.screens.checkout.CheckoutReviewScreen
import com.tfkcolin.maomao.ui.screens.checkout.CheckoutShippingScreen
import com.tfkcolin.maomao.ui.screens.checkout.OrderConfirmationScreen
import com.tfkcolin.maomao.ui.screens.checkout.ShoppingCartScreen
import com.tfkcolin.maomao.ui.screens.home.HomeScreen
import com.tfkcolin.maomao.ui.screens.productdetail.ProductDetailScreen
import com.tfkcolin.maomao.ui.screens.productlisting.ProductListingScreen
import com.tfkcolin.maomao.ui.screens.search.SearchScreen
import com.tfkcolin.maomao.ui.screens.search.SearchResultsScreen
import com.tfkcolin.maomao.ui.screens.splash.SplashScreen
import com.tfkcolin.maomao.ui.screens.welcome.WelcomeScreen

@Composable
fun AppNavHost(
    navController: NavHostController,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = Screen.Splash.route,
        modifier = modifier
    ) {
        composable(Screen.Splash.route) {
            val authViewModel: AuthViewModel = hiltViewModel()
            val currentUser by authViewModel.currentUser.collectAsState(initial = null)
            SplashScreen(
                onNavigateToAuth = { navController.navigate(Screen.Auth.route) },
                onNavigateToHome = { navController.navigate(Screen.Home.route) },
                currentUser = currentUser
            )
        }
        composable(Screen.Welcome.route) {
            WelcomeScreen(onNavigateToAuth = { navController.navigate(Screen.Auth.route) })
        }
        composable(Screen.Auth.route) {
            val authViewModel: AuthViewModel = hiltViewModel()
            val email by authViewModel.email.collectAsState()
            val password by authViewModel.password.collectAsState()
            val confirmPassword by authViewModel.confirmPassword.collectAsState()
            val isLoading by authViewModel.isLoading.collectAsState()
            val errorMessage by authViewModel.errorMessage.collectAsState()
            val authSuccess by authViewModel.authSuccess.collectAsState()
            var isSignInMode by remember { mutableStateOf(true) }

            AuthScreen(
                email = email,
                password = password,
                confirmPassword = confirmPassword,
                isLoading = isLoading,
                errorMessage = errorMessage,
                authSuccess = authSuccess,
                isSignInMode = isSignInMode,
                onEmailChange = authViewModel::onEmailChange,
                onPasswordChange = authViewModel::onPasswordChange,
                onConfirmPasswordChange = authViewModel::onConfirmPasswordChange,
                onSignInClick = authViewModel::signIn,
                onSignUpClick = authViewModel::signUp,
                onToggleSignInMode = { isSignInMode = !isSignInMode },
                onNavigateToForgotPassword = { navController.navigate(Screen.ForgotPassword.route) },
                onResetAuthSuccess = authViewModel::resetAuthSuccess,
                onResetErrorMessage = authViewModel::resetErrorMessage,
                onNavigateToHome = { navController.navigate(Screen.Home.route) }
            )
        }
        composable(Screen.ForgotPassword.route) {
            val authViewModel: AuthViewModel = hiltViewModel()
            val email by authViewModel.email.collectAsState()
            val isLoading by authViewModel.isLoading.collectAsState()
            val errorMessage by authViewModel.errorMessage.collectAsState()
            val authSuccess by authViewModel.authSuccess.collectAsState()

            ForgotPasswordScreen(
                email = email,
                isLoading = isLoading,
                errorMessage = errorMessage,
                authSuccess = authSuccess,
                onEmailChange = authViewModel::onEmailChange,
                onSendPasswordResetEmail = authViewModel::sendPasswordResetEmail,
                onBackToSignInClick = { navController.popBackStack() },
                onResetAuthSuccess = authViewModel::resetAuthSuccess,
                onResetErrorMessage = authViewModel::resetErrorMessage
            )
        }

        composable(Screen.Home.route) {
            HomeScreen(
                onNavigateToProductListing = { categoryId -> navController.navigate(Screen.ProductListing.createRoute(categoryId)) },
                onNavigateToProductDetail = { productId -> navController.navigate(Screen.ProductDetail.createRoute(productId)) },
                onNavigateToSearch = { navController.navigate(Screen.Search.route) },
                onNavigateToShoppingCart = { navController.navigate(Screen.ShoppingCart.route) },
                onNavigateToAccount = { navController.navigate(Screen.Account.route) }
            )
        }
        composable(Screen.ProductListing.route) { backStackEntry ->
            val categoryId = backStackEntry.arguments?.getString("categoryId")
            ProductListingScreen(
                categoryId = categoryId,
                onNavigateToProductDetail = { productId -> navController.navigate(Screen.ProductDetail.createRoute(productId)) }
            )
        }
        composable(Screen.ProductDetail.route) { backStackEntry ->
            val productId = backStackEntry.arguments?.getString("productId")
            ProductDetailScreen(
                productId = productId,
                onNavigateToShoppingCart = { navController.navigate(Screen.ShoppingCart.route) }
            )
        }
        composable(Screen.Search.route) {
            SearchScreen(onNavigateToSearchResults = { query -> navController.navigate(Screen.SearchResults.createRoute(query)) })
        }
        composable(Screen.SearchResults.route) { backStackEntry ->
            val query = backStackEntry.arguments?.getString("query")
            SearchResultsScreen(
                query = query,
                onNavigateToProductDetail = { productId -> navController.navigate(Screen.ProductDetail.createRoute(productId)) }
            )
        }

        composable(Screen.ShoppingCart.route) {
            ShoppingCartScreen(onProceedToShipping = { navController.navigate(Screen.CheckoutShipping.route) })
        }
        composable(Screen.CheckoutShipping.route) {
            CheckoutShippingScreen(onProceedToPayment = { navController.navigate(Screen.CheckoutPayment.route) })
        }
        composable(Screen.CheckoutPayment.route) {
            CheckoutPaymentScreen(onProceedToReview = { navController.navigate(Screen.CheckoutReview.route) })
        }
        composable(Screen.CheckoutReview.route) {
            CheckoutReviewScreen(onPlaceOrder = { orderNumber -> navController.navigate(Screen.OrderConfirmation.createRoute(orderNumber)) })
        }
        composable(Screen.OrderConfirmation.route) { backStackEntry ->
            val orderNumber = backStackEntry.arguments?.getString("orderNumber")
            OrderConfirmationScreen(
                orderNumber = orderNumber,
                onNavigateToHome = { navController.navigate(Screen.Home.route) },
                onNavigateToMyOrders = { navController.navigate(Screen.MyOrders.route) }
            )
        }

        composable(Screen.Account.route) {
            AccountScreen(
                onNavigateToMyOrders = { navController.navigate(Screen.MyOrders.route) },
                onNavigateToManageAddresses = { navController.navigate(Screen.ManageAddresses.route) },
                onNavigateToSettings = { navController.navigate(Screen.Settings.route) }
            )
        }
        composable(Screen.MyOrders.route) {
            MyOrdersScreen(
                onNavigateToOrderDetails = { orderId -> navController.navigate(Screen.OrderDetails.createRoute(orderId)) },
                onNavigateToOrderIssueResolution = { orderIssueId -> navController.navigate(Screen.OrderIssueResolution.createRoute(orderIssueId)) }
            )
        }
        composable(Screen.OrderDetails.route) { backStackEntry ->
            val orderId = backStackEntry.arguments?.getString("orderId")
            OrderDetailsScreen(
                orderId = orderId,
                onNavigateToOrderIssueResolution = { orderIssueId -> navController.navigate(Screen.OrderIssueResolution.createRoute(orderIssueId)) },
                onNavigateBack = { navController.popBackStack() }
            )
        }
        composable(Screen.OrderIssueResolution.route) { backStackEntry ->
            val orderIssueId = backStackEntry.arguments?.getString("orderIssueId")
            OrderIssueResolutionScreen(
                orderIssueId = orderIssueId,
                onIssueResolved = { navController.popBackStack() }, // Example: go back after resolution
                onNavigateBack = { navController.popBackStack() }
            )
        }
        composable(Screen.ManageAddresses.route) {
            ManageAddressesScreen(onNavigateBackToAccount = { navController.popBackStack() })
        }
        composable(Screen.Settings.route) {
            SettingsScreen(onNavigateBackToAccount = { navController.popBackStack() })
        }

        // Example of existing screen
        composable("about_screen") { AboutScreen() }
    }
}
