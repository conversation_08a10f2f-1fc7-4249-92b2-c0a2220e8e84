package com.tfkcolin.maomao.ui.screens.checkout

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.tfkcolin.maomao.ui.navigation.Screen

@Composable
fun CheckoutPaymentScreen(onProceedToReview: () -> Unit) {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Text(text = "Checkout Payment Screen")
            Button(onClick = onProceedToReview) {
                Text("Proceed to Review")
            }
        }
    }
}
