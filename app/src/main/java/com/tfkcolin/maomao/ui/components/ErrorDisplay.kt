package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

/**
 * A reusable composable that displays user-friendly error messages with an optional retry action.
 *
 * @param message The error message to display to the user.
 * @param onRetry An optional lambda function invoked when the "Retry" button is clicked.
 *   If null, the retry button will not be shown.
 *
 * Usage:
 * ```
 * ErrorDisplay(
 *     message = "Failed to load data. Please try again.",
 *     onRetry = { /* reload data */ }
 * )
 *
 * ErrorDisplay(message = "Invalid input.") // No retry button
 * ```
 */
@Composable
fun ErrorDisplay(message: String, onRetry: (() -> Unit)? = null) {
    Column(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Warning,
            contentDescription = "Error Icon",
            tint = Color.Red,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        Text(
            text = message,
            color = Color.Red,
            modifier = Modifier.padding(bottom = 16.dp)
        )
        onRetry?.let {
            Button(onClick = it) {
                Text("Retry")
            }
        }
    }
}
