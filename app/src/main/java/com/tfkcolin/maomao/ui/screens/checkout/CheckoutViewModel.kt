package com.tfkcolin.maomao.ui.screens.checkout

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.maomao.data.models.ShippingAddress
import com.tfkcolin.maomao.data.repository.CartRepository
import com.tfkcolin.maomao.data.repository.OrderRepository
import com.tfkcolin.maomao.data.repository.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CheckoutViewModel @Inject constructor(
    private val cartRepository: CartRepository,
    private val orderRepository: OrderRepository,
    private val userRepository: UserRepository
) : ViewModel() {

    // Cart data
    val cartItems = cartRepository.cartItems

    // Shipping
    private val _availableAddresses = MutableStateFlow<List<ShippingAddress>>(emptyList())
    val availableAddresses: StateFlow<List<ShippingAddress>> = _availableAddresses.asStateFlow()

    private val _selectedAddress = MutableStateFlow<ShippingAddress?>(null)
    val selectedAddress: StateFlow<ShippingAddress?> = _selectedAddress.asStateFlow()

    // Payment
    private val _selectedPaymentMethod = MutableStateFlow<PaymentMethod?>(null)
    val selectedPaymentMethod: StateFlow<PaymentMethod?> = _selectedPaymentMethod.asStateFlow()

    // Order processing
    private val _isProcessingOrder = MutableStateFlow(false)
    val isProcessingOrder: StateFlow<Boolean> = _isProcessingOrder.asStateFlow()

    private val _orderSuccess = MutableStateFlow<String?>(null)
    val orderSuccess: StateFlow<String?> = _orderSuccess.asStateFlow()

    // Loading and error states
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    // Checkout step
    private val _currentStep = MutableStateFlow(CheckoutStep.SHIPPING)
    val currentStep: StateFlow<CheckoutStep> = _currentStep.asStateFlow()

    enum class CheckoutStep {
        SHIPPING,
        PAYMENT,
        REVIEW,
        CONFIRMATION
    }

    enum class PaymentMethod {
        CREDIT_CARD,
        PAYPAL,
        APPLE_PAY,
        GOOGLE_PAY
    }

    init {
        loadShippingAddresses()
    }

    /**
     * Load user's shipping addresses
     */
    private fun loadShippingAddresses() {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val result = userRepository.getShippingAddresses()
                result.fold(
                    onSuccess = { addresses ->
                        _availableAddresses.value = addresses
                        // Auto-select default address
                        val defaultAddress = addresses.find { it.isDefault }
                        if (defaultAddress != null) {
                            _selectedAddress.value = defaultAddress
                        } else if (addresses.isNotEmpty()) {
                            _selectedAddress.value = addresses.first()
                        }
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to load addresses"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load addresses"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Select shipping address
     */
    fun selectShippingAddress(address: ShippingAddress) {
        _selectedAddress.value = address
    }

    /**
     * Select payment method
     */
    fun selectPaymentMethod(method: PaymentMethod) {
        _selectedPaymentMethod.value = method
    }

    /**
     * Move to next checkout step
     */
    fun nextStep() {
        when (_currentStep.value) {
            CheckoutStep.SHIPPING -> {
                if (validateShippingStep()) {
                    _currentStep.value = CheckoutStep.PAYMENT
                }
            }
            CheckoutStep.PAYMENT -> {
                if (validatePaymentStep()) {
                    _currentStep.value = CheckoutStep.REVIEW
                }
            }
            CheckoutStep.REVIEW -> {
                processOrder()
            }
            CheckoutStep.CONFIRMATION -> {
                // Already at final step
            }
        }
    }

    /**
     * Move to previous checkout step
     */
    fun previousStep() {
        when (_currentStep.value) {
            CheckoutStep.SHIPPING -> {
                // Already at first step
            }
            CheckoutStep.PAYMENT -> {
                _currentStep.value = CheckoutStep.SHIPPING
            }
            CheckoutStep.REVIEW -> {
                _currentStep.value = CheckoutStep.PAYMENT
            }
            CheckoutStep.CONFIRMATION -> {
                // Don't allow going back from confirmation
            }
        }
    }

    /**
     * Validate shipping step
     */
    private fun validateShippingStep(): Boolean {
        if (_selectedAddress.value == null) {
            _errorMessage.value = "Please select a shipping address"
            return false
        }
        return true
    }

    /**
     * Validate payment step
     */
    private fun validatePaymentStep(): Boolean {
        if (_selectedPaymentMethod.value == null) {
            _errorMessage.value = "Please select a payment method"
            return false
        }
        return true
    }

    /**
     * Process the order
     */
    private fun processOrder() {
        val address = _selectedAddress.value
        val paymentMethod = _selectedPaymentMethod.value

        if (address == null || paymentMethod == null) {
            _errorMessage.value = "Missing required information"
            return
        }

        _isProcessingOrder.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val orderItems = cartRepository.convertToOrderItems()
                val totalAmount = cartRepository.getCartTotal()

                val result = orderRepository.createOrder(
                    orderItems = orderItems,
                    totalAmount = totalAmount,
                    shippingAddress = address
                )

                result.fold(
                    onSuccess = { orderId ->
                        // Clear cart after successful order
                        cartRepository.clearCart()
                        _orderSuccess.value = orderId
                        _currentStep.value = CheckoutStep.CONFIRMATION
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to process order"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to process order"
            } finally {
                _isProcessingOrder.value = false
            }
        }
    }

    /**
     * Add new shipping address
     */
    fun addShippingAddress(address: ShippingAddress) {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val result = userRepository.addShippingAddress(address)
                result.fold(
                    onSuccess = { addressId ->
                        // Reload addresses
                        loadShippingAddresses()
                        // Select the newly added address
                        _selectedAddress.value = address.copy(id = addressId)
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to add address"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to add address"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Get order summary
     */
    fun getOrderSummary(): OrderSummary {
        val subtotal = cartRepository.getCartTotal()
        val shipping = calculateShipping()
        val tax = calculateTax()
        val total = subtotal + shipping + tax

        return OrderSummary(
            subtotal = subtotal,
            shipping = shipping,
            tax = tax,
            total = total,
            itemCount = cartRepository.getCartItemCount()
        )
    }

    /**
     * Calculate shipping cost
     */
    private fun calculateShipping(): Double {
        // This would typically be calculated based on items, weight, destination, etc.
        val subtotal = cartRepository.getCartTotal()
        return if (subtotal >= 100.0) 0.0 else 9.99
    }

    /**
     * Calculate tax
     */
    private fun calculateTax(): Double {
        // This would typically be calculated based on shipping address
        // For now, return 0 (tax calculated server-side)
        return 0.0
    }

    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * Reset order success state
     */
    fun resetOrderSuccess() {
        _orderSuccess.value = null
    }

    /**
     * Check if can proceed to next step
     */
    fun canProceedToNextStep(): Boolean {
        return when (_currentStep.value) {
            CheckoutStep.SHIPPING -> _selectedAddress.value != null
            CheckoutStep.PAYMENT -> _selectedPaymentMethod.value != null
            CheckoutStep.REVIEW -> !_isProcessingOrder.value
            CheckoutStep.CONFIRMATION -> false
        }
    }

    /**
     * Get step title
     */
    fun getStepTitle(): String {
        return when (_currentStep.value) {
            CheckoutStep.SHIPPING -> "Shipping Address"
            CheckoutStep.PAYMENT -> "Payment Method"
            CheckoutStep.REVIEW -> "Review Order"
            CheckoutStep.CONFIRMATION -> "Order Confirmed"
        }
    }

    /**
     * Get step progress (1-4)
     */
    fun getStepProgress(): Int {
        return when (_currentStep.value) {
            CheckoutStep.SHIPPING -> 1
            CheckoutStep.PAYMENT -> 2
            CheckoutStep.REVIEW -> 3
            CheckoutStep.CONFIRMATION -> 4
        }
    }

    data class OrderSummary(
        val subtotal: Double,
        val shipping: Double,
        val tax: Double,
        val total: Double,
        val itemCount: Int
    )
}
