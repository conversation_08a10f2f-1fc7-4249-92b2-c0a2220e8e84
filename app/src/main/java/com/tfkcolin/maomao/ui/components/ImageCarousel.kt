package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * A reusable composable that provides a swipeable image gallery.
 * It is primarily intended for the Product Detail Page to display multiple product images.
 *
 * @param imageUrls A list of URLs (strings) pointing to the images to be displayed in the carousel.
 *
 * Usage:
 * ```
 * ImageCarousel(imageUrls = listOf("url1", "url2", "url3"))
 * ```
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ImageCarousel(imageUrls: List<String>) {
    val pagerState = rememberPagerState(pageCount = { imageUrls.size })

    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        HorizontalPager(state = pagerState, modifier = Modifier.fillMaxWidth().height(200.dp)) { page ->
            // Placeholder for actual image loading (e.g., Coil, Glide)
            Text(text = "Image ${page + 1} Placeholder: ${imageUrls[page]}")
        }
        // Optional: Add pager indicators here
        Text(text = "Page ${pagerState.currentPage + 1} of ${imageUrls.size}")
    }
}
