package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.ProductVariant

/**
 * A reusable composable that allows users to select different product variants (e.g., color, size).
 * It is primarily used on the Product Detail Page.
 *
 * @param variants A list of available product variants.
 * @param selectedVariant The currently selected variant. Can be null if no variant is selected.
 * @param onVariantSelected A lambda function invoked when a variant is selected.
 *
 * Usage:
 * ```
 * VariantSelector(
 *     variants = listOf(
 *         ProductVariant(id = "v1", name = "Red S", attributes = mapOf("color" to "Red", "size" to "S")),
 *         ProductVariant(id = "v2", name = "Blue M", attributes = mapOf("color" to "Blue", "size" to "M"))
 *     ),
 *     selectedVariant = currentSelectedVariant,
 *     onVariantSelected = { variant -> /* update selected variant */ }
 * )
 * ```
 */
@Composable
fun VariantSelector(
    variants: List<ProductVariant>,
    selectedVariant: ProductVariant?,
    onVariantSelected: (variant: ProductVariant) -> Unit
) {
    Column(modifier = Modifier.padding(8.dp)) {
        Text(text = "Select Variant:")
        LazyRow {
            items(variants) { variant ->
                Button(
                    onClick = { onVariantSelected(variant) },
                    modifier = Modifier.padding(horizontal = 4.dp)
                ) {
                    Text(text = variant.name)
                }
            }
        }
        Text(text = "Selected: ${selectedVariant?.name ?: "None"}")
    }
}
