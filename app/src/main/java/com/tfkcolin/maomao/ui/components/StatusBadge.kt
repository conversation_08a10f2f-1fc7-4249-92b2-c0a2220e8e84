package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

/**
 * A reusable composable that provides a generic visual indicator for various statuses.
 * Examples include 'Action Required' for orders, or 'Active' for products.
 *
 * @param statusText The text to display within the badge (e.g., "Action Required", "Active").
 * @param type The type of status, which can influence the badge's appearance (e.g., color).
 *   This would typically be an enum like `StatusType.Success`, `StatusType.Warning`, etc.
 *
 * Usage:
 * ```
 * StatusBadge(statusText = "Action Required", type = StatusType.Warning)
 * StatusBadge(statusText = "Delivered", type = StatusType.Success)
 * ```
 */
@Composable
fun StatusBadge(statusText: String, type: StatusType) {
    val backgroundColor = when (type) {
        StatusType.Success -> Color(0xFF4CAF50) // Green
        StatusType.Warning -> Color(0xFFFFC107) // Amber
        StatusType.Error -> Color(0xFFF44336) // Red
        StatusType.Info -> Color(0xFF2196F3) // Blue
        StatusType.Default -> Color(0xFF9E9E9E) // Grey
    }
    val textColor = Color.White // For contrast

    Text(
        text = statusText,
        color = textColor,
        modifier = Modifier
            .background(backgroundColor, RoundedCornerShape(4.dp))
            .padding(horizontal = 8.dp, vertical = 4.dp)
    )
}

/**
 * Enum to define different types of statuses for the [StatusBadge] composable.
 */
enum class StatusType {
    Success, Warning, Error, Info, Default
}
