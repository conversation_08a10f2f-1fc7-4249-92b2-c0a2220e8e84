package com.tfkcolin.maomao.ui.screens.productlisting

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.repository.ProductRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProductListingViewModel @Inject constructor(
    private val productRepository: ProductRepository
) : ViewModel() {

    private val _products = MutableStateFlow<List<Product>>(emptyList())
    val products: StateFlow<List<Product>> = _products.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _categories = MutableStateFlow<List<String>>(emptyList())
    val categories: StateFlow<List<String>> = _categories.asStateFlow()

    private val _selectedCategory = MutableStateFlow<String?>(null)
    val selectedCategory: StateFlow<String?> = _selectedCategory.asStateFlow()

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    private val _sortOption = MutableStateFlow(SortOption.NEWEST)
    val sortOption: StateFlow<SortOption> = _sortOption.asStateFlow()

    enum class SortOption {
        NEWEST,
        OLDEST,
        NAME_ASC,
        NAME_DESC
    }

    init {
        loadCategories()
    }

    /**
     * Load products for a specific category
     */
    fun loadProductsForCategory(categoryId: String?) {
        _selectedCategory.value = categoryId
        if (categoryId.isNullOrEmpty()) {
            loadAllProducts()
        } else {
            loadProductsByCategory(categoryId)
        }
    }

    /**
     * Load all active products
     */
    private fun loadAllProducts() {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            productRepository.getActiveProducts().collect { result ->
                _isLoading.value = false
                result.fold(
                    onSuccess = { productList ->
                        _products.value = applySorting(productList)
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to load products"
                        _products.value = emptyList()
                    }
                )
            }
        }
    }

    /**
     * Load products by category
     */
    private fun loadProductsByCategory(category: String) {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            productRepository.getProductsByCategory(category).collect { result ->
                _isLoading.value = false
                result.fold(
                    onSuccess = { productList ->
                        _products.value = applySorting(productList)
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to load products"
                        _products.value = emptyList()
                    }
                )
            }
        }
    }

    /**
     * Search products
     */
    fun searchProducts(query: String) {
        _searchQuery.value = query
        if (query.isBlank()) {
            // If search is cleared, reload current category
            loadProductsForCategory(_selectedCategory.value)
            return
        }

        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            productRepository.searchProducts(query).collect { result ->
                _isLoading.value = false
                result.fold(
                    onSuccess = { productList ->
                        _products.value = applySorting(productList)
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to search products"
                        _products.value = emptyList()
                    }
                )
            }
        }
    }

    /**
     * Load available categories
     */
    private fun loadCategories() {
        viewModelScope.launch {
            val result = productRepository.getCategories()
            result.fold(
                onSuccess = { categoryList ->
                    _categories.value = categoryList
                },
                onFailure = { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load categories"
                }
            )
        }
    }

    /**
     * Change sort option
     */
    fun changeSortOption(newSortOption: SortOption) {
        _sortOption.value = newSortOption
        _products.value = applySorting(_products.value)
    }

    /**
     * Apply sorting to product list
     */
    private fun applySorting(products: List<Product>): List<Product> {
        return when (_sortOption.value) {
            SortOption.NEWEST -> products.sortedByDescending { it.createdAt }
            SortOption.OLDEST -> products.sortedBy { it.createdAt }
            SortOption.NAME_ASC -> products.sortedBy { it.name }
            SortOption.NAME_DESC -> products.sortedByDescending { it.name }
        }
    }

    /**
     * Refresh products
     */
    fun refreshProducts() {
        if (_searchQuery.value.isNotBlank()) {
            searchProducts(_searchQuery.value)
        } else {
            loadProductsForCategory(_selectedCategory.value)
        }
    }

    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * Clear search
     */
    fun clearSearch() {
        _searchQuery.value = ""
        loadProductsForCategory(_selectedCategory.value)
    }

    /**
     * Get filtered products count
     */
    fun getProductCount(): Int {
        return _products.value.size
    }

    /**
     * Check if currently showing search results
     */
    fun isShowingSearchResults(): Boolean {
        return _searchQuery.value.isNotBlank()
    }

    /**
     * Get current category display name
     */
    fun getCurrentCategoryDisplayName(): String {
        return _selectedCategory.value ?: "All Products"
    }

    /**
     * Check if products are empty and not loading
     */
    fun shouldShowEmptyState(): Boolean {
        return _products.value.isEmpty() && !_isLoading.value && _errorMessage.value == null
    }

    /**
     * Check if should show error state
     */
    fun shouldShowErrorState(): Boolean {
        return _errorMessage.value != null && !_isLoading.value
    }
}
