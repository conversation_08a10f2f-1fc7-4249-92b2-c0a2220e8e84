package com.tfkcolin.maomao.ui.screens.productdetail

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.maomao.data.models.GroupBuy
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.models.ProductVariant
import com.tfkcolin.maomao.data.repository.CartRepository
import com.tfkcolin.maomao.data.repository.GroupBuyRepository
import com.tfkcolin.maomao.data.repository.ProductRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ProductDetailViewModel @Inject constructor(
    private val productRepository: ProductRepository,
    private val groupBuyRepository: GroupBuyRepository,
    private val cartRepository: CartRepository
) : ViewModel() {

    private val _product = MutableStateFlow<Product?>(null)
    val product: StateFlow<Product?> = _product.asStateFlow()

    private val _productVariants = MutableStateFlow<List<ProductVariant>>(emptyList())
    val productVariants: StateFlow<List<ProductVariant>> = _productVariants.asStateFlow()

    private val _selectedVariant = MutableStateFlow<ProductVariant?>(null)
    val selectedVariant: StateFlow<ProductVariant?> = _selectedVariant.asStateFlow()

    private val _availableGroupBuys = MutableStateFlow<List<GroupBuy>>(emptyList())
    val availableGroupBuys: StateFlow<List<GroupBuy>> = _availableGroupBuys.asStateFlow()

    private val _selectedGroupBuy = MutableStateFlow<GroupBuy?>(null)
    val selectedGroupBuy: StateFlow<GroupBuy?> = _selectedGroupBuy.asStateFlow()

    private val _quantity = MutableStateFlow(1)
    val quantity: StateFlow<Int> = _quantity.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _addToCartSuccess = MutableStateFlow(false)
    val addToCartSuccess: StateFlow<Boolean> = _addToCartSuccess.asStateFlow()

    private val _isAddingToCart = MutableStateFlow(false)
    val isAddingToCart: StateFlow<Boolean> = _isAddingToCart.asStateFlow()

    /**
     * Load product details
     */
    fun loadProductDetails(productId: String) {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                // Load product
                val productResult = productRepository.getProductById(productId)
                productResult.fold(
                    onSuccess = { product ->
                        _product.value = product
                        product?.let { loadProductVariants(it.id) }
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to load product"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load product details"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Load product variants
     */
    private suspend fun loadProductVariants(productId: String) {
        val variantsResult = productRepository.getProductVariants(productId)
        variantsResult.fold(
            onSuccess = { variants ->
                _productVariants.value = variants
                // Auto-select first variant if available
                if (variants.isNotEmpty() && _selectedVariant.value == null) {
                    selectVariant(variants.first())
                }
            },
            onFailure = { exception ->
                _errorMessage.value = exception.message ?: "Failed to load product variants"
            }
        )
    }

    /**
     * Select a product variant
     */
    fun selectVariant(variant: ProductVariant) {
        _selectedVariant.value = variant
        _selectedGroupBuy.value = null // Reset group buy selection
        loadGroupBuysForVariant(variant.id)
    }

    /**
     * Load group buys for selected variant
     */
    private fun loadGroupBuysForVariant(variantId: String) {
        viewModelScope.launch {
            val groupBuysResult = groupBuyRepository.getGroupBuysForProductVariant(variantId)
            groupBuysResult.fold(
                onSuccess = { groupBuys ->
                    _availableGroupBuys.value = groupBuys
                },
                onFailure = { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load group buys"
                }
            )
        }
    }

    /**
     * Select a group buy
     */
    fun selectGroupBuy(groupBuy: GroupBuy?) {
        _selectedGroupBuy.value = groupBuy
    }

    /**
     * Update quantity
     */
    fun updateQuantity(newQuantity: Int) {
        if (newQuantity > 0) {
            _quantity.value = newQuantity
        }
    }

    /**
     * Add to cart
     */
    fun addToCart() {
        val currentProduct = _product.value
        val currentVariant = _selectedVariant.value
        
        if (currentProduct == null || currentVariant == null) {
            _errorMessage.value = "Please select a product variant"
            return
        }

        _isAddingToCart.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val isGroupBuy = _selectedGroupBuy.value != null
                val price = if (isGroupBuy) {
                    _selectedGroupBuy.value?.groupPrice ?: currentVariant.myPrice
                } else {
                    currentVariant.myPrice
                }

                val result = cartRepository.addToCart(
                    productVariantId = currentVariant.id,
                    productName = currentProduct.name,
                    productSku = currentVariant.sku,
                    productImageUrl = currentProduct.coverImageUrl,
                    quantity = _quantity.value,
                    pricePerUnit = price,
                    isGroupBuy = isGroupBuy,
                    groupBuyId = _selectedGroupBuy.value?.id,
                    variantAttributes = currentVariant.attributes
                )

                result.fold(
                    onSuccess = {
                        _addToCartSuccess.value = true
                        // If it's a group buy, also join the group buy
                        if (isGroupBuy && _selectedGroupBuy.value != null) {
                            joinGroupBuy(_selectedGroupBuy.value!!.id, _quantity.value)
                        }
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to add to cart"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to add to cart"
            } finally {
                _isAddingToCart.value = false
            }
        }
    }

    /**
     * Join group buy
     */
    private suspend fun joinGroupBuy(groupBuyId: String, quantity: Int) {
        val result = groupBuyRepository.joinGroupBuy(groupBuyId, quantity)
        result.fold(
            onSuccess = {
                // Refresh group buy data to show updated progress
                loadGroupBuysForVariant(_selectedVariant.value?.id ?: "")
            },
            onFailure = { exception ->
                _errorMessage.value = exception.message ?: "Failed to join group buy"
            }
        )
    }

    /**
     * Get current price based on selection
     */
    fun getCurrentPrice(): Double {
        val variant = _selectedVariant.value ?: return 0.0
        return if (_selectedGroupBuy.value != null) {
            _selectedGroupBuy.value?.groupPrice ?: variant.myPrice
        } else {
            variant.myPrice
        }
    }

    /**
     * Get total price for current quantity
     */
    fun getTotalPrice(): Double {
        return getCurrentPrice() * _quantity.value
    }

    /**
     * Check if group buy is selected
     */
    fun isGroupBuySelected(): Boolean {
        return _selectedGroupBuy.value != null
    }

    /**
     * Get group buy progress
     */
    fun getGroupBuyProgress(groupBuy: GroupBuy): Float {
        return groupBuyRepository.getGroupBuyProgress(groupBuy)
    }

    /**
     * Check if group buy is expiring soon
     */
    fun isGroupBuyExpiringSoon(groupBuy: GroupBuy): Boolean {
        return groupBuyRepository.isGroupBuyExpiringSoon(groupBuy)
    }

    /**
     * Get time remaining for group buy
     */
    fun getGroupBuyTimeRemaining(groupBuy: GroupBuy): Long {
        return groupBuyRepository.getTimeRemaining(groupBuy)
    }

    /**
     * Format time remaining for display
     */
    fun formatTimeRemaining(timeInMillis: Long): String {
        val hours = timeInMillis / (1000 * 60 * 60)
        val minutes = (timeInMillis % (1000 * 60 * 60)) / (1000 * 60)
        
        return when {
            hours > 24 -> {
                val days = hours / 24
                "${days}d ${hours % 24}h"
            }
            hours > 0 -> "${hours}h ${minutes}m"
            minutes > 0 -> "${minutes}m"
            else -> "Ending soon"
        }
    }

    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * Reset add to cart success state
     */
    fun resetAddToCartSuccess() {
        _addToCartSuccess.value = false
    }

    /**
     * Check if can add to cart
     */
    fun canAddToCart(): Boolean {
        return _selectedVariant.value != null && !_isAddingToCart.value
    }

    /**
     * Get savings amount if group buy is selected
     */
    fun getSavingsAmount(): Double {
        val variant = _selectedVariant.value ?: return 0.0
        val groupBuy = _selectedGroupBuy.value ?: return 0.0
        return (variant.myPrice - groupBuy.groupPrice) * _quantity.value
    }

    /**
     * Get savings percentage
     */
    fun getSavingsPercentage(): Int {
        val variant = _selectedVariant.value ?: return 0
        val groupBuy = _selectedGroupBuy.value ?: return 0
        if (variant.myPrice <= 0) return 0
        return ((variant.myPrice - groupBuy.groupPrice) / variant.myPrice * 100).toInt()
    }
}
