package com.tfkcolin.maomao.ui.screens.checkout

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.ShippingAddress
import com.tfkcolin.maomao.ui.components.AddressForm
import com.tfkcolin.maomao.ui.components.AddressDisplayCard // Potentially used for listing saved addresses
import com.tfkcolin.maomao.ui.navigation.Screen

@Composable
fun CheckoutShippingScreen(onProceedToPayment: () -> Unit) {
    // Mock data for existing addresses
    val savedAddresses = remember {
        listOf(
            ShippingAddress(id = "addr1", addressLine1 = "123 Main St", city = "Anytown", stateOrProvince = "CA", postalCode = "90210", country = "USA", isDefault = true),
            ShippingAddress(id = "addr2", addressLine1 = "456 Elm St", city = "Otherville", stateOrProvince = "NY", postalCode = "10001", country = "USA")
        )
    }

    // For simplicity, let's assume we're always showing the form for now, or selecting the default
    val selectedAddress = remember { savedAddresses.firstOrNull { it.isDefault } ?: savedAddresses.firstOrNull() }

    Column(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = "Checkout: Shipping", style = MaterialTheme.typography.headlineMedium, modifier = Modifier.padding(bottom = 16.dp))

        Text(text = "Selected Shipping Address:", style = MaterialTheme.typography.titleMedium, modifier = Modifier.padding(bottom = 8.dp))
        selectedAddress?.let {
            AddressDisplayCard(
                address = it,
                onSelect = { /* This card is for display, not selection here */ },
                onEditClick = { /* TODO: Allow editing selected address */ }
            )
        } ?: Text("No address selected. Please add one.")

        Spacer(modifier = Modifier.height(16.dp))

        // Option to add/edit address (could be a button to show AddressForm)
        Button(onClick = { /* TODO: Navigate to ManageAddressesScreen or show AddressForm */ }, modifier = Modifier.fillMaxWidth()) {
            Text("Change / Add Address")
        }
        Spacer(modifier = Modifier.height(16.dp))

        Button(onClick = onProceedToPayment, modifier = Modifier.fillMaxWidth()) {
            Text("Proceed to Payment")
        }
    }
}
