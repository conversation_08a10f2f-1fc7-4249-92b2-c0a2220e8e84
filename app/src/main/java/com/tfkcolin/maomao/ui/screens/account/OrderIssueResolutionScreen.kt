package com.tfkcolin.maomao.ui.screens.account

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.OrderIssue
import com.tfkcolin.maomao.ui.components.StatusBadge
import com.tfkcolin.maomao.ui.components.StatusType

@Composable
fun OrderIssueResolutionScreen(
    orderIssueId: String?,
    onIssueResolved: () -> Unit, // e.g., navigate back to Order Details or My Orders
    onNavigateBack: () -> Unit
) {
    // Mock data for order issue details using the new data model
    val issueDetails = remember {
        OrderIssue(
            id = orderIssueId ?: "defaultIssue",
            orderId = "order123",
            orderItemId = "orderItem456",
            userId = "user789",
            issueType = "PriceIncrease",
            status = "PendingCustomerResponse",
            details = "The supplier increased the price by $5.00. Do you accept or wish to cancel?",
            priceDifference = 5.00,
            createdAt = System.currentTimeMillis() - 3600000 // 1 hour ago
        )
    }

    val issueStatus = issueDetails.status
    val statusType = when (issueStatus) {
        "PendingCustomerResponse" -> StatusType.Warning
        "Resolved-PriceAccepted" -> StatusType.Success
        "Resolved-Cancelled" -> StatusType.Error
        else -> StatusType.Default
    }

    Column(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = "Order Issue Resolution for Issue: ${issueDetails.id}", style = MaterialTheme.typography.headlineMedium, modifier = Modifier.padding(bottom = 16.dp))

        Text(text = "Issue Type: ${issueDetails.issueType}", modifier = Modifier.padding(bottom = 4.dp))
        Text(text = "Details: ${issueDetails.details}", modifier = Modifier.padding(bottom = 8.dp))
        issueDetails.priceDifference?.let {
            Text(text = "Price Difference: $${"%.2f".format(it)}", modifier = Modifier.padding(bottom = 8.dp))
        }

        Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
            Text(text = "Status:", style = MaterialTheme.typography.titleMedium, modifier = Modifier.weight(1f))
            StatusBadge(statusText = issueStatus, type = statusType)
        }
        Spacer(modifier = Modifier.height(16.dp))

        if (issueStatus == "PendingCustomerResponse") {
            Button(onClick = onIssueResolved, modifier = Modifier.fillMaxWidth()) {
                Text("Accept Price Increase (Simulated)")
            }
            Spacer(modifier = Modifier.height(8.dp))
            Button(onClick = onIssueResolved, modifier = Modifier.fillMaxWidth()) {
                Text("Cancel Item (Simulated)")
            }
            Spacer(modifier = Modifier.height(8.dp))
        }

        Button(onClick = onNavigateBack, modifier = Modifier.fillMaxWidth()) {
            Text("Back")
        }
    }
}
