package com.tfkcolin.maomao.ui.screens.checkout

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.maomao.data.repository.CartRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ShoppingCartViewModel @Inject constructor(
    private val cartRepository: CartRepository
) : ViewModel() {

    val cartItems = cartRepository.cartItems
    val isLoading = cartRepository.isLoading

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _isUpdatingItem = MutableStateFlow(false)
    val isUpdatingItem: StateFlow<Boolean> = _isUpdatingItem.asStateFlow()

    private val _showRemoveConfirmation = MutableStateFlow<String?>(null)
    val showRemoveConfirmation: StateFlow<String?> = _showRemoveConfirmation.asStateFlow()

    /**
     * Update item quantity
     */
    fun updateItemQuantity(cartItemId: String, newQuantity: Int) {
        _isUpdatingItem.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val result = cartRepository.updateQuantity(cartItemId, newQuantity)
                result.fold(
                    onSuccess = {
                        // Success handled by flow update
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to update quantity"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to update quantity"
            } finally {
                _isUpdatingItem.value = false
            }
        }
    }

    /**
     * Remove item from cart
     */
    fun removeItem(cartItemId: String) {
        _isUpdatingItem.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val result = cartRepository.removeFromCart(cartItemId)
                result.fold(
                    onSuccess = {
                        _showRemoveConfirmation.value = null
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to remove item"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to remove item"
            } finally {
                _isUpdatingItem.value = false
            }
        }
    }

    /**
     * Show remove confirmation dialog
     */
    fun showRemoveConfirmation(cartItemId: String) {
        _showRemoveConfirmation.value = cartItemId
    }

    /**
     * Hide remove confirmation dialog
     */
    fun hideRemoveConfirmation() {
        _showRemoveConfirmation.value = null
    }

    /**
     * Clear entire cart
     */
    fun clearCart() {
        _isUpdatingItem.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val result = cartRepository.clearCart()
                result.fold(
                    onSuccess = {
                        // Success handled by flow update
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to clear cart"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to clear cart"
            } finally {
                _isUpdatingItem.value = false
            }
        }
    }

    /**
     * Get cart total amount
     */
    fun getCartTotal(): Double {
        return cartRepository.getCartTotal()
    }

    /**
     * Get cart item count
     */
    fun getCartItemCount(): Int {
        return cartRepository.getCartItemCount()
    }

    /**
     * Check if cart has group buy items
     */
    fun hasGroupBuyItems(): Boolean {
        return cartRepository.hasGroupBuyItems()
    }

    /**
     * Get group buy items
     */
    fun getGroupBuyItems(): List<CartRepository.CartItem> {
        return cartRepository.getGroupBuyItems()
    }

    /**
     * Get regular items
     */
    fun getRegularItems(): List<CartRepository.CartItem> {
        return cartRepository.getRegularItems()
    }

    /**
     * Calculate subtotal for regular items
     */
    fun getRegularItemsSubtotal(): Double {
        return getRegularItems().sumOf { it.quantity * it.pricePerUnit }
    }

    /**
     * Calculate subtotal for group buy items
     */
    fun getGroupBuyItemsSubtotal(): Double {
        return getGroupBuyItems().sumOf { it.quantity * it.pricePerUnit }
    }

    /**
     * Calculate estimated shipping cost
     */
    fun getEstimatedShipping(): Double {
        // This would typically be calculated based on items, weight, destination, etc.
        // For now, return a fixed amount
        val itemCount = getCartItemCount()
        return when {
            itemCount == 0 -> 0.0
            getCartTotal() >= 100.0 -> 0.0 // Free shipping over $100
            else -> 9.99
        }
    }

    /**
     * Calculate tax amount
     */
    fun getTaxAmount(): Double {
        // This would typically be calculated based on shipping address
        // For now, return 0 (tax calculated at checkout)
        return 0.0
    }

    /**
     * Get final total including shipping and tax
     */
    fun getFinalTotal(): Double {
        return getCartTotal() + getEstimatedShipping() + getTaxAmount()
    }

    /**
     * Check if cart is empty
     */
    fun isCartEmpty(): Boolean {
        return getCartItemCount() == 0
    }

    /**
     * Check if eligible for free shipping
     */
    fun isEligibleForFreeShipping(): Boolean {
        return getCartTotal() >= 100.0
    }

    /**
     * Get amount needed for free shipping
     */
    fun getAmountNeededForFreeShipping(): Double {
        return maxOf(0.0, 100.0 - getCartTotal())
    }

    /**
     * Validate cart for checkout
     */
    fun validateCartForCheckout(): String? {
        return when {
            isCartEmpty() -> "Cart is empty"
            getCartTotal() <= 0 -> "Invalid cart total"
            else -> null
        }
    }

    /**
     * Prepare cart for checkout
     */
    fun prepareForCheckout(): Boolean {
        val validationError = validateCartForCheckout()
        if (validationError != null) {
            _errorMessage.value = validationError
            return false
        }
        return true
    }

    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * Get cart summary for display
     */
    fun getCartSummary(): CartSummary {
        return CartSummary(
            itemCount = getCartItemCount(),
            subtotal = getCartTotal(),
            shipping = getEstimatedShipping(),
            tax = getTaxAmount(),
            total = getFinalTotal(),
            hasGroupBuyItems = hasGroupBuyItems(),
            isEligibleForFreeShipping = isEligibleForFreeShipping(),
            amountNeededForFreeShipping = getAmountNeededForFreeShipping()
        )
    }

    data class CartSummary(
        val itemCount: Int,
        val subtotal: Double,
        val shipping: Double,
        val tax: Double,
        val total: Double,
        val hasGroupBuyItems: Boolean,
        val isEligibleForFreeShipping: Boolean,
        val amountNeededForFreeShipping: Double
    )
}
