package com.tfkcolin.maomao.ui.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Shapes
import androidx.compose.ui.unit.dp

// Pinduoduo-inspired shapes with rounded corners
val Shapes = Shapes(
    // Small components like chips, small buttons
    small = RoundedCornerShape(4.dp),
    
    // Medium components like cards, dialogs
    medium = RoundedCornerShape(8.dp),
    
    // Large components like bottom sheets, large cards
    large = RoundedCornerShape(12.dp),
    
    // Extra large components (custom extension)
    extraLarge = RoundedCornerShape(24.dp)
)
