package com.tfkcolin.maomao.ui.screens.checkout

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.OrderItem
import com.tfkcolin.maomao.ui.components.OrderItemDisplay
import com.tfkcolin.maomao.ui.components.OrderSummary
import com.tfkcolin.maomao.ui.components.QuantitySelector // Assuming QuantitySelector is used within OrderItemDisplay

@Composable
fun ShoppingCartScreen(onProceedToShipping: () -> Unit) {
    // Mock data for cart items using the new data models
    val cartItems = remember {
        listOf(
            OrderItem(
                id = "cartItem1",
                productVariantId = "pv_cart1",
                productSnapshot = mapOf("name" to "Designer T-Shirt", "sku" to "TSHIRT-DES-M"),
                quantity = 1,
                pricePerUnitPaid = 29.99,
                fulfillmentStatus = "Pending Purchase",
                isGroupBuy = false
            ),
            OrderItem(
                id = "cartItem2",
                productVariantId = "pv_cart2",
                productSnapshot = mapOf("name" to "Smart Home Hub", "sku" to "HUB-SMART-V2"),
                quantity = 1,
                pricePerUnitPaid = 79.99,
                fulfillmentStatus = "Pending Purchase",
                isGroupBuy = true
            )
        )
    }

    // Calculate summary from mock data
    val subtotal = cartItems.sumOf { it.quantity * it.pricePerUnitPaid }
    val taxes = subtotal * 0.10 // 10% tax
    val total = subtotal + taxes

    LazyColumn(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item { Text(text = "Shopping Cart", style = MaterialTheme.typography.headlineMedium, modifier = Modifier.padding(bottom = 16.dp)) }

        items(cartItems) { item ->
            OrderItemDisplay(
                orderItem = item,
                onRemove = { /* TODO: Implement remove item from cart */ },
                onQuantityChange = { newQty -> /* TODO: Update quantity for item */ }
            )
            Spacer(modifier = Modifier.height(8.dp))
            if (item.isGroupBuy) {
                Text(text = "This is a group buy item.", style = MaterialTheme.typography.bodySmall, color = Color.Gray)
                Spacer(modifier = Modifier.height(4.dp))
            }
        }

        item {
            Spacer(modifier = Modifier.height(16.dp))
            OrderSummary(
                subtotal = subtotal,
                taxes = taxes,
                total = total,
                currency = "$"
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(onClick = onProceedToShipping, modifier = Modifier.fillMaxWidth()) {
                Text("Proceed to Shipping")
            }
        }
    }
}
