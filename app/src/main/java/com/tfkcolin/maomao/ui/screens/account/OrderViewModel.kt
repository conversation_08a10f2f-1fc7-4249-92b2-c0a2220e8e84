package com.tfkcolin.maomao.ui.screens.account

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.maomao.data.models.Order
import com.tfkcolin.maomao.data.models.OrderItem
import com.tfkcolin.maomao.data.models.OrderIssue
import com.tfkcolin.maomao.data.repository.OrderRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class OrderViewModel @Inject constructor(
    private val orderRepository: OrderRepository
) : ViewModel() {

    // Orders list
    private val _orders = MutableStateFlow<List<Order>>(emptyList())
    val orders: StateFlow<List<Order>> = _orders.asStateFlow()

    private val _actionRequiredOrders = MutableStateFlow<List<Order>>(emptyList())
    val actionRequiredOrders: StateFlow<List<Order>> = _actionRequiredOrders.asStateFlow()

    // Order details
    private val _selectedOrder = MutableStateFlow<Order?>(null)
    val selectedOrder: StateFlow<Order?> = _selectedOrder.asStateFlow()

    private val _orderItems = MutableStateFlow<List<OrderItem>>(emptyList())
    val orderItems: StateFlow<List<OrderItem>> = _orderItems.asStateFlow()

    private val _orderIssues = MutableStateFlow<List<OrderIssue>>(emptyList())
    val orderIssues: StateFlow<List<OrderIssue>> = _orderIssues.asStateFlow()

    // Order issue resolution
    private val _selectedOrderIssue = MutableStateFlow<OrderIssue?>(null)
    val selectedOrderIssue: StateFlow<OrderIssue?> = _selectedOrderIssue.asStateFlow()

    // Loading and error states
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _isLoadingOrderDetails = MutableStateFlow(false)
    val isLoadingOrderDetails: StateFlow<Boolean> = _isLoadingOrderDetails.asStateFlow()

    private val _isResolvingIssue = MutableStateFlow(false)
    val isResolvingIssue: StateFlow<Boolean> = _isResolvingIssue.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _issueResolutionSuccess = MutableStateFlow(false)
    val issueResolutionSuccess: StateFlow<Boolean> = _issueResolutionSuccess.asStateFlow()

    init {
        loadUserOrders()
        loadActionRequiredOrders()
    }

    /**
     * Load user's orders
     */
    fun loadUserOrders() {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            orderRepository.getUserOrders().collect { result ->
                _isLoading.value = false
                result.fold(
                    onSuccess = { orderList ->
                        _orders.value = orderList
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to load orders"
                        _orders.value = emptyList()
                    }
                )
            }
        }
    }

    /**
     * Load orders with "Action Required" status
     */
    private fun loadActionRequiredOrders() {
        viewModelScope.launch {
            orderRepository.getActionRequiredOrders().collect { result ->
                result.fold(
                    onSuccess = { orderList ->
                        _actionRequiredOrders.value = orderList
                    },
                    onFailure = { exception ->
                        // Don't show error for action required orders as it's secondary data
                    }
                )
            }
        }
    }

    /**
     * Load order details
     */
    fun loadOrderDetails(orderId: String) {
        _isLoadingOrderDetails.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                // Load order
                val orderResult = orderRepository.getOrderById(orderId)
                orderResult.fold(
                    onSuccess = { order ->
                        _selectedOrder.value = order
                        if (order != null) {
                            loadOrderItems(orderId)
                            loadOrderIssues(orderId)
                        }
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to load order details"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load order details"
            } finally {
                _isLoadingOrderDetails.value = false
            }
        }
    }

    /**
     * Load order items
     */
    private suspend fun loadOrderItems(orderId: String) {
        val itemsResult = orderRepository.getOrderItems(orderId)
        itemsResult.fold(
            onSuccess = { items ->
                _orderItems.value = items
            },
            onFailure = { exception ->
                _errorMessage.value = exception.message ?: "Failed to load order items"
            }
        )
    }

    /**
     * Load order issues
     */
    private suspend fun loadOrderIssues(orderId: String) {
        val issuesResult = orderRepository.getOrderIssues(orderId)
        issuesResult.fold(
            onSuccess = { issues ->
                _orderIssues.value = issues
            },
            onFailure = { exception ->
                _errorMessage.value = exception.message ?: "Failed to load order issues"
            }
        )
    }

    /**
     * Load order issue details
     */
    fun loadOrderIssueDetails(issueId: String) {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val result = orderRepository.getOrderIssueById(issueId)
                result.fold(
                    onSuccess = { issue ->
                        _selectedOrderIssue.value = issue
                        // Also load the related order details
                        issue?.orderId?.let { orderId ->
                            loadOrderDetails(orderId)
                        }
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to load order issue"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load order issue"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Respond to order issue
     */
    fun respondToOrderIssue(issueId: String, response: String, action: IssueResolutionAction) {
        _isResolvingIssue.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                val newStatus = when (action) {
                    IssueResolutionAction.ACCEPT_PRICE_INCREASE -> "Resolved-PriceAccepted"
                    IssueResolutionAction.CHOOSE_ALTERNATIVE -> "Resolved-AlternativeAccepted"
                    IssueResolutionAction.CANCEL_ITEM -> "Resolved-Cancelled"
                    IssueResolutionAction.CONTACT_SUPPORT -> "AwaitingAdminAction"
                }

                val result = orderRepository.respondToOrderIssue(issueId, response, newStatus)
                result.fold(
                    onSuccess = {
                        _issueResolutionSuccess.value = true
                        // Refresh order data
                        _selectedOrderIssue.value?.orderId?.let { orderId ->
                            loadOrderDetails(orderId)
                        }
                        // Refresh orders list
                        loadUserOrders()
                        loadActionRequiredOrders()
                    },
                    onFailure = { exception ->
                        _errorMessage.value = exception.message ?: "Failed to respond to issue"
                    }
                )
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to respond to issue"
            } finally {
                _isResolvingIssue.value = false
            }
        }
    }

    /**
     * Get orders by status
     */
    fun getOrdersByStatus(status: String): List<Order> {
        return _orders.value.filter { it.status == status }
    }

    /**
     * Get order status color
     */
    fun getOrderStatusColor(status: String): OrderStatusColor {
        return when (status) {
            "Pending Payment" -> OrderStatusColor.WARNING
            "Processing" -> OrderStatusColor.INFO
            "Action Required" -> OrderStatusColor.ERROR
            "Partially Shipped", "Shipped" -> OrderStatusColor.SUCCESS
            "Delivered" -> OrderStatusColor.SUCCESS
            "Cancelled" -> OrderStatusColor.ERROR
            else -> OrderStatusColor.DEFAULT
        }
    }

    /**
     * Check if order has action required
     */
    fun hasActionRequired(order: Order): Boolean {
        return order.status == "Action Required"
    }

    /**
     * Get pending issues count
     */
    fun getPendingIssuesCount(): Int {
        return _actionRequiredOrders.value.size
    }

    /**
     * Format order date
     */
    fun formatOrderDate(timestamp: Long): String {
        val date = java.util.Date(timestamp)
        val formatter = java.text.SimpleDateFormat("MMM dd, yyyy", java.util.Locale.getDefault())
        return formatter.format(date)
    }

    /**
     * Format currency amount
     */
    fun formatCurrency(amount: Double, currency: String = "USD"): String {
        return when (currency) {
            "USD" -> "$%.2f".format(amount)
            "EUR" -> "€%.2f".format(amount)
            "GBP" -> "£%.2f".format(amount)
            else -> "$currency %.2f".format(amount)
        }
    }

    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * Reset issue resolution success state
     */
    fun resetIssueResolutionSuccess() {
        _issueResolutionSuccess.value = false
    }

    /**
     * Refresh all order data
     */
    fun refreshOrders() {
        loadUserOrders()
        loadActionRequiredOrders()
    }

    /**
     * Check if should show empty state
     */
    fun shouldShowEmptyState(): Boolean {
        return _orders.value.isEmpty() && !_isLoading.value && _errorMessage.value == null
    }

    /**
     * Check if should show error state
     */
    fun shouldShowErrorState(): Boolean {
        return _errorMessage.value != null && !_isLoading.value
    }

    enum class IssueResolutionAction {
        ACCEPT_PRICE_INCREASE,
        CHOOSE_ALTERNATIVE,
        CANCEL_ITEM,
        CONTACT_SUPPORT
    }

    enum class OrderStatusColor {
        DEFAULT,
        SUCCESS,
        WARNING,
        ERROR,
        INFO
    }
}
