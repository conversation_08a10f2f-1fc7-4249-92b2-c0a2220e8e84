package com.tfkcolin.maomao.ui.screens.home

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.maomao.data.models.GroupBuy
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.repository.GroupBuyRepository
import com.tfkcolin.maomao.data.repository.ProductRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class HomeViewModel @Inject constructor(
    private val productRepository: ProductRepository,
    private val groupBuyRepository: GroupBuyRepository
) : ViewModel() {

    private val _featuredProducts = MutableStateFlow<List<Product>>(emptyList())
    val featuredProducts: StateFlow<List<Product>> = _featuredProducts.asStateFlow()

    private val _newArrivals = MutableStateFlow<List<Product>>(emptyList())
    val newArrivals: StateFlow<List<Product>> = _newArrivals.asStateFlow()

    private val _trendingGroupBuys = MutableStateFlow<List<GroupBuy>>(emptyList())
    val trendingGroupBuys: StateFlow<List<GroupBuy>> = _trendingGroupBuys.asStateFlow()

    private val _categories = MutableStateFlow<List<String>>(emptyList())
    val categories: StateFlow<List<String>> = _categories.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()

    init {
        loadHomeData()
    }

    /**
     * Load all home screen data
     */
    private fun loadHomeData() {
        _isLoading.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                // Load data concurrently
                launch { loadFeaturedProducts() }
                launch { loadNewArrivals() }
                launch { loadTrendingGroupBuys() }
                launch { loadCategories() }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to load home data"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Load featured/curated products
     */
    private suspend fun loadFeaturedProducts() {
        productRepository.getFeaturedProducts(8).collect { result ->
            result.fold(
                onSuccess = { products ->
                    _featuredProducts.value = products
                },
                onFailure = { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load featured products"
                }
            )
        }
    }

    /**
     * Load new arrivals (most recent products)
     */
    private suspend fun loadNewArrivals() {
        productRepository.getActiveProducts().collect { result ->
            result.fold(
                onSuccess = { products ->
                    // Take the 6 most recent products
                    _newArrivals.value = products
                        .sortedByDescending { it.createdAt }
                        .take(6)
                },
                onFailure = { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load new arrivals"
                }
            )
        }
    }

    /**
     * Load trending group buys
     */
    private suspend fun loadTrendingGroupBuys() {
        groupBuyRepository.getTrendingGroupBuys(5).collect { result ->
            result.fold(
                onSuccess = { groupBuys ->
                    _trendingGroupBuys.value = groupBuys
                },
                onFailure = { exception ->
                    _errorMessage.value = exception.message ?: "Failed to load trending group buys"
                }
            )
        }
    }

    /**
     * Load categories for navigation
     */
    private suspend fun loadCategories() {
        val result = productRepository.getCategories()
        result.fold(
            onSuccess = { categoryList ->
                _categories.value = categoryList.take(8) // Limit to 8 categories for home screen
            },
            onFailure = { exception ->
                _errorMessage.value = exception.message ?: "Failed to load categories"
            }
        )
    }

    /**
     * Refresh home data
     */
    fun refreshHomeData() {
        _isRefreshing.value = true
        _errorMessage.value = null

        viewModelScope.launch {
            try {
                // Load data concurrently
                launch { loadFeaturedProducts() }
                launch { loadNewArrivals() }
                launch { loadTrendingGroupBuys() }
                launch { loadCategories() }
            } catch (e: Exception) {
                _errorMessage.value = e.message ?: "Failed to refresh home data"
            } finally {
                _isRefreshing.value = false
            }
        }
    }

    /**
     * Clear error message
     */
    fun clearErrorMessage() {
        _errorMessage.value = null
    }

    /**
     * Get group buy progress for display
     */
    fun getGroupBuyProgress(groupBuy: GroupBuy): Float {
        return groupBuyRepository.getGroupBuyProgress(groupBuy)
    }

    /**
     * Check if group buy is expiring soon
     */
    fun isGroupBuyExpiringSoon(groupBuy: GroupBuy): Boolean {
        return groupBuyRepository.isGroupBuyExpiringSoon(groupBuy)
    }

    /**
     * Get time remaining for group buy
     */
    fun getGroupBuyTimeRemaining(groupBuy: GroupBuy): Long {
        return groupBuyRepository.getTimeRemaining(groupBuy)
    }

    /**
     * Format time remaining for display
     */
    fun formatTimeRemaining(timeInMillis: Long): String {
        val hours = timeInMillis / (1000 * 60 * 60)
        val minutes = (timeInMillis % (1000 * 60 * 60)) / (1000 * 60)
        
        return when {
            hours > 24 -> {
                val days = hours / 24
                "${days}d ${hours % 24}h"
            }
            hours > 0 -> "${hours}h ${minutes}m"
            minutes > 0 -> "${minutes}m"
            else -> "Ending soon"
        }
    }

    /**
     * Check if should show empty state
     */
    fun shouldShowEmptyState(): Boolean {
        return _featuredProducts.value.isEmpty() && 
               _newArrivals.value.isEmpty() && 
               _trendingGroupBuys.value.isEmpty() && 
               !_isLoading.value && 
               _errorMessage.value == null
    }

    /**
     * Check if should show error state
     */
    fun shouldShowErrorState(): Boolean {
        return _errorMessage.value != null && !_isLoading.value
    }

    /**
     * Get section loading states for individual sections
     */
    fun isFeaturedProductsLoaded(): Boolean {
        return _featuredProducts.value.isNotEmpty()
    }

    fun isNewArrivalsLoaded(): Boolean {
        return _newArrivals.value.isNotEmpty()
    }

    fun isTrendingGroupBuysLoaded(): Boolean {
        return _trendingGroupBuys.value.isNotEmpty()
    }

    fun isCategoriesLoaded(): Boolean {
        return _categories.value.isNotEmpty()
    }

    /**
     * Get welcome message based on time of day
     */
    fun getWelcomeMessage(): String {
        val hour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
        return when (hour) {
            in 5..11 -> "Good Morning"
            in 12..16 -> "Good Afternoon"
            in 17..21 -> "Good Evening"
            else -> "Welcome"
        }
    }
}
