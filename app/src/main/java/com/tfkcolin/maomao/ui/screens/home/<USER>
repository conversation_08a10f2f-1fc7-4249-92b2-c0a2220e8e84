package com.tfkcolin.maomao.ui.screens.home

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.GroupBuy
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.models.ProductVariant
import com.tfkcolin.maomao.ui.components.ProductCard
import com.tfkcolin.maomao.ui.navigation.Screen

@Composable
fun HomeScreen(
    onNavigateToProductListing: (categoryId: String) -> Unit,
    onNavigateToProductDetail: (productId: String) -> Unit,
    onNavigateToSearch: () -> Unit,
    onNavigateToShoppingCart: () -> Unit,
    onNavigateToAccount: () -> Unit
) {
    // Mock data for products using the new data models
    val curatedProducts = remember {
        listOf(
            Product(id = "curated1", name = "Curated Ceramic Bowl", description = "Hand-picked bowl.", coverImageUrl = "url_bowl.jpg"),
            Product(id = "curated2", name = "Artisan Tea Set", description = "Exquisite tea set.", coverImageUrl = "url_tea.jpg")
        )
    }
    val newArrivals = remember {
        listOf(
            Product(id = "new1", name = "Minimalist Lamp", description = "Modern design lamp.", coverImageUrl = "url_lamp.jpg"),
            Product(id = "new2", name = "Smart Coffee Mug", description = "Temperature controlled mug.", coverImageUrl = "url_mug.jpg")
        )
    }
    val trendingGroupBuys = remember {
        listOf(
            Product(id = "groupbuy1", name = "Ergonomic Chair", description = "Comfortable office chair.", coverImageUrl = "url_chair.jpg"),
            Product(id = "groupbuy2", name = "Noise-Cancelling Headphones", description = "Immersive audio experience.", coverImageUrl = "url_headphones.jpg")
        )
    }

    // Mock product variants for displaying prices in ProductCard
    val curatedProductVariant1 = remember { ProductVariant(id = "pv_curated1", productId = "curated1", name = "Standard", myPrice = 25.00, currency = "$") }
    val curatedProductVariant2 = remember { ProductVariant(id = "pv_curated2", productId = "curated2", name = "Standard", myPrice = 35.50, currency = "$") }
    val newArrivalVariant1 = remember { ProductVariant(id = "pv_new1", productId = "new1", name = "White", myPrice = 15.00, currency = "$") }
    val newArrivalVariant2 = remember { ProductVariant(id = "pv_new2", productId = "new2", name = "Black", myPrice = 45.00, currency = "$") }

    // Mock GroupBuy data for trending group buys
    val groupBuy1 = remember { GroupBuy(id = "gb1", productVariantId = "pv_groupbuy1", targetQuantity = 10, currentQuantity = 7, groupPrice = 10.00, status = "Active", expiresAt = System.currentTimeMillis() + 86400000) }
    val groupBuy2 = remember { GroupBuy(id = "gb2", productVariantId = "pv_groupbuy2", targetQuantity = 20, currentQuantity = 15, groupPrice = 20.00, status = "Active", expiresAt = System.currentTimeMillis() + 86400000 * 2) }

    val trendingGroupBuyVariants = remember {
        listOf(
            ProductVariant(id = "pv_groupbuy1", productId = "groupbuy1", name = "Basic", myPrice = 15.00, currency = "$"),
            ProductVariant(id = "pv_groupbuy2", productId = "groupbuy2", name = "Pro", myPrice = 25.00, currency = "$")
        )
    }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item { Text(text = "Home Screen", modifier = Modifier.padding(16.dp)) }

        item {
            Text(text = "Curator's Picks", style = MaterialTheme.typography.headlineSmall, modifier = Modifier.padding(8.dp))
        }
        items(curatedProducts) { product ->
            val variant = when (product.id) {
                "curated1" -> curatedProductVariant1
                "curated2" -> curatedProductVariant2
                else -> curatedProductVariant1 // Fallback
            }
            ProductCard(product = product, productVariant = variant, onProductClick = onNavigateToProductDetail)
        }

        item {
            Text(text = "New Arrivals", style = MaterialTheme.typography.headlineSmall, modifier = Modifier.padding(8.dp))
        }
        items(newArrivals) { product ->
            val variant = when (product.id) {
                "new1" -> newArrivalVariant1
                "new2" -> newArrivalVariant2
                else -> newArrivalVariant1 // Fallback
            }
            ProductCard(product = product, productVariant = variant, onProductClick = onNavigateToProductDetail)
        }

        item {
            Text(text = "Trending Group Buys", style = MaterialTheme.typography.headlineSmall, modifier = Modifier.padding(8.dp))
        }
        items(trendingGroupBuys) { product ->
            val variant = when (product.id) {
                "groupbuy1" -> trendingGroupBuyVariants[0]
                "groupbuy2" -> trendingGroupBuyVariants[1]
                else -> trendingGroupBuyVariants[0] // Fallback
            }
            val groupBuy = when (product.id) {
                "groupbuy1" -> groupBuy1
                "groupbuy2" -> groupBuy2
                else -> null
            }
            ProductCard(product = product, productVariant = variant, groupBuy = groupBuy, onProductClick = onNavigateToProductDetail)
        }

        item {
            // Navigation buttons for demonstration
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Button(onClick = { onNavigateToProductListing("exampleCategory") }) {
                    Text("Go to Product Listing")
                }
                Button(onClick = onNavigateToSearch) {
                    Text("Go to Search")
                }
                Button(onClick = onNavigateToShoppingCart) {
                    Text("Go to Shopping Cart")
                }
                Button(onClick = onNavigateToAccount) {
                    Text("Go to Account")
                }
            }
        }
    }
}

