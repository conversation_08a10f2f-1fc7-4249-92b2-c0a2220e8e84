package com.tfkcolin.maomao.ui.components.search

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.FilterList
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material.icons.filled.TrendingUp
import androidx.compose.material3.AssistChip
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilterChip
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.RangeSlider
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.ui.components.buttons.OutlinedMaomaoButton
import com.tfkcolin.maomao.ui.components.buttons.PrimaryButton
import com.tfkcolin.maomao.ui.components.cards.MaomaoCard
import com.tfkcolin.maomao.ui.components.inputs.LabeledCheckbox
import com.tfkcolin.maomao.ui.components.inputs.LabeledRadioButton
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * Search filter data classes
 */
data class SearchFilters(
    val categories: Set<String> = emptySet(),
    val priceRange: ClosedFloatingPointRange<Float> = 0f..1000f,
    val rating: Float? = null,
    val brands: Set<String> = emptySet(),
    val inStock: Boolean = false,
    val onSale: Boolean = false,
    val freeShipping: Boolean = false
)

data class SortOption(
    val id: String,
    val name: String,
    val description: String? = null
)

/**
 * Enhanced search bar with suggestions and recent searches
 */
@Composable
fun EnhancedSearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "Search products...",
    enabled: Boolean = true,
    showSuggestions: Boolean = false,
    suggestions: List<String> = emptyList(),
    recentSearches: List<String> = emptyList(),
    onSuggestionClick: (String) -> Unit = {},
    onClearRecentSearches: () -> Unit = {}
) {
    Column(modifier = modifier) {
        // Search input field
        OutlinedTextField(
            value = query,
            onValueChange = onQueryChange,
            placeholder = { Text(placeholder) },
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "Search",
                    modifier = Modifier.size(Size.iconMedium)
                )
            },
            trailingIcon = {
                if (query.isNotEmpty()) {
                    IconButton(
                        onClick = { 
                            onQueryChange("")
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Clear,
                            contentDescription = "Clear search",
                            modifier = Modifier.size(Size.iconMedium)
                        )
                    }
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = enabled,
            singleLine = true,
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
            keyboardActions = KeyboardActions(
                onSearch = { onSearch(query) }
            ),
            shape = RoundedCornerShape(BorderRadius.textField),
            colors = TextFieldDefaults.colors(
                focusedIndicatorColor = MaterialTheme.colorScheme.primary,
                unfocusedIndicatorColor = MaterialTheme.colorScheme.outline
            )
        )
        
        // Suggestions and recent searches
        if (showSuggestions && (suggestions.isNotEmpty() || recentSearches.isNotEmpty())) {
            MaomaoCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = Spacing.small)
            ) {
                LazyColumn(
                    modifier = Modifier.padding(Spacing.small)
                ) {
                    // Search suggestions
                    if (suggestions.isNotEmpty()) {
                        item {
                            SectionHeader(
                                title = "Suggestions",
                                icon = Icons.Default.TrendingUp
                            )
                        }
                        
                        items(suggestions) { suggestion ->
                            SearchSuggestionItem(
                                text = suggestion,
                                icon = Icons.Default.Search,
                                onClick = { onSuggestionClick(suggestion) }
                            )
                        }
                    }
                    
                    // Recent searches
                    if (recentSearches.isNotEmpty()) {
                        item {
                            SectionHeader(
                                title = "Recent Searches",
                                icon = Icons.Default.History,
                                onClearClick = onClearRecentSearches
                            )
                        }
                        
                        items(recentSearches) { recentSearch ->
                            SearchSuggestionItem(
                                text = recentSearch,
                                icon = Icons.Default.History,
                                onClick = { onSuggestionClick(recentSearch) }
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Search suggestion item
 */
@Composable
private fun SearchSuggestionItem(
    text: String,
    icon: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(Spacing.medium),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(Size.iconMedium)
        )
        
        Text(
            text = text,
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.colorScheme.onSurface,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * Section header for suggestions
 */
@Composable
private fun SectionHeader(
    title: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    onClearClick: (() -> Unit)? = null
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(Spacing.medium),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.small)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(Size.iconSmall)
            )
            
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        onClearClick?.let { clearClick ->
            Text(
                text = "Clear",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.clickable { clearClick() }
            )
        }
    }
}

/**
 * Search filters panel
 */
@OptIn(ExperimentalLayoutApi::class, ExperimentalMaterial3Api::class)
@Composable
fun SearchFiltersPanel(
    filters: SearchFilters,
    onFiltersChange: (SearchFilters) -> Unit,
    onApplyFilters: () -> Unit,
    onClearFilters: () -> Unit,
    modifier: Modifier = Modifier,
    categories: List<String> = emptyList(),
    brands: List<String> = emptyList(),
    maxPrice: Float = 1000f
) {
    var localFilters by remember { mutableStateOf(filters) }
    
    MaomaoCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium),
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Filters",
                    style = MaterialTheme.typography.titleLarge.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = "Clear All",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.clickable { 
                        localFilters = SearchFilters()
                        onClearFilters()
                    }
                )
            }
            
            // Categories
            if (categories.isNotEmpty()) {
                FilterSection(title = "Categories") {
                    FlowRow(
                        horizontalArrangement = Arrangement.spacedBy(Spacing.small),
                        verticalArrangement = Arrangement.spacedBy(Spacing.small)
                    ) {
                        categories.forEach { category ->
                            FilterChip(
                                selected = localFilters.categories.contains(category),
                                onClick = {
                                    localFilters = if (localFilters.categories.contains(category)) {
                                        localFilters.copy(categories = localFilters.categories - category)
                                    } else {
                                        localFilters.copy(categories = localFilters.categories + category)
                                    }
                                },
                                label = { Text(category) }
                            )
                        }
                    }
                }
            }
            
            // Price Range
            FilterSection(title = "Price Range") {
                Column(
                    verticalArrangement = Arrangement.spacedBy(Spacing.small)
                ) {
                    RangeSlider(
                        value = localFilters.priceRange,
                        onValueChange = { range ->
                            localFilters = localFilters.copy(priceRange = range)
                        },
                        valueRange = 0f..maxPrice,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "$${localFilters.priceRange.start.toInt()}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = "$${localFilters.priceRange.endInclusive.toInt()}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }
            
            // Rating
            FilterSection(title = "Minimum Rating") {
                FlowRow(
                    horizontalArrangement = Arrangement.spacedBy(Spacing.small)
                ) {
                    listOf(4f, 3f, 2f, 1f).forEach { rating ->
                        FilterChip(
                            selected = localFilters.rating == rating,
                            onClick = {
                                localFilters = localFilters.copy(
                                    rating = if (localFilters.rating == rating) null else rating
                                )
                            },
                            label = { Text("${rating.toInt()}+ ⭐") }
                        )
                    }
                }
            }
            
            // Brands
            if (brands.isNotEmpty()) {
                FilterSection(title = "Brands") {
                    FlowRow(
                        horizontalArrangement = Arrangement.spacedBy(Spacing.small),
                        verticalArrangement = Arrangement.spacedBy(Spacing.small)
                    ) {
                        brands.forEach { brand ->
                            FilterChip(
                                selected = localFilters.brands.contains(brand),
                                onClick = {
                                    localFilters = if (localFilters.brands.contains(brand)) {
                                        localFilters.copy(brands = localFilters.brands - brand)
                                    } else {
                                        localFilters.copy(brands = localFilters.brands + brand)
                                    }
                                },
                                label = { Text(brand) }
                            )
                        }
                    }
                }
            }
            
            // Quick filters
            FilterSection(title = "Quick Filters") {
                Column(
                    verticalArrangement = Arrangement.spacedBy(Spacing.small)
                ) {
                    LabeledCheckbox(
                        checked = localFilters.inStock,
                        onCheckedChange = { localFilters = localFilters.copy(inStock = it) },
                        label = "In Stock Only"
                    )
                    
                    LabeledCheckbox(
                        checked = localFilters.onSale,
                        onCheckedChange = { localFilters = localFilters.copy(onSale = it) },
                        label = "On Sale"
                    )
                    
                    LabeledCheckbox(
                        checked = localFilters.freeShipping,
                        onCheckedChange = { localFilters = localFilters.copy(freeShipping = it) },
                        label = "Free Shipping"
                    )
                }
            }
            
            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
            ) {
                OutlinedMaomaoButton(
                    text = "Clear",
                    onClick = {
                        localFilters = SearchFilters()
                        onClearFilters()
                    },
                    modifier = Modifier.weight(1f)
                )
                
                PrimaryButton(
                    text = "Apply",
                    onClick = {
                        onFiltersChange(localFilters)
                        onApplyFilters()
                    },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * Filter section with title
 */
@Composable
private fun FilterSection(
    title: String,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.small)
    ) {
        Text(
            text = title,
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Medium
            ),
            color = MaterialTheme.colorScheme.onSurface
        )
        
        content()
    }
}

/**
 * Sort options selector
 */
@Composable
fun SortOptionsSelector(
    selectedSortOption: SortOption?,
    sortOptions: List<SortOption>,
    onSortOptionSelected: (SortOption) -> Unit,
    modifier: Modifier = Modifier
) {
    MaomaoCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium),
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            Text(
                text = "Sort By",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onSurface
            )
            
            sortOptions.forEach { option ->
                LabeledRadioButton(
                    selected = selectedSortOption?.id == option.id,
                    onClick = { onSortOptionSelected(option) },
                    label = option.name,
                    description = option.description
                )
            }
        }
    }
}

/**
 * Search results header with count and view options
 */
@Composable
fun SearchResultsHeader(
    resultCount: Int,
    query: String,
    onFilterClick: () -> Unit,
    onSortClick: () -> Unit,
    modifier: Modifier = Modifier,
    hasActiveFilters: Boolean = false
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.small)
    ) {
        // Results count
        Text(
            text = if (resultCount > 0) {
                "$resultCount results for \"$query\""
            } else {
                "No results found for \"$query\""
            },
            style = MaterialTheme.typography.titleMedium,
            color = MaterialTheme.colorScheme.onSurface
        )
        
        // Filter and sort buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Spacing.small)
        ) {
            OutlinedMaomaoButton(
                text = if (hasActiveFilters) "Filters (Active)" else "Filters",
                onClick = onFilterClick,
                leadingIcon = Icons.Default.FilterList,
                modifier = Modifier.weight(1f)
            )
            
            OutlinedMaomaoButton(
                text = "Sort",
                onClick = onSortClick,
                leadingIcon = Icons.Default.Sort,
                modifier = Modifier.weight(1f)
            )
        }
    }
}
