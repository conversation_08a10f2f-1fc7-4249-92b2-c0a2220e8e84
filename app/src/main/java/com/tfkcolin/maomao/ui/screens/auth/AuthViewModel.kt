package com.tfkcolin.maomao.ui.screens.auth

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tfkcolin.maomao.data.repository.AuthRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository
) : ViewModel() {

    private val _email = MutableStateFlow("")
    val email: StateFlow<String> = _email.asStateFlow()

    private val _password = MutableStateFlow("")
    val password: StateFlow<String> = _password.asStateFlow()

    private val _confirmPassword = MutableStateFlow("")
    val confirmPassword: StateFlow<String> = _confirmPassword.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _errorMessage = MutableStateFlow<String?>(null)
    val errorMessage: StateFlow<String?> = _errorMessage.asStateFlow()

    private val _authSuccess = MutableStateFlow(false)
    val authSuccess: StateFlow<Boolean> = _authSuccess.asStateFlow()

    val currentUser = authRepository.currentUser

    fun onEmailChange(newEmail: String) {
        _email.value = newEmail
    }

    fun onPasswordChange(newPassword: String) {
        _password.value = newPassword
    }

    fun onConfirmPasswordChange(newConfirmPassword: String) {
        _confirmPassword.value = newConfirmPassword
    }

    fun signIn() {
        _isLoading.value = true
        _errorMessage.value = null
        _authSuccess.value = false
        viewModelScope.launch {
            val result = authRepository.signInWithEmailAndPassword(email.value, password.value)
            _isLoading.value = false
            if (result.isSuccess) {
                _authSuccess.value = true
            } else {
                _errorMessage.value = result.exceptionOrNull()?.message ?: "Unknown error"
            }
        }
    }

    fun signUp() {
        _isLoading.value = true
        _errorMessage.value = null
        _authSuccess.value = false
        viewModelScope.launch {
            if (password.value != confirmPassword.value) {
                _errorMessage.value = "Passwords do not match"
                _isLoading.value = false
                return@launch
            }
            val result = authRepository.createUserWithEmailAndPassword(email.value, password.value)
            _isLoading.value = false
            if (result.isSuccess) {
                _authSuccess.value = true
            } else {
                _errorMessage.value = result.exceptionOrNull()?.message ?: "Unknown error"
            }
        }
    }

    fun sendPasswordResetEmail() {
        _isLoading.value = true
        _errorMessage.value = null
        _authSuccess.value = false
        viewModelScope.launch {
            val result = authRepository.sendPasswordResetEmail(email.value)
            _isLoading.value = false
            if (result.isSuccess) {
                _authSuccess.value = true // Indicate success for UI feedback
            } else {
                _errorMessage.value = result.exceptionOrNull()?.message ?: "Unknown error"
            }
        }
    }

    fun resetAuthSuccess() {
        _authSuccess.value = false
    }

    fun resetErrorMessage() {
        _errorMessage.value = null
    }
}
