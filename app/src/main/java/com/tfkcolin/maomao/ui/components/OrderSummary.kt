package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * A reusable composable that presents a breakdown of costs (subtotal, taxes/fees, total).
 * It is used in the Shopping Cart, Checkout Review, and Order Details screens.
 *
 * @param subtotal The subtotal amount before taxes and fees.
 * @param taxes The total amount of taxes and fees.
 * @param total The final total amount.
 * @param currency The currency symbol or code (e.g., "$", "USD").
 *
 * Usage:
 * ```
 * OrderSummary(
 *     subtotal = 100.00,
 *     taxes = 10.00,
 *     total = 110.00,
 *     currency = "$"
 * )
 * ```
 */
@Composable
fun OrderSummary(
    subtotal: Double,
    taxes: Double,
    total: Double,
    currency: String
) {
    Column(modifier = Modifier.fillMaxWidth().padding(8.dp)) {
        Row(modifier = Modifier.fillMaxWidth().padding(vertical = 2.dp)) {
            Text(text = "Subtotal:", modifier = Modifier.weight(1f))
            Text(text = "$currency${"%.2f".format(subtotal)}")
        }
        Row(modifier = Modifier.fillMaxWidth().padding(vertical = 2.dp)) {
            Text(text = "Taxes & Fees:", modifier = Modifier.weight(1f))
            Text(text = "$currency${"%.2f".format(taxes)}")
        }
        Row(modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)) {
            Text(text = "Total:", modifier = Modifier.weight(1f))
            Text(text = "$currency${"%.2f".format(total)}")
        }
    }
}
