package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * A reusable composable that provides a search input field with search functionality.
 * It is used on the Search screen and can be integrated into top app bars.
 *
 * @param query The current search query string.
 * @param onQueryChange A lambda function invoked when the query text changes.
 * @param onSearch A lambda function invoked when the search action is triggered (e.g., by pressing Enter or a search button).
 * @param hint The placeholder text to display when the search bar is empty. Defaults to "Search products...".
 *
 * Usage:
 * ```
 * var searchQuery by remember { mutableStateOf("") }
 * SearchBar(
 *     query = searchQuery,
 *     onQueryChange = { searchQuery = it },
 *     onSearch = { query -> /* perform search */ }
 * )
 * ```
 */
@Composable
fun SearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    hint: String = "Search products..."
) {
    OutlinedTextField(
        value = query,
        onValueChange = onQueryChange,
        label = { Text(hint) },
        leadingIcon = { Icon(Icons.Default.Search, contentDescription = "Search Icon") },
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp),
        singleLine = true,
        // In a real app, you'd add keyboard actions for 'onSearch'
    )
}
