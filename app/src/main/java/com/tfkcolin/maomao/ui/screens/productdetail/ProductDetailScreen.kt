package com.tfkcolin.maomao.ui.screens.productdetail

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.GroupBuy
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.models.ProductVariant
import com.tfkcolin.maomao.ui.components.GroupBuyProgressBar
import com.tfkcolin.maomao.ui.components.ImageCarousel
import com.tfkcolin.maomao.ui.components.VariantSelector

@Composable
fun ProductDetailScreen(
    productId: String?,
    onNavigateToShoppingCart: () -> Unit
) {
    // Mock data for product details using the new data models
    val product = remember {
        Product(
            id = productId ?: "defaultProduct",
            name = "Luxury Silk Scarf",
            description = "A beautifully crafted silk scarf, perfect for any occasion. Hand-dyed with natural pigments.",
            coverImageUrl = "url_scarf_main.jpg",
            images = listOf("url_scarf_main.jpg", "url_scarf_detail1.jpg", "url_scarf_detail2.jpg"),
            category = "Accessories",
            status = "Active",
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis()
        )
    }
    val productVariants = remember {
        listOf(
            ProductVariant(
                id = "pv_scarf_red",
                productId = product.id,
                sku = "SCARF-RED-OS",
                name = "Red - One Size",
                attributes = mapOf("color" to "Red", "size" to "One Size"),
                myPrice = 49.99,
                currency = "$",
                mainImageUrl = "url_scarf_red.jpg",
                weight = 0.1,
                weightUnit = "kg",
                virtualStock = 100
            ),
            ProductVariant(
                id = "pv_scarf_blue",
                productId = product.id,
                sku = "SCARF-BLUE-OS",
                name = "Blue - One Size",
                attributes = mapOf("color" to "Blue", "size" to "One Size"),
                myPrice = 49.99,
                currency = "$",
                mainImageUrl = "url_scarf_blue.jpg",
                weight = 0.1,
                weightUnit = "kg",
                virtualStock = 80
            ),
            ProductVariant(
                id = "pv_scarf_green_gb",
                productId = product.id,
                sku = "SCARF-GREEN-OS-GB",
                name = "Green - One Size (Group Buy)",
                attributes = mapOf("color" to "Green", "size" to "One Size"),
                myPrice = 60.00, // Higher base price
                currency = "$",
                mainImageUrl = "url_scarf_green.jpg",
                weight = 0.1,
                weightUnit = "kg",
                virtualStock = 50
            )
        )
    }
    var selectedVariant by remember { mutableStateOf<ProductVariant?>(null) }

    // Mock GroupBuy data, linked to the green variant
    val groupBuyForGreenVariant = remember {
        GroupBuy(
            id = "gb_scarf_green",
            productVariantId = "pv_scarf_green_gb",
            targetQuantity = 20,
            currentQuantity = 15,
            groupPrice = 39.99, // Discounted price
            status = "Active",
            expiresAt = System.currentTimeMillis() + (2 * 24 * 60 * 60 * 1000L) // 2 days from now
        )
    }

    // Determine if the currently selected variant has an active group buy
    val activeGroupBuy: GroupBuy? = if (selectedVariant?.id == groupBuyForGreenVariant.productVariantId && groupBuyForGreenVariant.status == "Active") {
        groupBuyForGreenVariant
    } else {
        null
    }

    val currentVariantPrice = selectedVariant?.myPrice ?: productVariants.firstOrNull()?.myPrice ?: 0.0
    val currentVariantCurrency = selectedVariant?.currency ?: productVariants.firstOrNull()?.currency ?: "$"

    Column(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = "Product Detail Screen for Product: ${product.name}")
        Spacer(modifier = Modifier.height(16.dp))

        ImageCarousel(imageUrls = product.images)
        Spacer(modifier = Modifier.height(16.dp))

        Text(text = "Product Name: ${product.name}", style = MaterialTheme.typography.headlineSmall)
        Text(text = "Curator's Note: ${product.description}", style = MaterialTheme.typography.bodyMedium)
        Text(text = "Price: $currentVariantCurrency${String.format("%.2f", currentVariantPrice)}", style = MaterialTheme.typography.titleLarge)

        VariantSelector(
            variants = productVariants,
            selectedVariant = selectedVariant,
            onVariantSelected = { selectedVariant = it }
        )
        Spacer(modifier = Modifier.height(16.dp))

        activeGroupBuy?.let {
            GroupBuyProgressBar(groupBuy = it)
            Spacer(modifier = Modifier.height(16.dp))
        }

        Button(onClick = onNavigateToShoppingCart) {
            Text(if (activeGroupBuy != null) "Join Group Buy" else "Add to Cart")
        }
    }
}
