package com.tfkcolin.maomao.ui.screens.account

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.Order
import com.tfkcolin.maomao.ui.components.OrderListItem

@Composable
fun MyOrdersScreen(
    onNavigateToOrderDetails: (orderId: String) -> Unit,
    onNavigateToOrderIssueResolution: (orderIssueId: String) -> Unit
) {
    // Mock data for orders using the new data models
    val orders = remember {
        listOf(
            Order(id = "order1", orderNumber = "CB-1001", createdAt = System.currentTimeMillis() - ******** * 5, totalAmount = 150.00, status = "Delivered", currency = "$"),
            Order(id = "order2", orderNumber = "CB-1002", createdAt = System.currentTimeMillis() - ******** * 2, totalAmount = 200.50, status = "Action Required", currency = "$"),
            Order(id = "order3", orderNumber = "CB-1003", createdAt = System.currentTimeMillis(), totalAmount = 75.00, status = "Processing", currency = "$")
        )
    }

    LazyColumn(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item { Text(text = "My Orders List", style = MaterialTheme.typography.headlineMedium, modifier = Modifier.padding(bottom = 16.dp)) }

        items(orders) { order ->
            OrderListItem(
                order = order,
                onOrderClick = { orderId ->
                    if (order.status == "Action Required") {
                        onNavigateToOrderIssueResolution("ISSUE_FOR_${orderId}") // Assuming an issue ID can be derived
                    } else {
                        onNavigateToOrderDetails(orderId)
                    }
                }
            )
        }
    }
}
