package com.tfkcolin.maomao.ui.screens.account

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.tfkcolin.maomao.ui.navigation.Screen

@Composable
fun AccountScreen(
    onNavigateToMyOrders: () -> Unit,
    onNavigateToManageAddresses: () -> Unit,
    onNavigateToSettings: () -> Unit
) {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Text(text = "Account / Profile Screen")
            Button(onClick = onNavigateToMyOrders) {
                Text("My Orders")
            }
            But<PERSON>(onClick = onNavigateToManageAddresses) {
                Text("Manage Addresses")
            }
            But<PERSON>(onClick = onNavigateToSettings) {
                Text("Settings")
            }
        }
    }
}
