package com.tfkcolin.maomao.ui.theme

import androidx.compose.ui.graphics.Color

// Pinduoduo-inspired color palette

// Primary Colors
val PinduoduoRed = Color(0xFFE02E24)
val PinduoduoRedDark = Color(0xFFC41F16)
val PinduoduoRedLight = Color(0xFFFF6B61)
val PinduoduoYellow = Color(0xFFFFD100)
val PinduoduoOrange = Color(0xFFFF8C00)

// Secondary Colors
val SecondaryBlue = Color(0xFF2196F3)
val SecondaryGreen = Color(0xFF4CAF50)
val SecondaryPurple = Color(0xFF9C27B0)

// Background and surface colors
val LightBackground = Color(0xFFF5F5F5)
val LightSurface = Color(0xFFFFFFFF)
val LightSurfaceVariant = Color(0xFFF8F9FA)
val LightOutline = Color(0xFFE0E0E0)
val LightOutlineVariant = Color(0xFFF5F5F5)

// Text colors
val TextPrimary = Color(0xFF212121)
val TextSecondary = Color(0xFF757575)
val TextTertiary = Color(0xFF9E9E9E)
val TextDisabled = Color(0xFFBDBDBD)

// Dark Theme Colors
val DarkBackground = Color(0xFF121212)
val DarkSurface = Color(0xFF1E1E1E)
val DarkSurfaceVariant = Color(0xFF2C2C2C)
val DarkOutline = Color(0xFF404040)
val DarkOutlineVariant = Color(0xFF2C2C2C)

// Dark Text colors
val DarkTextPrimary = Color(0xFFE0E0E0)
val DarkTextSecondary = Color(0xFFB0B0B0)
val DarkTextTertiary = Color(0xFF808080)

// Status Colors
val SuccessGreen = Color(0xFF4CAF50)
val SuccessGreenLight = Color(0xFF81C784)
val WarningAmber = Color(0xFFFFC107)
val WarningAmberLight = Color(0xFFFFD54F)
val ErrorRed = Color(0xFFF44336)
val ErrorRedLight = Color(0xFFEF5350)
val InfoBlue = Color(0xFF2196F3)
val InfoBlueLight = Color(0xFF64B5F6)

// E-commerce specific colors
val PriceRed = Color(0xFFE53E3E)
val DiscountGreen = Color(0xFF38A169)
val SaleOrange = Color(0xFFED8936)
val OutOfStockGray = Color(0xFF718096)

// Group Buy specific colors
val GroupBuyPrimary = Color(0xFFFF6B35)
val GroupBuySecondary = Color(0xFFFFA726)
val GroupBuyProgress = Color(0xFF66BB6A)

// Rating colors
val RatingGold = Color(0xFFFFB400)
val RatingGray = Color(0xFFE0E0E0)

// Legacy colors (keeping for backward compatibility)
val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)