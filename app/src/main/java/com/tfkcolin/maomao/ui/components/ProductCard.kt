package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.GroupBuy
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.models.ProductVariant
import com.tfkcolin.maomao.ui.components.StatusBadge
import com.tfkcolin.maomao.ui.components.StatusType

/**
 * A reusable composable that displays a concise summary of a product.
 * It is suitable for product listings, home screen sections, and search results.
 *
 * @param product The core product data to display.
 * @param productVariant The specific product variant to display its price.
 * @param groupBuy An optional GroupBuy object if the product variant is part of an active group buy.
 * @param onProductClick A lambda function that is invoked when the product card is clicked.
 *   It typically navigates to the Product Detail Page.
 *
 * Usage:
 * ```
 * ProductCard(
 *     product = Product(id = "1", name = "Example Product", coverImageUrl = "...", description = "..."),
 *     productVariant = ProductVariant(id = "v1", myPrice = 19.99),
 *     groupBuy = GroupBuy(id = "gb1", groupPrice = 15.00), // Optional
 *     onProductClick = { productId -> /* navigate to product detail */ }
 * )
 * ```
 */
@Composable
fun ProductCard(
    product: Product,
    productVariant: ProductVariant, // ProductCard should always display a specific variant's price
    groupBuy: GroupBuy? = null, // Optional: to indicate active group buy
    onProductClick: (productId: String) -> Unit
) {
    Card(
        modifier = Modifier
            .padding(8.dp)
            .fillMaxWidth() // Make it fill width for better visibility in lists
            .clickable { onProductClick(product.id) }
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            // Placeholder for Image - In a real app, use Coil/Glide for product.coverImageUrl
            Text(text = "Image: ${product.coverImageUrl.takeIf { it.isNotBlank() } ?: "No Image"}")
            Text(text = "Product Name: ${product.name}")
            Text(text = "Variant: ${productVariant.name}")

            groupBuy?.let {
                Text(text = "Group Price: ${it.groupPrice}", style = MaterialTheme.typography.titleMedium)
                StatusBadge(statusText = "Group Buy Active", type = StatusType.Info)
            } ?: run {
                Text(text = "Price: ${productVariant.currency}${productVariant.myPrice}", style = MaterialTheme.typography.titleMedium)
            }
        }
    }
}
