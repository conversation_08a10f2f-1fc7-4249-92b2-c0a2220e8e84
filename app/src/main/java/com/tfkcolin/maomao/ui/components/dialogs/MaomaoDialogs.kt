package com.tfkcolin.maomao.ui.components.dialogs

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.BasicAlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.tfkcolin.maomao.ui.components.buttons.OutlinedMaomaoButton
import com.tfkcolin.maomao.ui.components.buttons.PrimaryButton
import com.tfkcolin.maomao.ui.components.buttons.TextMaomaoButton
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.ErrorRed
import com.tfkcolin.maomao.ui.theme.InfoBlue
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing
import com.tfkcolin.maomao.ui.theme.SuccessGreen
import com.tfkcolin.maomao.ui.theme.WarningAmber

/**
 * Dialog types for different use cases
 */
enum class DialogType {
    Info, Success, Warning, Error
}

/**
 * Confirmation dialog with customizable actions
 */
@Composable
fun ConfirmationDialog(
    title: String,
    message: String,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    confirmText: String = "Confirm",
    dismissText: String = "Cancel",
    type: DialogType = DialogType.Info,
    isDestructive: Boolean = false
) {
    val (icon, iconColor) = when (type) {
        DialogType.Info -> Icons.Default.Info to InfoBlue
        DialogType.Success -> Icons.Default.CheckCircle to SuccessGreen
        DialogType.Warning -> Icons.Default.Warning to WarningAmber
        DialogType.Error -> Icons.Default.Error to ErrorRed
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        modifier = modifier,
        icon = {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = iconColor,
                modifier = Modifier.size(Size.iconLarge)
            )
        },
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Bold
                ),
                textAlign = TextAlign.Center
            )
        },
        text = {
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        },
        confirmButton = {
            PrimaryButton(
                text = confirmText,
                onClick = onConfirm
            )
        },
        dismissButton = {
            if (isDestructive) {
                TextMaomaoButton(
                    text = dismissText,
                    onClick = onDismiss
                )
            } else {
                OutlinedMaomaoButton(
                    text = dismissText,
                    onClick = onDismiss
                )
            }
        },
        shape = RoundedCornerShape(BorderRadius.dialog),
        containerColor = MaterialTheme.colorScheme.surface,
        titleContentColor = MaterialTheme.colorScheme.onSurface,
        textContentColor = MaterialTheme.colorScheme.onSurfaceVariant
    )
}

/**
 * Simple alert dialog with single action
 */
@Composable
fun AlertMaomaoDialog(
    title: String,
    message: String,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    buttonText: String = "OK",
    type: DialogType = DialogType.Info
) {
    val (icon, iconColor) = when (type) {
        DialogType.Info -> Icons.Default.Info to InfoBlue
        DialogType.Success -> Icons.Default.CheckCircle to SuccessGreen
        DialogType.Warning -> Icons.Default.Warning to WarningAmber
        DialogType.Error -> Icons.Default.Error to ErrorRed
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        modifier = modifier,
        icon = {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = iconColor,
                modifier = Modifier.size(Size.iconLarge)
            )
        },
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Bold
                ),
                textAlign = TextAlign.Center
            )
        },
        text = {
            Text(
                text = message,
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        },
        confirmButton = {
            PrimaryButton(
                text = buttonText,
                onClick = onDismiss
            )
        },
        shape = RoundedCornerShape(BorderRadius.dialog),
        containerColor = MaterialTheme.colorScheme.surface,
        titleContentColor = MaterialTheme.colorScheme.onSurface,
        textContentColor = MaterialTheme.colorScheme.onSurfaceVariant
    )
}

/**
 * Loading dialog with progress indicator
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LoadingDialog(
    message: String,
    modifier: Modifier = Modifier,
    canDismiss: Boolean = false,
    onDismiss: (() -> Unit)? = null
) {
    BasicAlertDialog(
        onDismissRequest = {
            if (canDismiss && onDismiss != null) onDismiss()
        },
        modifier = modifier,
        properties = DialogProperties(
            dismissOnBackPress = canDismiss,
            dismissOnClickOutside = canDismiss
        )
    ) {
        Card(
            shape = RoundedCornerShape(BorderRadius.dialog),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier.padding(Spacing.extraLarge),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Spacing.medium)
            ) {
                androidx.compose.material3.CircularProgressIndicator(
                    modifier = Modifier.size(Size.iconExtraLarge),
                    strokeWidth = 3.dp,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}

/**
 * Custom dialog with flexible content
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomDialog(
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    title: String? = null,
    canDismissOnBackPress: Boolean = true,
    canDismissOnClickOutside: Boolean = true,
    content: @Composable () -> Unit
) {
    BasicAlertDialog(
        onDismissRequest = onDismiss,
        modifier = modifier,
        properties = DialogProperties(
            dismissOnBackPress = canDismissOnBackPress,
            dismissOnClickOutside = canDismissOnClickOutside
        )
    ) {
        Surface(
            shape = RoundedCornerShape(BorderRadius.dialog),
            color = MaterialTheme.colorScheme.surface,
            tonalElevation = 8.dp
        ) {
            Column(
                modifier = Modifier.padding(Spacing.extraLarge)
            ) {
                title?.let { titleText ->
                    Text(
                        text = titleText,
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.padding(bottom = Spacing.medium)
                    )
                }
                
                content()
            }
        }
    }
}

/**
 * Selection dialog with list of options
 */
@Composable
fun <T> SelectionDialog(
    title: String,
    items: List<T>,
    selectedItem: T?,
    onItemSelected: (T) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    itemLabel: (T) -> String = { it.toString() }
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        modifier = modifier,
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Bold
                )
            )
        },
        text = {
            Column {
                items.forEach { item ->
                    val isSelected = selectedItem == item
                    
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = Spacing.small),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        androidx.compose.material3.RadioButton(
                            selected = isSelected,
                            onClick = { onItemSelected(item) }
                        )
                        
                        Spacer(modifier = Modifier.width(Spacing.small))
                        
                        Text(
                            text = itemLabel(item),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextMaomaoButton(
                text = "Cancel",
                onClick = onDismiss
            )
        },
        shape = RoundedCornerShape(BorderRadius.dialog),
        containerColor = MaterialTheme.colorScheme.surface
    )
}

/**
 * Input dialog with text field
 */
@Composable
fun InputDialog(
    title: String,
    value: String,
    onValueChange: (String) -> Unit,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
    label: String = "Input",
    placeholder: String = "",
    confirmText: String = "OK",
    dismissText: String = "Cancel",
    isError: Boolean = false,
    errorMessage: String? = null
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        modifier = modifier,
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Bold
                )
            )
        },
        text = {
            Column {
                com.tfkcolin.maomao.ui.components.inputs.MaomaoTextField(
                    value = value,
                    onValueChange = onValueChange,
                    label = label,
                    placeholder = placeholder,
                    isError = isError,
                    errorMessage = errorMessage,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            PrimaryButton(
                text = confirmText,
                onClick = onConfirm,
                enabled = value.isNotBlank() && !isError
            )
        },
        dismissButton = {
            TextMaomaoButton(
                text = dismissText,
                onClick = onDismiss
            )
        },
        shape = RoundedCornerShape(BorderRadius.dialog),
        containerColor = MaterialTheme.colorScheme.surface
    )
}
