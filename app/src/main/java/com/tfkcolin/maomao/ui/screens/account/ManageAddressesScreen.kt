package com.tfkcolin.maomao.ui.screens.account

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.ShippingAddress
import com.tfkcolin.maomao.ui.components.AddressDisplayCard
import com.tfkcolin.maomao.ui.components.AddressForm

@Composable
fun ManageAddressesScreen(onNavigateBackToAccount: () -> Unit) {
    // Mock data for saved addresses using the new data model
    val savedAddresses = remember {
        listOf(
            ShippingAddress(id = "addr1", addressLine1 = "123 Main St", city = "Anytown", stateOrProvince = "CA", postalCode = "90210", country = "USA", isDefault = true),
            ShippingAddress(id = "addr2", addressLine1 = "456 Elm St", city = "Otherville", stateOrProvince = "NY", postalCode = "10001", country = "USA")
        )
    }

    // State to control showing the Add/Edit Address form
    var showAddressForm by remember { mutableStateOf(false) }
    var addressToEdit by remember { mutableStateOf<ShippingAddress?>(null) }

    LazyColumn(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item { Text(text = "Manage Addresses", style = MaterialTheme.typography.headlineMedium, modifier = Modifier.padding(bottom = 16.dp)) }

        if (showAddressForm) {
            item {
                AddressForm(
                    address = addressToEdit,
                    onSave = { newAddress ->
                        // TODO: Save new/edited address to backend
                        showAddressForm = false
                        addressToEdit = null
                    },
                    onCancel = {
                        showAddressForm = false
                        addressToEdit = null
                    }
                )
                Spacer(modifier = Modifier.height(16.dp))
            }
        } else {
            item {
                Button(onClick = {
                    showAddressForm = true
                    addressToEdit = null // For adding new address
                }, modifier = Modifier.fillMaxWidth().padding(bottom = 8.dp)) {
                    Text("Add New Address")
                }
            }

            items(savedAddresses) { address ->
                AddressDisplayCard(
                    address = address,
                    onEditClick = { addressId ->
                        addressToEdit = savedAddresses.find { it.id == addressId }
                        showAddressForm = true
                    },
                    onDeleteClick = { addressId -> /* TODO: Delete address */ },
                    onSelect = { addressId -> /* Not directly used here, but for consistency */ }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }

            item {
                Spacer(modifier = Modifier.height(16.dp))
                Button(onClick = onNavigateBackToAccount, modifier = Modifier.fillMaxWidth()) {
                    Text("Back to Account")
                }
            }
        }
    }
}
