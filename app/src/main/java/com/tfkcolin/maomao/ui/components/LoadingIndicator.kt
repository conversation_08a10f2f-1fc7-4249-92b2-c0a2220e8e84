package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

/**
 * A reusable composable that provides visual feedback during data loading or processing.
 *
 * @param modifier Optional [Modifier] for this composable.
 *
 * Usage:
 * ```
 * if (isLoading) {
 *     LoadingIndicator(modifier = Modifier.fillMaxSize())
 * }
 * ```
 */
@Composable
fun LoadingIndicator(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
        Text(text = "Loading...") // Optional text
    }
}
