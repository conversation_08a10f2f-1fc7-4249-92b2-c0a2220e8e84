package com.tfkcolin.maomao.ui.components.cart

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Remove
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.repository.CartRepository.CartItem
import com.tfkcolin.maomao.ui.components.buttons.OutlinedMaomaoButton
import com.tfkcolin.maomao.ui.components.buttons.PrimaryButton
import com.tfkcolin.maomao.ui.components.cards.MaomaoCard
import com.tfkcolin.maomao.ui.components.images.ProductImage
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.DiscountGreen
import com.tfkcolin.maomao.ui.theme.ErrorRed
import com.tfkcolin.maomao.ui.theme.PriceRed
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * Cart item card component displaying product details, quantity controls, and pricing
 */
@Composable
fun CartItemCard(
    cartItem: CartItem,
    onQuantityChange: (Int) -> Unit,
    onRemoveItem: () -> Unit,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    showRemoveButton: Boolean = true
) {
    MaomaoCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium),
            horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            // Product Image
            ProductImage(
                imageUrl = cartItem.productImageUrl,
                contentDescription = cartItem.productName,
                modifier = Modifier.size(Size.productImageMedium)
            )
            
            // Product Details
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(Spacing.small)
            ) {
                // Product Name
                Text(
                    text = cartItem.productName,
                    style = MaterialTheme.typography.titleMedium,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                // Variant Info
                if (cartItem.variantAttributes.isNotEmpty()) {
                    cartItem.variantAttributes.forEach { (key, value) ->
                        Text(
                            text = "$key: $value",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
                
                // Price Section
                CartItemPricing(
                    unitPrice = cartItem.pricePerUnit,
                    quantity = cartItem.quantity,
                    currency = "Fcfa",
                )
                
                // Quantity Controls and Remove Button
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    QuantitySelector(
                        quantity = cartItem.quantity,
                        onQuantityChange = onQuantityChange,
                        enabled = !isLoading,
                    )
                    
                    if (showRemoveButton) {
                        IconButton(
                            onClick = onRemoveItem,
                            enabled = !isLoading
                        ) {
                            Icon(
                                imageVector = Icons.Default.Delete,
                                contentDescription = "Remove item",
                                tint = ErrorRed,
                                modifier = Modifier.size(Size.iconMedium)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * Quantity selector with increment/decrement buttons
 */
@Composable
fun QuantitySelector(
    quantity: Int,
    onQuantityChange: (Int) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    minQuantity: Int = 1,
    maxQuantity: Int = 99
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.small)
    ) {
        // Decrease button
        IconButton(
            onClick = { 
                if (quantity > minQuantity) {
                    onQuantityChange(quantity - 1)
                }
            },
            enabled = enabled && quantity > minQuantity,
            modifier = Modifier.size(32.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .clip(RoundedCornerShape(BorderRadius.small))
                    .background(
                        if (enabled && quantity > minQuantity) {
                            MaterialTheme.colorScheme.outline
                        } else {
                            MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
                        }
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Remove,
                    contentDescription = "Decrease quantity",
                    modifier = Modifier.size(16.dp),
                    tint = if (enabled && quantity > minQuantity) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                    }
                )
            }
        }
        
        // Quantity display
        Text(
            text = quantity.toString(),
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Medium
            ),
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.width(32.dp)
        )
        
        // Increase button
        IconButton(
            onClick = { 
                if (quantity < maxQuantity) {
                    onQuantityChange(quantity + 1)
                }
            },
            enabled = enabled && quantity < maxQuantity,
            modifier = Modifier.size(32.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(32.dp)
                    .clip(RoundedCornerShape(BorderRadius.small))
                    .background(
                        if (enabled && quantity < maxQuantity) {
                            MaterialTheme.colorScheme.primary
                        } else {
                            MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
                        }
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Add,
                    contentDescription = "Increase quantity",
                    modifier = Modifier.size(16.dp),
                    tint = if (enabled && quantity < maxQuantity) {
                        MaterialTheme.colorScheme.onPrimary
                    } else {
                        MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                    }
                )
            }
        }
    }
}

/**
 * Cart item pricing display with discounts
 */
@Composable
private fun CartItemPricing(
    unitPrice: Double,
    quantity: Int,
    currency: String,
    originalPrice: Double? = null,
    discountPercentage: Int? = null
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(Spacing.small),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Current unit price
            Text(
                text = "$currency${String.format("%.2f", unitPrice)}",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = PriceRed
            )
            
            // Original price (if discounted)
            originalPrice?.let { original ->
                if (original > unitPrice) {
                    Text(
                        text = "$currency${String.format("%.2f", original)}",
                        style = MaterialTheme.typography.bodySmall.copy(
                            textDecoration = TextDecoration.LineThrough
                        ),
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
            
            // Discount percentage
            discountPercentage?.let { discount ->
                if (discount > 0) {
                    Text(
                        text = "-$discount%",
                        style = MaterialTheme.typography.bodySmall.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        color = DiscountGreen
                    )
                }
            }
        }
        
        // Total price for this item
        if (quantity > 1) {
            Text(
                text = "Total: $currency${String.format("%.2f", unitPrice * quantity)}",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * Cart summary component showing totals and checkout button
 */
@Composable
fun CartSummary(
    subtotal: Double,
    shipping: Double,
    tax: Double,
    discount: Double,
    total: Double,
    currency: String,
    onCheckout: () -> Unit,
    modifier: Modifier = Modifier,
    isCheckoutEnabled: Boolean = true,
    isLoading: Boolean = false,
    promoCode: String? = null,
    onApplyPromoCode: ((String) -> Unit)? = null
) {
    MaomaoCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium),
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            Text(
                text = "Order Summary",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onSurface
            )
            
            // Price breakdown
            Column(
                verticalArrangement = Arrangement.spacedBy(Spacing.small)
            ) {
                SummaryRow(
                    label = "Subtotal",
                    value = "$currency${String.format("%.2f", subtotal)}"
                )
                
                if (shipping > 0) {
                    SummaryRow(
                        label = "Shipping",
                        value = "$currency${String.format("%.2f", shipping)}"
                    )
                } else {
                    SummaryRow(
                        label = "Shipping",
                        value = "Free",
                        valueColor = DiscountGreen
                    )
                }
                
                if (tax > 0) {
                    SummaryRow(
                        label = "Tax",
                        value = "$currency${String.format("%.2f", tax)}"
                    )
                }
                
                if (discount > 0) {
                    SummaryRow(
                        label = "Discount",
                        value = "-$currency${String.format("%.2f", discount)}",
                        valueColor = DiscountGreen
                    )
                }
                
                HorizontalDivider(
                    modifier = Modifier.padding(vertical = Spacing.small),
                    color = MaterialTheme.colorScheme.outline
                )
                
                SummaryRow(
                    label = "Total",
                    value = "$currency${String.format("%.2f", total)}",
                    labelStyle = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    valueStyle = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Bold
                    ),
                    valueColor = PriceRed
                )
            }
            
            // Promo code section
            if (onApplyPromoCode != null) {
                PromoCodeSection(
                    currentPromoCode = promoCode,
                    onApplyPromoCode = onApplyPromoCode,
                    enabled = !isLoading
                )
            }
            
            // Checkout button
            PrimaryButton(
                text = "Proceed to Checkout",
                onClick = onCheckout,
                modifier = Modifier.fillMaxWidth(),
                enabled = isCheckoutEnabled && !isLoading,
                isLoading = isLoading,
                fullWidth = true
            )
        }
    }
}

/**
 * Summary row component for price breakdown
 */
@Composable
private fun SummaryRow(
    label: String,
    value: String,
    modifier: Modifier = Modifier,
    labelStyle: androidx.compose.ui.text.TextStyle = MaterialTheme.typography.bodyLarge,
    valueStyle: androidx.compose.ui.text.TextStyle = MaterialTheme.typography.bodyLarge,
    valueColor: Color = MaterialTheme.colorScheme.onSurface
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = labelStyle,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Text(
            text = value,
            style = valueStyle,
            color = valueColor
        )
    }
}

/**
 * Promo code input section
 */
@Composable
private fun PromoCodeSection(
    currentPromoCode: String?,
    onApplyPromoCode: (String) -> Unit,
    enabled: Boolean
) {
    var promoCodeInput by remember { androidx.compose.runtime.mutableStateOf("") }

    Column(
        verticalArrangement = Arrangement.spacedBy(Spacing.small)
    ) {
        Text(
            text = "Promo Code",
            style = MaterialTheme.typography.titleSmall,
            color = MaterialTheme.colorScheme.onSurface
        )

        Row(
            horizontalArrangement = Arrangement.spacedBy(Spacing.small),
            verticalAlignment = Alignment.CenterVertically
        ) {
            com.tfkcolin.maomao.ui.components.inputs.MaomaoTextField(
                value = promoCodeInput,
                onValueChange = { promoCodeInput = it },
                label = "Enter code",
                modifier = Modifier.weight(1f),
                enabled = enabled
            )

            OutlinedMaomaoButton(
                text = "Apply",
                onClick = {
                    if (promoCodeInput.isNotBlank()) {
                        onApplyPromoCode(promoCodeInput)
                    }
                },
                enabled = enabled && promoCodeInput.isNotBlank()
            )
        }

        currentPromoCode?.let { code ->
            Text(
                text = "Applied: $code",
                style = MaterialTheme.typography.bodySmall,
                color = DiscountGreen
            )
        }
    }
}

/**
 * Empty cart state component
 */
@Composable
fun EmptyCartState(
    onContinueShopping: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(Spacing.extraLarge),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Spacing.large)
    ) {
        // Empty cart illustration placeholder
        Box(
            modifier = Modifier
                .size(120.dp)
                .clip(RoundedCornerShape(BorderRadius.large))
                .background(MaterialTheme.colorScheme.surfaceVariant),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Default.ShoppingCart,
                contentDescription = "Empty cart",
                modifier = Modifier.size(Size.iconExtraLarge),
                tint = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.small)
        ) {
            Text(
                text = "Your cart is empty",
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onSurface
            )

            Text(
                text = "Add some products to get started",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        PrimaryButton(
            text = "Continue Shopping",
            onClick = onContinueShopping,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

/**
 * Cart item count badge
 */
@Composable
fun CartBadge(
    itemCount: Int,
    modifier: Modifier = Modifier
) {
    if (itemCount > 0) {
        Box(
            modifier = modifier
                .size(20.dp)
                .clip(RoundedCornerShape(10.dp))
                .background(ErrorRed),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (itemCount > 99) "99+" else itemCount.toString(),
                style = MaterialTheme.typography.labelSmall.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = Color.White
            )
        }
    }
}
