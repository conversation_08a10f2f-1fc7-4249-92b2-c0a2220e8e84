package com.tfkcolin.maomao.ui.components.navigation

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Menu
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.CenterAlignedTopAppBar
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LargeTopAppBar
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MediumTopAppBar
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.TopAppBarScrollBehavior
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.ui.components.images.AvatarImage
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * App bar size variants
 */
enum class AppBarSize {
    Small, Medium, Large, CenterAligned
}

/**
 * Enhanced top app bar with various configurations
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MaomaoTopAppBar(
    title: String,
    modifier: Modifier = Modifier,
    size: AppBarSize = AppBarSize.Small,
    navigationIcon: ImageVector? = null,
    onNavigationClick: (() -> Unit)? = null,
    actions: (@Composable RowScope.() -> Unit)? = null,
    scrollBehavior: TopAppBarScrollBehavior? = null
) {
    val colors = TopAppBarDefaults.topAppBarColors(
        containerColor = MaterialTheme.colorScheme.surface,
        titleContentColor = MaterialTheme.colorScheme.onSurface,
        navigationIconContentColor = MaterialTheme.colorScheme.onSurface,
        actionIconContentColor = MaterialTheme.colorScheme.onSurface
    )

    when (size) {
        AppBarSize.Small -> {
            TopAppBar(
                title = {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                },
                modifier = modifier,
                navigationIcon = {
                    navigationIcon?.let { icon ->
                        IconButton(
                            onClick = onNavigationClick ?: {}
                        ) {
                            Icon(
                                imageVector = icon,
                                contentDescription = "Navigation",
                                modifier = Modifier.size(Size.iconMedium)
                            )
                        }
                    }
                },
                actions = actions ?: {},
                colors = colors,
                scrollBehavior = scrollBehavior
            )
        }
        AppBarSize.Medium -> {
            MediumTopAppBar(
                title = {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                },
                modifier = modifier,
                navigationIcon = {
                    navigationIcon?.let { icon ->
                        IconButton(
                            onClick = onNavigationClick ?: {}
                        ) {
                            Icon(
                                imageVector = icon,
                                contentDescription = "Navigation",
                                modifier = Modifier.size(Size.iconMedium)
                            )
                        }
                    }
                },
                actions = actions ?: {},
                colors = colors,
                scrollBehavior = scrollBehavior
            )
        }
        AppBarSize.Large -> {
            LargeTopAppBar(
                title = {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                },
                modifier = modifier,
                navigationIcon = {
                    navigationIcon?.let { icon ->
                        IconButton(
                            onClick = onNavigationClick ?: {}
                        ) {
                            Icon(
                                imageVector = icon,
                                contentDescription = "Navigation",
                                modifier = Modifier.size(Size.iconMedium)
                            )
                        }
                    }
                },
                actions = actions ?: {},
                colors = colors,
                scrollBehavior = scrollBehavior
            )
        }
        AppBarSize.CenterAligned -> {
            CenterAlignedTopAppBar(
                title = {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                },
                modifier = modifier,
                navigationIcon = {
                    navigationIcon?.let { icon ->
                        IconButton(
                            onClick = onNavigationClick ?: {}
                        ) {
                            Icon(
                                imageVector = icon,
                                contentDescription = "Navigation",
                                modifier = Modifier.size(Size.iconMedium)
                            )
                        }
                    }
                },
                actions = actions ?: {},
                colors = colors,
                scrollBehavior = scrollBehavior
            )
        }
    }
}

/**
 * Home screen app bar with search and cart
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeAppBar(
    title: String,
    onSearchClick: () -> Unit,
    onCartClick: () -> Unit,
    onProfileClick: () -> Unit,
    modifier: Modifier = Modifier,
    cartItemCount: Int = 0,
    userAvatarUrl: String? = null,
    scrollBehavior: TopAppBarScrollBehavior? = null
) {
    MaomaoTopAppBar(
        title = title,
        modifier = modifier,
        size = AppBarSize.Large,
        scrollBehavior = scrollBehavior,
        actions = {
            // Search button
            IconButton(onClick = onSearchClick) {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "Search",
                    modifier = Modifier.size(Size.iconMedium)
                )
            }
            
            // Cart button with badge
            IconButton(onClick = onCartClick) {
                if (cartItemCount > 0) {
                    BadgedBox(
                        badge = {
                            Badge(
                                containerColor = MaterialTheme.colorScheme.error,
                                contentColor = MaterialTheme.colorScheme.onError
                            ) {
                                Text(
                                    text = if (cartItemCount > 99) "99+" else cartItemCount.toString(),
                                    style = MaterialTheme.typography.labelSmall
                                )
                            }
                        }
                    ) {
                        Icon(
                            imageVector = Icons.Default.ShoppingCart,
                            contentDescription = "Shopping Cart",
                            modifier = Modifier.size(Size.iconMedium)
                        )
                    }
                } else {
                    Icon(
                        imageVector = Icons.Default.ShoppingCart,
                        contentDescription = "Shopping Cart",
                        modifier = Modifier.size(Size.iconMedium)
                    )
                }
            }
            
            // Profile button
            IconButton(onClick = onProfileClick) {
                if (userAvatarUrl != null) {
                    AvatarImage(
                        imageUrl = userAvatarUrl,
                        contentDescription = "Profile",
                        size = Size.iconLarge
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.Menu,
                        contentDescription = "Profile",
                        modifier = Modifier.size(Size.iconMedium)
                    )
                }
            }
        }
    )
}

/**
 * Detail screen app bar with back navigation
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DetailAppBar(
    title: String,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
    actions: (@Composable RowScope.() -> Unit)? = null,
    scrollBehavior: TopAppBarScrollBehavior? = null
) {
    MaomaoTopAppBar(
        title = title,
        modifier = modifier,
        size = AppBarSize.Small,
        navigationIcon = Icons.Default.ArrowBack,
        onNavigationClick = onBackClick,
        actions = actions,
        scrollBehavior = scrollBehavior
    )
}

/**
 * Search app bar with search functionality
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchAppBar(
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "Search products...",
    scrollBehavior: TopAppBarScrollBehavior? = null
) {
    TopAppBar(
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Spacing.small)
            ) {
                com.tfkcolin.maomao.ui.components.inputs.SearchTextField(
                    value = searchQuery,
                    onValueChange = onSearchQueryChange,
                    onSearch = { /* Handle search */ },
                    placeholder = placeholder,
                    modifier = Modifier.weight(1f)
                )
            }
        },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = onBackClick) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Back",
                    modifier = Modifier.size(Size.iconMedium)
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.surface,
            titleContentColor = MaterialTheme.colorScheme.onSurface,
            navigationIconContentColor = MaterialTheme.colorScheme.onSurface
        ),
        scrollBehavior = scrollBehavior
    )
}

/**
 * Action app bar for selection modes
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ActionAppBar(
    selectedCount: Int,
    onCloseClick: () -> Unit,
    modifier: Modifier = Modifier,
    actions: (@Composable RowScope.() -> Unit)? = null
) {
    TopAppBar(
        title = {
            Text(
                text = "$selectedCount selected",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Medium
                )
            )
        },
        modifier = modifier,
        navigationIcon = {
            IconButton(onClick = onCloseClick) {
                Icon(
                    imageVector = Icons.Default.ArrowBack,
                    contentDescription = "Close selection",
                    modifier = Modifier.size(Size.iconMedium)
                )
            }
        },
        actions = actions ?: {},
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer,
            titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer,
            navigationIconContentColor = MaterialTheme.colorScheme.onPrimaryContainer,
            actionIconContentColor = MaterialTheme.colorScheme.onPrimaryContainer
        )
    )
}
