package com.tfkcolin.maomao.ui.components.buttons

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.GroupBuyPrimary
import com.tfkcolin.maomao.ui.theme.PriceRed
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * Add to Cart button with shopping cart icon
 * Primary action for product pages
 */
@Composable
fun AddToCartButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    enabled: Boolean = true,
    fullWidth: Boolean = true
) {
    PrimaryButton(
        text = "Add to Cart",
        onClick = onClick,
        modifier = modifier,
        size = ButtonSize.Large,
        isLoading = isLoading,
        enabled = enabled,
        leadingIcon = Icons.Default.ShoppingCart,
        fullWidth = fullWidth
    )
}

/**
 * Buy Now button for immediate purchase
 * High-priority action with distinctive styling
 */
@Composable
fun BuyNowButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    enabled: Boolean = true,
    fullWidth: Boolean = true
) {
    Button(
        onClick = onClick,
        modifier = modifier
            .then(if (fullWidth) Modifier.fillMaxWidth() else Modifier),
        enabled = enabled && !isLoading,
        shape = RoundedCornerShape(BorderRadius.button),
        colors = ButtonDefaults.buttonColors(
            containerColor = PriceRed,
            contentColor = Color.White,
            disabledContainerColor = MaterialTheme.colorScheme.outline,
            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        )
    ) {
        ButtonContent(
            text = "Buy Now",
            isLoading = isLoading,
            leadingIcon = null,
            trailingIcon = null,
            size = ButtonSize.Large
        )
    }
}

/**
 * Join Group Buy button with special styling
 */
@Composable
fun JoinGroupBuyButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    enabled: Boolean = true,
    participantCount: Int = 0,
    maxParticipants: Int = 0,
    fullWidth: Boolean = true
) {
    Button(
        onClick = onClick,
        modifier = modifier
            .then(if (fullWidth) Modifier.fillMaxWidth() else Modifier),
        enabled = enabled && !isLoading,
        shape = RoundedCornerShape(BorderRadius.button),
        colors = ButtonDefaults.buttonColors(
            containerColor = GroupBuyPrimary,
            contentColor = Color.White,
            disabledContainerColor = MaterialTheme.colorScheme.outline,
            disabledContentColor = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        )
    ) {
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = null,
                modifier = Modifier.size(Size.iconMedium)
            )
            Spacer(modifier = Modifier.width(Spacing.small))
            Text(
                text = "Join Group Buy ($participantCount/$maxParticipants)",
                style = MaterialTheme.typography.labelLarge.copy(fontWeight = FontWeight.Medium)
            )
        }
    }
}

/**
 * Wishlist/Favorite button toggle
 */
@Composable
fun WishlistButton(
    isFavorited: Boolean,
    onToggle: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    IconButton(
        onClick = onToggle,
        modifier = modifier,
        enabled = enabled
    ) {
        Icon(
            imageVector = if (isFavorited) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
            contentDescription = if (isFavorited) "Remove from wishlist" else "Add to wishlist",
            tint = if (isFavorited) PriceRed else MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.size(Size.iconMedium)
        )
    }
}

/**
 * Quantity selector buttons for cart items
 */
@Composable
fun QuantityButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    OutlinedButton(
        onClick = onClick,
        modifier = modifier.size(32.dp),
        enabled = enabled,
        shape = RoundedCornerShape(BorderRadius.small),
        contentPadding = androidx.compose.foundation.layout.PaddingValues(0.dp)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Bold
        )
    }
}

/**
 * Product action buttons row for product cards
 * Contains Add to Cart and Wishlist buttons
 */
@Composable
fun ProductActionButtons(
    onAddToCart: () -> Unit,
    onToggleWishlist: () -> Unit,
    isFavorited: Boolean,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(Spacing.small),
        verticalAlignment = Alignment.CenterVertically
    ) {
        PrimaryButton(
            text = "Add to Cart",
            onClick = onAddToCart,
            modifier = Modifier.weight(1f),
            size = ButtonSize.Medium,
            isLoading = isLoading,
            enabled = enabled,
            leadingIcon = Icons.Default.ShoppingCart
        )
        
        WishlistButton(
            isFavorited = isFavorited,
            onToggle = onToggleWishlist,
            enabled = enabled
        )
    }
}

/**
 * Checkout action buttons for cart screen
 */
@Composable
fun CheckoutButtons(
    onContinueShopping: () -> Unit,
    onProceedToCheckout: () -> Unit,
    modifier: Modifier = Modifier,
    isCheckoutEnabled: Boolean = true,
    isLoading: Boolean = false
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(Spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        OutlinedMaomaoButton(
            text = "Continue Shopping",
            onClick = onContinueShopping,
            modifier = Modifier.weight(1f),
            size = ButtonSize.Large
        )
        
        PrimaryButton(
            text = "Checkout",
            onClick = onProceedToCheckout,
            modifier = Modifier.weight(1f),
            size = ButtonSize.Large,
            enabled = isCheckoutEnabled,
            isLoading = isLoading
        )
    }
}

/**
 * Filter and sort buttons for product listings
 */
@Composable
fun FilterSortButtons(
    onFilterClick: () -> Unit,
    onSortClick: () -> Unit,
    modifier: Modifier = Modifier,
    hasActiveFilters: Boolean = false
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(Spacing.small)
    ) {
        OutlinedMaomaoButton(
            text = if (hasActiveFilters) "Filters (Active)" else "Filters",
            onClick = onFilterClick,
            modifier = Modifier.weight(1f),
            size = ButtonSize.Medium
        )
        
        OutlinedMaomaoButton(
            text = "Sort",
            onClick = onSortClick,
            modifier = Modifier.weight(1f),
            size = ButtonSize.Medium
        )
    }
}
