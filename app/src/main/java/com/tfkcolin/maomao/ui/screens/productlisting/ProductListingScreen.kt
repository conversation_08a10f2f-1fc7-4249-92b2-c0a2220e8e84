package com.tfkcolin.maomao.ui.screens.productlisting

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.models.ProductVariant
import com.tfkcolin.maomao.ui.components.ProductCard

@Composable
fun ProductListingScreen(
    categoryId: String?,
    onNavigateToProductDetail: (productId: String) -> Unit
) {
    // Mock data for products using the new data models
    val products = remember {
        listOf(
            Product(id = "listProd1", name = "Elegant Vase", description = "Hand-blown glass vase.", coverImageUrl = "url_vase.jpg"),
            Product(id = "listProd2", name = "Wireless Earbuds", description = "High-fidelity sound.", coverImageUrl = "url_earbuds.jpg"),
            Product(id = "listProd3", name = "Smartwatch", description = "Track your fitness.", coverImageUrl = "url_watch.jpg")
        )
    }

    // Mock product variants for displaying prices in ProductCard
    val productVariant1 = remember { ProductVariant(id = "pv_list1", productId = "listProd1", name = "Clear", myPrice = 50.00, currency = "$") }
    val productVariant2 = remember { ProductVariant(id = "pv_list2", productId = "listProd2", name = "Black", myPrice = 75.00, currency = "$") }
    val productVariant3 = remember { ProductVariant(id = "pv_list3", productId = "listProd3", name = "Silver", myPrice = 25.00, currency = "$") }


    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item { Text(text = "Product Listing Screen for Category: $categoryId", modifier = Modifier.padding(16.dp)) }
        // Placeholder for sorting and filtering UI
        item { Text(text = "Sorting and Filtering Options Placeholder", modifier = Modifier.padding(8.dp)) }

        items(products) { product ->
            val variant = when (product.id) {
                "listProd1" -> productVariant1
                "listProd2" -> productVariant2
                "listProd3" -> productVariant3
                else -> ProductVariant(id = "default", productId = product.id, name = "Default Variant", myPrice = 0.0, currency = "$") // Provide a default non-null variant
            }
            ProductCard(product = product, productVariant = variant, onProductClick = onNavigateToProductDetail)
        }
    }
}
