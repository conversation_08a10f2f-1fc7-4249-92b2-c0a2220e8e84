package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.OrderItem
import com.tfkcolin.maomao.ui.components.QuantitySelector

/**
 * A reusable composable that displays the details of a single item within the Shopping Cart,
 * Checkout Review, or Order Details screens.
 *
 * @param orderItem The order item data to display.
 * @param onRemove An optional lambda function invoked when a "Remove" action is triggered for the item.
 * @param onQuantityChange An optional lambda function invoked when the quantity of the item changes.
 *   It receives the new quantity.
 *
 * Usage:
 * ```
 * OrderItemDisplay(
 *     orderItem = OrderItem(
 *         productSnapshot = mapOf("name" to "Product A", "sku" to "SKU001"),
 *         quantity = 2,
 *         pricePerUnitPaid = 10.0,
 *         fulfillmentStatus = "Pending Purchase"
 *     ),
 *     onRemove = { /* remove item from cart */ },
 *     onQuantityChange = { newQty -> /* update item quantity */ }
 * )
 * ```
 */
@Composable
fun OrderItemDisplay(
    orderItem: OrderItem,
    onRemove: (() -> Unit)? = null,
    onQuantityChange: ((Int) -> Unit)? = null
) {
    Column(modifier = Modifier.fillMaxWidth().padding(8.dp)) {
        Text(text = "Product: ${orderItem.productSnapshot["name"] ?: "N/A"}")
        Text(text = "SKU: ${orderItem.productSnapshot["sku"] ?: "N/A"}")
        Text(text = "Price per unit: $${"%.2f".format(orderItem.pricePerUnitPaid)}")
        Text(text = "Fulfillment Status: ${orderItem.fulfillmentStatus}")

        Row(modifier = Modifier.fillMaxWidth()) {
            onQuantityChange?.let {
                QuantitySelector(
                    currentQuantity = orderItem.quantity,
                    onQuantityChange = it,
                    modifier = Modifier.weight(1f)
                )
            } ?: run {
                Text(text = "Quantity: ${orderItem.quantity}", modifier = Modifier.weight(1f))
            }
            onRemove?.let {
                Button(onClick = it, modifier = Modifier.padding(start = 8.dp)) {
                    Text("Remove")
                }
            }
        }
    }
}
