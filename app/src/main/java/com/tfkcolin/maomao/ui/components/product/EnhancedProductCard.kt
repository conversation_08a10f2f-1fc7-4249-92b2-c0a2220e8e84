package com.tfkcolin.maomao.ui.components.product

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.GroupBuy
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.models.ProductVariant
import com.tfkcolin.maomao.ui.components.StatusBadge
import com.tfkcolin.maomao.ui.components.StatusType
import com.tfkcolin.maomao.ui.components.buttons.ProductActionButtons
import com.tfkcolin.maomao.ui.components.cards.MaomaoCard
import com.tfkcolin.maomao.ui.components.images.ProductImage
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.DiscountGreen
import com.tfkcolin.maomao.ui.theme.GroupBuyPrimary
import com.tfkcolin.maomao.ui.theme.PriceRed
import com.tfkcolin.maomao.ui.theme.RatingGold
import com.tfkcolin.maomao.ui.theme.SaleOrange
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * Enhanced product card with modern e-commerce styling
 * Supports regular products, group buys, sales, and wishlist functionality
 */
@Composable
fun EnhancedProductCard(
    product: Product,
    productVariant: ProductVariant,
    onProductClick: (productId: String) -> Unit,
    onAddToCart: () -> Unit,
    onToggleWishlist: () -> Unit,
    modifier: Modifier = Modifier,
    groupBuy: GroupBuy? = null,
    isFavorited: Boolean = false,
    isLoading: Boolean = false,
    rating: Float? = null,
    reviewCount: Int = 0,
    discountPercentage: Int? = null,
    isOnSale: Boolean = false,
    showActions: Boolean = true
) {
    MaomaoCard(
        modifier = modifier.fillMaxWidth(),
        onClick = { onProductClick(product.id) }
    ) {
        Column(
            modifier = Modifier.padding(Spacing.small)
        ) {
            // Product Image with badges
            Box {
                ProductImage(
                    imageUrl = product.coverImageUrl,
                    contentDescription = product.name,
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(1f)
                        .clip(RoundedCornerShape(BorderRadius.medium))
                )
                
                // Badges overlay
                Column(
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .padding(Spacing.small),
                    verticalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
                ) {
                    // Discount badge
                    discountPercentage?.let { discount ->
                        StatusBadge(
                            statusText = "-$discount%",
                            type = StatusType.Success
                        )
                    }
                    
                    // Sale badge
                    if (isOnSale) {
                        StatusBadge(
                            statusText = "SALE",
                            type = StatusType.Warning
                        )
                    }
                    
                    // Group buy badge
                    groupBuy?.let {
                        StatusBadge(
                            statusText = "Group Buy",
                            type = StatusType.Info
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(Spacing.small))
            
            // Product Info
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
            ) {
                // Product Name
                Text(
                    text = product.name,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
                
                // Rating and Reviews
                if (rating != null && rating > 0) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = "Rating",
                            tint = RatingGold,
                            modifier = Modifier.size(Size.iconSmall)
                        )
                        Text(
                            text = String.format("%.1f", rating),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        if (reviewCount > 0) {
                            Text(
                                text = "($reviewCount)",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
                
                // Variant info (if applicable)
                if (productVariant.name.isNotBlank() && productVariant.name != "Default") {
                    Text(
                        text = productVariant.name,
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // Price Section
                PriceSection(
                    productVariant = productVariant,
                    groupBuy = groupBuy,
                    discountPercentage = discountPercentage
                )
                
                // Group Buy Progress (if applicable)
                groupBuy?.let { gb ->
                    GroupBuyProgress(
                        currentParticipants = gb.currentParticipants,
                        maxParticipants = gb.maxParticipants,
                        modifier = Modifier.padding(top = Spacing.small)
                    )
                }
                
                // Action Buttons
                if (showActions) {
                    ProductActionButtons(
                        onAddToCart = onAddToCart,
                        onToggleWishlist = onToggleWishlist,
                        isFavorited = isFavorited,
                        enabled = !isLoading,
                        isLoading = isLoading,
                        modifier = Modifier.padding(top = Spacing.small)
                    )
                }
            }
        }
    }
}

/**
 * Price section component showing current price, original price, and discounts
 */
@Composable
private fun PriceSection(
    productVariant: ProductVariant,
    groupBuy: GroupBuy?,
    discountPercentage: Int?
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Spacing.small)
    ) {
        // Current Price (Group Buy or Regular)
        val currentPrice = groupBuy?.groupPrice ?: productVariant.myPrice
        val currency = productVariant.currency
        
        Text(
            text = "$currency${String.format("%.2f", currentPrice)}",
            style = MaterialTheme.typography.titleMedium.copy(
                fontWeight = FontWeight.Bold
            ),
            color = if (groupBuy != null) GroupBuyPrimary else PriceRed
        )
        
        // Original Price (if discounted)
        if (discountPercentage != null && discountPercentage > 0) {
            Text(
                text = "$currency${String.format("%.2f", productVariant.myPrice)}",
                style = MaterialTheme.typography.bodySmall.copy(
                    textDecoration = TextDecoration.LineThrough
                ),
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
        
        // Savings indicator
        if (groupBuy != null) {
            val savings = productVariant.myPrice - groupBuy.groupPrice
            if (savings > 0) {
                Text(
                    text = "Save $currency${String.format("%.2f", savings)}",
                    style = MaterialTheme.typography.bodySmall,
                    color = DiscountGreen
                )
            }
        }
    }
}

/**
 * Group buy progress indicator
 */
@Composable
private fun GroupBuyProgress(
    currentParticipants: Int,
    maxParticipants: Int,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Group Buy Progress",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = "$currentParticipants/$maxParticipants",
                style = MaterialTheme.typography.bodySmall.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = GroupBuyPrimary
            )
        }
        
        // Progress bar
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(4.dp)
                .clip(RoundedCornerShape(2.dp))
        ) {
            // Background
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(MaterialTheme.colorScheme.outline.copy(alpha = 0.3f))
            )
            
            // Progress
            val progress = (currentParticipants.toFloat() / maxParticipants.toFloat()).coerceIn(0f, 1f)
            Box(
                modifier = Modifier
                    .fillMaxWidth(progress)
                    .height(4.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(GroupBuyPrimary)
            )
        }
    }
}

/**
 * Compact product card for list view
 */
@Composable
fun CompactProductCard(
    product: Product,
    productVariant: ProductVariant,
    onProductClick: (productId: String) -> Unit,
    onAddToCart: () -> Unit,
    onToggleWishlist: () -> Unit,
    modifier: Modifier = Modifier,
    groupBuy: GroupBuy? = null,
    isFavorited: Boolean = false,
    isLoading: Boolean = false,
    rating: Float? = null,
    reviewCount: Int = 0
) {
    MaomaoCard(
        modifier = modifier.fillMaxWidth(),
        onClick = { onProductClick(product.id) }
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium),
            horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            // Product Image
            ProductImage(
                imageUrl = product.coverImageUrl,
                contentDescription = product.name,
                modifier = Modifier.size(Size.productImageSmall)
            )

            // Product Info
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
            ) {
                Text(
                    text = product.name,
                    style = MaterialTheme.typography.titleSmall,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )

                if (rating != null && rating > 0) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = "Rating",
                            tint = RatingGold,
                            modifier = Modifier.size(12.dp)
                        )
                        Text(
                            text = String.format("%.1f", rating),
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }

                PriceSection(
                    productVariant = productVariant,
                    groupBuy = groupBuy,
                    discountPercentage = null
                )
            }

            // Action buttons
            ProductActionButtons(
                onAddToCart = onAddToCart,
                onToggleWishlist = onToggleWishlist,
                isFavorited = isFavorited,
                enabled = !isLoading,
                isLoading = isLoading
            )
        }
    }
}
