package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.ShippingAddress

/**
 * A reusable composable that displays a single saved address in a card format.
 * It is used in the `ManageAddressesScreen` and potentially in checkout flows for address selection.
 *
 * @param address The address data to display.
 * @param onEditClick An optional lambda function invoked when the "Edit" action is triggered for this address.
 * @param onDeleteClick An optional lambda function invoked when the "Delete" action is triggered for this address.
 * @param onSelect A lambda function invoked when the address card itself is clicked,
 *   typically for selection purposes (e.g., in checkout).
 *
 * Usage:
 * ```
 * AddressDisplayCard(
 *     address = ShippingAddress(
 *         id = "addr1",
 *         addressLine1 = "123 Main St",
 *         city = "Anytown",
 *         stateOrProvince = "CA",
 *         postalCode = "90210",
 *         country = "USA"
 *     ),
 *     onEditClick = { addressId -> /* navigate to edit form */ },
 *     onDeleteClick = { addressId -> /* delete address */ },
 *     onSelect = { addressId -> /* select address for checkout */ }
 * )
 * ```
 */
@Composable
fun AddressDisplayCard(
    address: ShippingAddress,
    onEditClick: ((addressId: String) -> Unit)? = null,
    onDeleteClick: ((addressId: String) -> Unit)? = null,
    onSelect: (addressId: String) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
            .clickable { onSelect(address.id) }
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Text(text = address.addressLine1)
            address.addressLine2?.let { Text(text = it) }
            Text(text = "${address.city}, ${address.stateOrProvince} ${address.postalCode}")
            Text(text = address.country)
            if (address.isDefault) {
                Text(text = "(Default Address)", style = MaterialTheme.typography.bodySmall)
            }

            Row(modifier = Modifier.fillMaxWidth().padding(top = 8.dp)) {
                onEditClick?.let {
                    Button(onClick = { it(address.id) }, modifier = Modifier.weight(1f).padding(end = 4.dp)) {
                        Text("Edit")
                    }
                }
                onDeleteClick?.let {
                    Button(onClick = { it(address.id) }, modifier = Modifier.weight(1f).padding(start = 4.dp)) {
                        Text("Delete")
                    }
                }
            }
        }
    }
}
