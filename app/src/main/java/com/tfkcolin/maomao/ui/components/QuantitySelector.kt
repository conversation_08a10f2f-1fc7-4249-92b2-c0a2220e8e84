package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * A reusable composable that provides controls for incrementing or decrementing the quantity of an item.
 * It is typically used in the Shopping Cart.
 *
 * @param currentQuantity The current quantity of the item.
 * @param onQuantityChange A lambda function invoked when the quantity changes. It receives the new quantity.
 * @param maxQuantity The maximum allowed quantity for the item. Defaults to 99.
 *
 * Usage:
 * ```
 * QuantitySelector(
 *     currentQuantity = 1,
 *     onQuantityChange = { newQty -> /* update item quantity */ },
 *     maxQuantity = 5
 * )
 * ```
 */
@Composable
fun QuantitySelector(
    modifier: Modifier = Modifier,
    currentQuantity: Int,
    onQuantityChange: (newQuantity: Int) -> Unit,
    maxQuantity: Int = 99
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Button(
            onClick = { if (currentQuantity > 1) onQuantityChange(currentQuantity - 1) },
            enabled = currentQuantity > 1,
            modifier = Modifier.padding(4.dp)
        ) {
            Text("-")
        }
        Text(text = "$currentQuantity", modifier = Modifier.padding(horizontal = 8.dp))
        Button(
            onClick = { if (currentQuantity < maxQuantity) onQuantityChange(currentQuantity + 1) },
            enabled = currentQuantity < maxQuantity,
            modifier = Modifier.padding(4.dp)
        ) {
            Text("+")
        }
    }
}
