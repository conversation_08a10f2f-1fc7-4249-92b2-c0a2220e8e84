package com.tfkcolin.maomao.ui.components.auth

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Phone
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.ui.components.buttons.PrimaryButton
import com.tfkcolin.maomao.ui.components.buttons.TextMaomaoButton
import com.tfkcolin.maomao.ui.components.cards.MaomaoCard
import com.tfkcolin.maomao.ui.components.inputs.EmailTextField
import com.tfkcolin.maomao.ui.components.inputs.MaomaoTextField
import com.tfkcolin.maomao.ui.components.inputs.PasswordTextField
import com.tfkcolin.maomao.ui.components.inputs.PhoneTextField
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * Authentication form data class
 */
data class AuthFormState(
    val email: String = "",
    val password: String = "",
    val confirmPassword: String = "",
    val fullName: String = "",
    val phoneNumber: String = "",
    val emailError: String? = null,
    val passwordError: String? = null,
    val confirmPasswordError: String? = null,
    val fullNameError: String? = null,
    val phoneNumberError: String? = null,
    val isLoading: Boolean = false
)

/**
 * Login form component
 */
@Composable
fun LoginForm(
    email: String,
    password: String,
    onEmailChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onLoginClick: () -> Unit,
    onForgotPasswordClick: () -> Unit,
    modifier: Modifier = Modifier,
    emailError: String? = null,
    passwordError: String? = null,
    isLoading: Boolean = false,
    enabled: Boolean = true
) {
    val focusManager = LocalFocusManager.current
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        // Email field
        EmailTextField(
            value = email,
            onValueChange = onEmailChange,
            isError = emailError != null,
            errorMessage = emailError,
            enabled = enabled && !isLoading,
            imeAction = ImeAction.Next,
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        )
        
        // Password field
        PasswordTextField(
            value = password,
            onValueChange = onPasswordChange,
            label = "Password",
            isError = passwordError != null,
            errorMessage = passwordError,
            enabled = enabled && !isLoading,
            imeAction = ImeAction.Done,
            keyboardActions = KeyboardActions(
                onDone = { 
                    focusManager.clearFocus()
                    if (email.isNotBlank() && password.isNotBlank()) {
                        onLoginClick()
                    }
                }
            )
        )
        
        // Forgot password link
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            TextMaomaoButton(
                text = "Forgot Password?",
                onClick = onForgotPasswordClick,
                enabled = enabled && !isLoading
            )
        }
        
        // Login button
        PrimaryButton(
            text = "Sign In",
            onClick = onLoginClick,
            modifier = Modifier.fillMaxWidth(),
            isLoading = isLoading,
            enabled = enabled && email.isNotBlank() && password.isNotBlank(),
            fullWidth = true
        )
    }
}

/**
 * Registration form component
 */
@Composable
fun RegistrationForm(
    fullName: String,
    email: String,
    phoneNumber: String,
    password: String,
    confirmPassword: String,
    onFullNameChange: (String) -> Unit,
    onEmailChange: (String) -> Unit,
    onPhoneNumberChange: (String) -> Unit,
    onPasswordChange: (String) -> Unit,
    onConfirmPasswordChange: (String) -> Unit,
    onRegisterClick: () -> Unit,
    modifier: Modifier = Modifier,
    fullNameError: String? = null,
    emailError: String? = null,
    phoneNumberError: String? = null,
    passwordError: String? = null,
    confirmPasswordError: String? = null,
    isLoading: Boolean = false,
    enabled: Boolean = true
) {
    val focusManager = LocalFocusManager.current
    
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        // Full name field
        MaomaoTextField(
            value = fullName,
            onValueChange = onFullNameChange,
            label = "Full Name",
            leadingIcon = Icons.Default.Person,
            isError = fullNameError != null,
            errorMessage = fullNameError,
            enabled = enabled && !isLoading,
            imeAction = ImeAction.Next,
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        )
        
        // Email field
        EmailTextField(
            value = email,
            onValueChange = onEmailChange,
            isError = emailError != null,
            errorMessage = emailError,
            enabled = enabled && !isLoading,
            imeAction = ImeAction.Next,
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        )
        
        // Phone number field
        PhoneTextField(
            value = phoneNumber,
            onValueChange = onPhoneNumberChange,
            isError = phoneNumberError != null,
            errorMessage = phoneNumberError,
            enabled = enabled && !isLoading,
            imeAction = ImeAction.Next,
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        )
        
        // Password field
        PasswordTextField(
            value = password,
            onValueChange = onPasswordChange,
            label = "Password",
            isError = passwordError != null,
            errorMessage = passwordError,
            enabled = enabled && !isLoading,
            imeAction = ImeAction.Next,
            keyboardActions = KeyboardActions(
                onNext = { focusManager.moveFocus(FocusDirection.Down) }
            )
        )
        
        // Confirm password field
        PasswordTextField(
            value = confirmPassword,
            onValueChange = onConfirmPasswordChange,
            label = "Confirm Password",
            isError = confirmPasswordError != null,
            errorMessage = confirmPasswordError,
            enabled = enabled && !isLoading,
            imeAction = ImeAction.Done,
            keyboardActions = KeyboardActions(
                onDone = { 
                    focusManager.clearFocus()
                    if (isFormValid(fullName, email, phoneNumber, password, confirmPassword)) {
                        onRegisterClick()
                    }
                }
            )
        )
        
        // Register button
        PrimaryButton(
            text = "Create Account",
            onClick = onRegisterClick,
            modifier = Modifier.fillMaxWidth(),
            isLoading = isLoading,
            enabled = enabled && isFormValid(fullName, email, phoneNumber, password, confirmPassword),
            fullWidth = true
        )
    }
}

/**
 * Social login buttons section
 */
@Composable
fun SocialLoginSection(
    onGoogleLogin: () -> Unit,
    onFacebookLogin: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        // Divider with "OR" text
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            HorizontalDivider(modifier = Modifier.weight(1f))
            Text(
                text = "OR",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            HorizontalDivider(modifier = Modifier.weight(1f))
        }
        
        // Social login buttons
        Column(
            verticalArrangement = Arrangement.spacedBy(Spacing.small)
        ) {
            SocialLoginButton(
                text = "Continue with Google",
                onClick = onGoogleLogin,
                backgroundColor = Color.White,
                textColor = Color.Black,
                enabled = enabled && !isLoading,
                modifier = Modifier.fillMaxWidth()
            )
            
            SocialLoginButton(
                text = "Continue with Facebook",
                onClick = onFacebookLogin,
                backgroundColor = Color(0xFF1877F2),
                textColor = Color.White,
                enabled = enabled && !isLoading,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * Individual social login button
 */
@Composable
fun SocialLoginButton(
    text: String,
    onClick: () -> Unit,
    backgroundColor: Color,
    textColor: Color,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    icon: (@Composable () -> Unit)? = null
) {
    MaomaoCard(
        modifier = modifier
            .fillMaxWidth()
            .clickable(enabled = enabled) { onClick() },
        containerColor = backgroundColor
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            icon?.let { iconContent ->
                iconContent()
                Spacer(modifier = Modifier.width(Spacing.small))
            }
            
            Text(
                text = text,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = textColor
            )
        }
    }
}

/**
 * Auth mode toggle (Login/Register)
 */
@Composable
fun AuthModeToggle(
    isLoginMode: Boolean,
    onToggleMode: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = if (isLoginMode) "Don't have an account?" else "Already have an account?",
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        TextButton(onClick = onToggleMode) {
            Text(
                text = if (isLoginMode) "Sign Up" else "Sign In",
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

/**
 * Terms and privacy policy text
 */
@Composable
fun TermsAndPrivacyText(
    onTermsClick: () -> Unit,
    onPrivacyClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Text(
        text = "By continuing, you agree to our Terms of Service and Privacy Policy",
        style = MaterialTheme.typography.bodySmall,
        color = MaterialTheme.colorScheme.onSurfaceVariant,
        textAlign = TextAlign.Center,
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = Spacing.medium)
            .clickable { onTermsClick() } // Simplified - in real app, make specific parts clickable
    )
}

/**
 * Helper function to validate registration form
 */
private fun isFormValid(
    fullName: String,
    email: String,
    phoneNumber: String,
    password: String,
    confirmPassword: String
): Boolean {
    return fullName.isNotBlank() &&
            email.isNotBlank() &&
            phoneNumber.isNotBlank() &&
            password.isNotBlank() &&
            confirmPassword.isNotBlank() &&
            password == confirmPassword
}
