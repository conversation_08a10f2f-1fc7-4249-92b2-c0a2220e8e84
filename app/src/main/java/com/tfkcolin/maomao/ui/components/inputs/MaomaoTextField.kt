package com.tfkcolin.maomao.ui.components.inputs

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.VisibilityOff
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.Spacing

/**
 * Text field variants
 */
enum class TextFieldVariant {
    Filled, Outlined
}

/**
 * Reusable text field component with validation and error handling
 */
@Composable
fun MaomaoTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    variant: TextFieldVariant = TextFieldVariant.Outlined,
    placeholder: String? = null,
    leadingIcon: ImageVector? = null,
    trailingIcon: ImageVector? = null,
    onTrailingIconClick: (() -> Unit)? = null,
    isError: Boolean = false,
    errorMessage: String? = null,
    helperText: String? = null,
    enabled: Boolean = true,
    readOnly: Boolean = false,
    singleLine: Boolean = true,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    keyboardType: KeyboardType = KeyboardType.Text,
    imeAction: ImeAction = ImeAction.Next,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    visualTransformation: VisualTransformation = VisualTransformation.None
) {
    Column(modifier = modifier) {
        when (variant) {
            TextFieldVariant.Filled -> {
                TextField(
                    value = value,
                    onValueChange = onValueChange,
                    label = { Text(label) },
                    placeholder = placeholder?.let { { Text(it) } },
                    leadingIcon = leadingIcon?.let { { Icon(it, contentDescription = null) } },
                    trailingIcon = {
                        when {
                            isError -> Icon(
                                Icons.Default.Error,
                                contentDescription = "Error",
                                tint = MaterialTheme.colorScheme.error
                            )
                            trailingIcon != null -> {
                                if (onTrailingIconClick != null) {
                                    IconButton(onClick = onTrailingIconClick) {
                                        Icon(trailingIcon, contentDescription = null)
                                    }
                                } else {
                                    Icon(trailingIcon, contentDescription = null)
                                }
                            }
                        }
                    },
                    isError = isError,
                    enabled = enabled,
                    readOnly = readOnly,
                    singleLine = singleLine,
                    maxLines = maxLines,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = keyboardType,
                        imeAction = imeAction
                    ),
                    keyboardActions = keyboardActions,
                    visualTransformation = visualTransformation,
                    shape = RoundedCornerShape(BorderRadius.textField),
                    modifier = Modifier.fillMaxWidth()
                )
            }
            TextFieldVariant.Outlined -> {
                OutlinedTextField(
                    value = value,
                    onValueChange = onValueChange,
                    label = { Text(label) },
                    placeholder = placeholder?.let { { Text(it) } },
                    leadingIcon = leadingIcon?.let { { Icon(it, contentDescription = null) } },
                    trailingIcon = {
                        when {
                            isError -> Icon(
                                Icons.Default.Error,
                                contentDescription = "Error",
                                tint = MaterialTheme.colorScheme.error
                            )
                            trailingIcon != null -> {
                                if (onTrailingIconClick != null) {
                                    IconButton(onClick = onTrailingIconClick) {
                                        Icon(trailingIcon, contentDescription = null)
                                    }
                                } else {
                                    Icon(trailingIcon, contentDescription = null)
                                }
                            }
                        }
                    },
                    isError = isError,
                    enabled = enabled,
                    readOnly = readOnly,
                    singleLine = singleLine,
                    maxLines = maxLines,
                    keyboardOptions = KeyboardOptions(
                        keyboardType = keyboardType,
                        imeAction = imeAction
                    ),
                    keyboardActions = keyboardActions,
                    visualTransformation = visualTransformation,
                    shape = RoundedCornerShape(BorderRadius.textField),
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
        
        // Helper text or error message
        if (isError && errorMessage != null) {
            Text(
                text = errorMessage,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = Spacing.medium, top = Spacing.extraSmall)
            )
        } else if (helperText != null) {
            Text(
                text = helperText,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                style = MaterialTheme.typography.bodySmall,
                modifier = Modifier.padding(start = Spacing.medium, top = Spacing.extraSmall)
            )
        }
    }
}

/**
 * Password text field with visibility toggle
 */
@Composable
fun PasswordTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    variant: TextFieldVariant = TextFieldVariant.Outlined,
    placeholder: String? = null,
    isError: Boolean = false,
    errorMessage: String? = null,
    helperText: String? = null,
    enabled: Boolean = true,
    imeAction: ImeAction = ImeAction.Done,
    keyboardActions: KeyboardActions = KeyboardActions.Default
) {
    var passwordVisible by remember { mutableStateOf(false) }
    
    MaomaoTextField(
        value = value,
        onValueChange = onValueChange,
        label = label,
        modifier = modifier,
        variant = variant,
        placeholder = placeholder,
        trailingIcon = if (passwordVisible) Icons.Default.Visibility else Icons.Default.VisibilityOff,
        onTrailingIconClick = { passwordVisible = !passwordVisible },
        isError = isError,
        errorMessage = errorMessage,
        helperText = helperText,
        enabled = enabled,
        keyboardType = KeyboardType.Password,
        imeAction = imeAction,
        keyboardActions = keyboardActions,
        visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation()
    )
}

/**
 * Email text field with email keyboard type
 */
@Composable
fun EmailTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    variant: TextFieldVariant = TextFieldVariant.Outlined,
    placeholder: String? = null,
    isError: Boolean = false,
    errorMessage: String? = null,
    helperText: String? = null,
    enabled: Boolean = true,
    imeAction: ImeAction = ImeAction.Next,
    keyboardActions: KeyboardActions = KeyboardActions.Default
) {
    MaomaoTextField(
        value = value,
        onValueChange = onValueChange,
        label = "Email",
        modifier = modifier,
        variant = variant,
        placeholder = placeholder ?: "Enter your email",
        isError = isError,
        errorMessage = errorMessage,
        helperText = helperText,
        enabled = enabled,
        keyboardType = KeyboardType.Email,
        imeAction = imeAction,
        keyboardActions = keyboardActions
    )
}

/**
 * Phone number text field
 */
@Composable
fun PhoneTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    variant: TextFieldVariant = TextFieldVariant.Outlined,
    placeholder: String? = null,
    isError: Boolean = false,
    errorMessage: String? = null,
    helperText: String? = null,
    enabled: Boolean = true,
    imeAction: ImeAction = ImeAction.Next,
    keyboardActions: KeyboardActions = KeyboardActions.Default
) {
    MaomaoTextField(
        value = value,
        onValueChange = onValueChange,
        label = "Phone Number",
        modifier = modifier,
        variant = variant,
        placeholder = placeholder ?: "Enter your phone number",
        isError = isError,
        errorMessage = errorMessage,
        helperText = helperText,
        enabled = enabled,
        keyboardType = KeyboardType.Phone,
        imeAction = imeAction,
        keyboardActions = keyboardActions
    )
}

/**
 * Search text field with search styling
 */
@Composable
fun SearchTextField(
    value: String,
    onValueChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    modifier: Modifier = Modifier,
    placeholder: String = "Search products...",
    enabled: Boolean = true
) {
    MaomaoTextField(
        value = value,
        onValueChange = onValueChange,
        label = "Search",
        modifier = modifier,
        variant = TextFieldVariant.Filled,
        placeholder = placeholder,
        enabled = enabled,
        keyboardType = KeyboardType.Text,
        imeAction = ImeAction.Search,
        keyboardActions = KeyboardActions(
            onSearch = { onSearch(value) }
        )
    )
}

/**
 * Multiline text field for descriptions, reviews, etc.
 */
@Composable
fun MultilineTextField(
    value: String,
    onValueChange: (String) -> Unit,
    label: String,
    modifier: Modifier = Modifier,
    variant: TextFieldVariant = TextFieldVariant.Outlined,
    placeholder: String? = null,
    isError: Boolean = false,
    errorMessage: String? = null,
    helperText: String? = null,
    enabled: Boolean = true,
    minLines: Int = 3,
    maxLines: Int = 6,
    maxLength: Int? = null
) {
    val displayValue = if (maxLength != null && value.length > maxLength) {
        value.take(maxLength)
    } else {
        value
    }

    Column(modifier = modifier) {
        MaomaoTextField(
            value = displayValue,
            onValueChange = { newValue ->
                if (maxLength == null || newValue.length <= maxLength) {
                    onValueChange(newValue)
                }
            },
            label = label,
            variant = variant,
            placeholder = placeholder,
            isError = isError,
            errorMessage = errorMessage,
            helperText = helperText,
            enabled = enabled,
            singleLine = false,
            maxLines = maxLines,
            keyboardType = KeyboardType.Text,
            imeAction = ImeAction.Default
        )

        // Character counter
        if (maxLength != null) {
            Text(
                text = "${displayValue.length}/$maxLength",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = Spacing.medium, top = Spacing.extraSmall)
            )
        }
    }
}
