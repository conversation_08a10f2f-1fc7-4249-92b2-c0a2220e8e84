package com.tfkcolin.maomao.ui.screens.checkout

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.foundation.layout.Column
import androidx.compose.material3.Button
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier

@Composable
fun OrderConfirmationScreen(
    orderNumber: String?,
    onNavigateToHome: () -> Unit,
    onNavigateToMyOrders: () -> Unit
) {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Text(text = "Order Confirmation Screen for Order: $orderNumber")
            Button(onClick = onNavigateToHome) {
                Text("Back to Home")
            }
            Button(onClick = onNavigateToMyOrders) {
                Text("View My Orders")
            }
        }
    }
}
