package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Checkbox
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.ShippingAddress

/**
 * A reusable composable form for entering or editing shipping addresses.
 * It is used in the Checkout flow and Account Management.
 *
 * @param address The initial address data to pre-fill the form. Can be null for a new address.
 * @param onSave A lambda function invoked when the "Save" button is clicked. It receives the
 *   completed ShippingAddress data.
 * @param onCancel A lambda function invoked when the "Cancel" button is clicked.
 *
 * Usage:
 * ```
 * AddressForm(
 *     address = existingAddress, // or null for new address
 *     onSave = { newAddress -> /* save address to backend */ },
 *     onCancel = { /* navigate back */ }
 * )
 * ```
 */
@Composable
fun AddressForm(
    address: ShippingAddress?,
    onSave: (address: ShippingAddress) -> Unit,
    onCancel: () -> Unit
) {
    var addressLine1 by remember(address) { mutableStateOf(address?.addressLine1 ?: "") }
    var addressLine2 by remember(address) { mutableStateOf(address?.addressLine2 ?: "") }
    var city by remember(address) { mutableStateOf(address?.city ?: "") }
    var stateOrProvince by remember(address) { mutableStateOf(address?.stateOrProvince ?: "") }
    var postalCode by remember(address) { mutableStateOf(address?.postalCode ?: "") }
    var country by remember(address) { mutableStateOf(address?.country ?: "") }
    var isDefault by remember(address) { mutableStateOf(address?.isDefault ?: false) }

    Column(modifier = Modifier.padding(16.dp)) {
        Text(text = "Address Details")
        OutlinedTextField(
            value = addressLine1,
            onValueChange = { addressLine1 = it },
            label = { Text("Address Line 1") },
            modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
        )
        OutlinedTextField(
            value = addressLine2,
            onValueChange = { addressLine2 = it },
            label = { Text("Address Line 2 (Optional)") },
            modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
        )
        OutlinedTextField(
            value = city,
            onValueChange = { city = it },
            label = { Text("City") },
            modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
        )
        OutlinedTextField(
            value = stateOrProvince,
            onValueChange = { stateOrProvince = it },
            label = { Text("State/Province") },
            modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
        )
        OutlinedTextField(
            value = postalCode,
            onValueChange = { postalCode = it },
            label = { Text("Postal Code") },
            modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
        )
        OutlinedTextField(
            value = country,
            onValueChange = { country = it },
            label = { Text("Country") },
            modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
        )
        // Checkbox for isDefault
        Row(
            modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = isDefault,
                onCheckedChange = { isDefault = it }
            )
            Text("Set as Default Address")
        }

        Button(
            onClick = {
                val newAddress = ShippingAddress(
                    id = address?.id ?: "", // Keep existing ID if editing, or empty for new
                    addressLine1 = addressLine1,
                    addressLine2 = addressLine2.takeIf { it.isNotBlank() },
                    city = city,
                    stateOrProvince = stateOrProvince,
                    postalCode = postalCode,
                    country = country,
                    isDefault = isDefault
                )
                onSave(newAddress)
            },
            modifier = Modifier.fillMaxWidth().padding(vertical = 8.dp)
        ) {
            Text("Save Address")
        }
        Button(
            onClick = onCancel,
            modifier = Modifier.fillMaxWidth().padding(vertical = 4.dp)
        ) {
            Text("Cancel")
        }
    }
}
