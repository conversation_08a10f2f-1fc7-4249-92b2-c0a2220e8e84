package com.tfkcolin.maomao.ui.screens.auth

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.ui.theme.MaomaoTheme
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ForgotPasswordScreen(
    email: String,
    isLoading: Boolean,
    errorMessage: String?,
    authSuccess: Boolean,
    onEmailChange: (String) -> Unit,
    onSendPasswordResetEmail: () -> Unit,
    onBackToSignInClick: () -> Unit,
    onResetAuthSuccess: () -> Unit,
    onResetErrorMessage: () -> Unit
) {
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()

    LaunchedEffect(authSuccess) {
        if (authSuccess) {
            scope.launch {
                snackbarHostState.showSnackbar(
                    message = "Password reset email sent. Check your inbox.",
                    duration = SnackbarDuration.Long
                )
            }
            onResetAuthSuccess()
            onBackToSignInClick()
        }
    }

    LaunchedEffect(errorMessage) {
        errorMessage?.let { message ->
            scope.launch {
                snackbarHostState.showSnackbar(
                    message = message,
                    actionLabel = "Dismiss",
                    duration = SnackbarDuration.Short
                )
                onResetErrorMessage()
            }
        }
    }

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "Reset Password",
                style = MaterialTheme.typography.headlineLarge,
                modifier = Modifier.padding(bottom = 32.dp)
            )

            OutlinedTextField(
                value = email,
                onValueChange = onEmailChange,
                label = { Text("Email") },
                singleLine = true,
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(16.dp))

            Button(
                onClick = onSendPasswordResetEmail,
                enabled = !isLoading,
                modifier = Modifier.fillMaxWidth()
            ) {
                if (isLoading) {
                    CircularProgressIndicator(color = MaterialTheme.colorScheme.onPrimary, modifier = Modifier.size(24.dp))
                } else {
                    Text("Send Reset Email")
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            TextButton(
                onClick = onBackToSignInClick,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Back to Sign In")
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ForgotPasswordScreenPreview() {
    MaomaoTheme {
        ForgotPasswordScreen(
            email = "<EMAIL>",
            isLoading = false,
            errorMessage = null,
            authSuccess = false,
            onEmailChange = {},
            onSendPasswordResetEmail = {},
            onBackToSignInClick = {},
            onResetAuthSuccess = {},
            onResetErrorMessage = {}
        )
    }
}
