package com.tfkcolin.maomao.ui.theme

import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * Design system spacing tokens following Material Design 3 guidelines
 * Provides consistent spacing throughout the app
 */
object Spacing {
    // Base spacing unit (4dp)
    val base: Dp = 4.dp
    
    // Micro spacing (for fine adjustments)
    val micro: Dp = 2.dp
    
    // Extra small spacing
    val extraSmall: Dp = 4.dp
    
    // Small spacing
    val small: Dp = 8.dp
    
    // Medium spacing (default)
    val medium: Dp = 16.dp
    
    // Large spacing
    val large: Dp = 24.dp
    
    // Extra large spacing
    val extraLarge: Dp = 32.dp
    
    // XXL spacing (for major sections)
    val xxl: Dp = 48.dp
    
    // XXXL spacing (for screen-level spacing)
    val xxxl: Dp = 64.dp
    
    // Component-specific spacing
    val buttonPadding: Dp = 16.dp
    val cardPadding: Dp = 16.dp
    val screenPadding: Dp = 16.dp
    val listItemPadding: Dp = 12.dp
    val chipPadding: Dp = 8.dp
    
    // Icon spacing
    val iconSmall: Dp = 16.dp
    val iconMedium: Dp = 24.dp
    val iconLarge: Dp = 32.dp
    val iconExtraLarge: Dp = 48.dp
    
    // Form spacing
    val formFieldSpacing: Dp = 16.dp
    val formSectionSpacing: Dp = 24.dp
    
    // Grid spacing
    val gridSpacing: Dp = 8.dp
    val gridSpacingLarge: Dp = 16.dp
}

/**
 * Elevation tokens for consistent shadows and depth
 */
object Elevation {
    val none: Dp = 0.dp
    val level1: Dp = 1.dp
    val level2: Dp = 3.dp
    val level3: Dp = 6.dp
    val level4: Dp = 8.dp
    val level5: Dp = 12.dp
    
    // Component-specific elevations
    val card: Dp = level1
    val cardHovered: Dp = level2
    val button: Dp = level1
    val fab: Dp = level3
    val bottomSheet: Dp = level4
    val dialog: Dp = level5
    val appBar: Dp = level2
    val navigationBar: Dp = level2
}

/**
 * Border radius tokens for consistent rounded corners
 */
object BorderRadius {
    val none: Dp = 0.dp
    val small: Dp = 4.dp
    val medium: Dp = 8.dp
    val large: Dp = 12.dp
    val extraLarge: Dp = 16.dp
    val xxl: Dp = 24.dp
    val circular: Dp = 50.dp
    
    // Component-specific radius
    val button: Dp = medium
    val card: Dp = large
    val chip: Dp = extraLarge
    val textField: Dp = small
    val dialog: Dp = extraLarge
}

/**
 * Size tokens for consistent component sizing
 */
object Size {
    // Button heights
    val buttonSmall: Dp = 32.dp
    val buttonMedium: Dp = 40.dp
    val buttonLarge: Dp = 48.dp
    
    // Icon sizes
    val iconSmall: Dp = 16.dp
    val iconMedium: Dp = 24.dp
    val iconLarge: Dp = 32.dp
    val iconExtraLarge: Dp = 48.dp
    
    // Avatar sizes
    val avatarSmall: Dp = 32.dp
    val avatarMedium: Dp = 48.dp
    val avatarLarge: Dp = 64.dp
    val avatarExtraLarge: Dp = 96.dp
    
    // Input field heights
    val textFieldSmall: Dp = 40.dp
    val textFieldMedium: Dp = 48.dp
    val textFieldLarge: Dp = 56.dp
    
    // Product image sizes
    val productImageSmall: Dp = 80.dp
    val productImageMedium: Dp = 120.dp
    val productImageLarge: Dp = 200.dp
    val productImageExtraLarge: Dp = 300.dp
    
    // Minimum touch target size (accessibility)
    val minTouchTarget: Dp = 48.dp
}
