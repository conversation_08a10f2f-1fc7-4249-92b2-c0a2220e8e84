package com.tfkcolin.maomao.ui.screens.account

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.Order
import com.tfkcolin.maomao.data.models.OrderItem
import com.tfkcolin.maomao.data.models.ShippingAddress
import com.tfkcolin.maomao.ui.components.OrderItemDisplay
import com.tfkcolin.maomao.ui.components.OrderSummary
import com.tfkcolin.maomao.ui.components.StatusBadge
import com.tfkcolin.maomao.ui.components.StatusType

@Composable
fun OrderDetailsScreen(
    orderId: String?,
    onNavigateToOrderIssueResolution: (orderIssueId: String) -> Unit,
    onNavigateBack: () -> Unit
) {
    // Mock data for order details using the new data models
    val orderDetails = remember {
        Order(
            id = orderId ?: "defaultOrder",
            userId = "user123",
            orderNumber = "CB-1002",
            status = "Action Required",
            totalAmount = 200.50,
            currency = "$",
            shippingAddress = mapOf(
                "addressLine1" to "456 Oak Ave",
                "city" to "Somewhere",
                "stateOrProvince" to "NY",
                "postalCode" to "10001",
                "country" to "USA"
            ),
            createdAt = System.currentTimeMillis() - 86400000 * 2
        )
    }

    val orderItems = remember {
        listOf(
            OrderItem(
                id = "orderItemA",
                productVariantId = "pv_orderA",
                productSnapshot = mapOf("name" to "Product A", "sku" to "SKU00A"),
                quantity = 1,
                pricePerUnitPaid = 100.0,
                fulfillmentStatus = "Awaiting Resolution"
            ),
            OrderItem(
                id = "orderItemB",
                productVariantId = "pv_orderB",
                productSnapshot = mapOf("name" to "Product B", "sku" to "SKU00B"),
                quantity = 2,
                pricePerUnitPaid = 50.25,
                fulfillmentStatus = "Purchased"
            )
        )
    }

    val orderStatus = orderDetails.status
    val statusType = when (orderStatus) {
        "Action Required" -> StatusType.Warning
        "Delivered" -> StatusType.Success
        "Cancelled" -> StatusType.Error
        else -> StatusType.Default
    }

    LazyColumn(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item { Text(text = "Order Details for Order: ${orderDetails.orderNumber}", style = MaterialTheme.typography.headlineMedium, modifier = Modifier.padding(bottom = 16.dp)) }

        item {
            Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                Text(text = "Status:", style = MaterialTheme.typography.titleMedium, modifier = Modifier.weight(1f))
                StatusBadge(statusText = orderStatus, type = statusType)
            }
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            Text(text = "Items:", style = MaterialTheme.typography.titleMedium, modifier = Modifier.fillMaxWidth().padding(vertical = 8.dp))
        }
        items(orderItems) { item ->
            OrderItemDisplay(orderItem = item)
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            Spacer(modifier = Modifier.height(16.dp))
            Text(text = "Shipping Address:", style = MaterialTheme.typography.titleMedium, modifier = Modifier.fillMaxWidth().padding(bottom = 8.dp))
            val shippingAddress = orderDetails.shippingAddress
            Text(text = "${shippingAddress["addressLine1"]}, ${shippingAddress["city"]}", modifier = Modifier.fillMaxWidth())
            Text(text = "${shippingAddress["stateOrProvince"]}, ${shippingAddress["postalCode"]}, ${shippingAddress["country"]}", modifier = Modifier.fillMaxWidth())
            Spacer(modifier = Modifier.height(16.dp))

            Text(text = "Payment Summary: Placeholder", style = MaterialTheme.typography.titleMedium, modifier = Modifier.fillMaxWidth().padding(bottom = 8.dp))
            OrderSummary(
                subtotal = orderDetails.totalAmount / 1.1, // Assuming 10% tax for calculation
                taxes = orderDetails.totalAmount - (orderDetails.totalAmount / 1.1),
                total = orderDetails.totalAmount,
                currency = orderDetails.currency
            )
            Spacer(modifier = Modifier.height(16.dp))

            if (orderStatus == "Action Required") {
                Button(onClick = { onNavigateToOrderIssueResolution("ISSUE_FOR_${orderId}") }, modifier = Modifier.fillMaxWidth()) {
                    Text("Resolve Issue")
                }
                Spacer(modifier = Modifier.height(8.dp))
            }
            Button(onClick = onNavigateBack, modifier = Modifier.fillMaxWidth()) {
                Text("Back to My Orders")
            }
        }
    }
}
