package com.tfkcolin.maomao.ui.components.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.Verified
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.Order
import com.tfkcolin.maomao.data.models.User
import com.tfkcolin.maomao.ui.components.StatusBadge
import com.tfkcolin.maomao.ui.components.StatusType
import com.tfkcolin.maomao.ui.components.cards.MaomaoCard
import com.tfkcolin.maomao.ui.components.images.AvatarImage
import com.tfkcolin.maomao.ui.theme.BorderRadius
import com.tfkcolin.maomao.ui.theme.PriceRed
import com.tfkcolin.maomao.ui.theme.RatingGold
import com.tfkcolin.maomao.ui.theme.Size
import com.tfkcolin.maomao.ui.theme.Spacing
import com.tfkcolin.maomao.ui.theme.SuccessGreen
import java.util.Date

/**
 * User profile header with avatar, name, and basic info
 */
@Composable
fun ProfileHeader(
    user: User,
    onEditProfile: () -> Unit,
    modifier: Modifier = Modifier,
    showEditButton: Boolean = true
) {
    MaomaoCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            // Avatar and edit button
            Box {
                AvatarImage(
                    imageUrl = user.photoURL,
                    contentDescription = "Profile picture",
                    size = Size.avatarExtraLarge
                )
                
                if (showEditButton) {
                    IconButton(
                        onClick = onEditProfile,
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .size(32.dp)
                            .background(
                                MaterialTheme.colorScheme.primary,
                                CircleShape
                            )
                    ) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "Edit profile",
                            tint = MaterialTheme.colorScheme.onPrimary,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
            
            // User info
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(Spacing.small)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(Spacing.small)
                ) {
                    Text(
                        text = user.displayName ?: "User",
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        color = MaterialTheme.colorScheme.onSurface
                    )
                }
                
                user.email?.let { email ->
                    Text(
                        text = email,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                // Member since
                user.createdAt?.let { createdAt ->
                    Text(
                        text = "Member since ${Date(createdAt).year}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

/**
 * Profile stats section showing orders, reviews, etc.
 */
@Composable
fun ProfileStats(
    totalOrders: Int,
    totalReviews: Int,
    averageRating: Float,
    totalSavings: Double,
    currency: String,
    modifier: Modifier = Modifier
) {
    MaomaoCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium)
        ) {
            Text(
                text = "Your Stats",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(bottom = Spacing.medium)
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                StatItem(
                    value = totalOrders.toString(),
                    label = "Orders",
                    modifier = Modifier.weight(1f)
                )
                
                StatItem(
                    value = totalReviews.toString(),
                    label = "Reviews",
                    modifier = Modifier.weight(1f)
                )
                
                StatItem(
                    value = if (averageRating > 0) String.format("%.1f", averageRating) else "N/A",
                    label = "Rating",
                    icon = if (averageRating > 0) Icons.Default.Star else null,
                    iconTint = RatingGold,
                    modifier = Modifier.weight(1f)
                )
                
                StatItem(
                    value = "$currency${String.format("%.0f", totalSavings)}",
                    label = "Saved",
                    valueColor = SuccessGreen,
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

/**
 * Individual stat item
 */
@Composable
private fun StatItem(
    value: String,
    label: String,
    modifier: Modifier = Modifier,
    valueColor: Color = MaterialTheme.colorScheme.onSurface,
    icon: ImageVector? = null,
    iconTint: Color = MaterialTheme.colorScheme.onSurface
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
        ) {
            Text(
                text = value,
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = valueColor
            )
            
            icon?.let { iconVector ->
                Icon(
                    imageVector = iconVector,
                    contentDescription = null,
                    tint = iconTint,
                    modifier = Modifier.size(Size.iconSmall)
                )
            }
        }
        
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * Profile menu item component
 */
@Composable
fun ProfileMenuItem(
    title: String,
    subtitle: String? = null,
    icon: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    showChevron: Boolean = true,
    badge: String? = null,
    trailingContent: (@Composable () -> Unit)? = null
) {
    MaomaoCard(
        modifier = modifier.fillMaxWidth(),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            // Icon
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(RoundedCornerShape(BorderRadius.medium))
                    .background(MaterialTheme.colorScheme.primaryContainer),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onPrimaryContainer,
                    modifier = Modifier.size(Size.iconMedium)
                )
            }
            
            // Title and subtitle
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(Spacing.small)
                ) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                    
                    badge?.let { badgeText ->
                        StatusBadge(
                            statusText = badgeText,
                            type = StatusType.Info
                        )
                    }
                }
                
                subtitle?.let { subtitleText ->
                    Text(
                        text = subtitleText,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
            
            // Trailing content or chevron
            if (trailingContent != null) {
                trailingContent()
            } else if (showChevron) {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "Navigate",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.size(Size.iconMedium)
                )
            }
        }
    }
}

/**
 * Settings toggle item
 */
@Composable
fun SettingsToggleItem(
    title: String,
    subtitle: String? = null,
    icon: ImageVector,
    isChecked: Boolean,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    ProfileMenuItem(
        title = title,
        subtitle = subtitle,
        icon = icon,
        onClick = { if (enabled) onToggle(!isChecked) },
        modifier = modifier,
        showChevron = false,
        trailingContent = {
            Switch(
                checked = isChecked,
                onCheckedChange = onToggle,
                enabled = enabled
            )
        }
    )
}

/**
 * Order history item component
 */
@Composable
fun OrderHistoryItem(
    order: Order,
    onOrderClick: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    MaomaoCard(
        modifier = modifier.fillMaxWidth(),
        onClick = { onOrderClick(order.id) }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.medium),
            verticalArrangement = Arrangement.spacedBy(Spacing.small)
        ) {
            // Order header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Order #${order.id.take(8)}",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                StatusBadge(
                    statusText = order.status,
                    type = when (order.status.lowercase()) {
                        "delivered" -> StatusType.Success
                        "cancelled" -> StatusType.Error
                        "processing" -> StatusType.Info
                        else -> StatusType.Default
                    }
                )
            }
        }
    }
}
