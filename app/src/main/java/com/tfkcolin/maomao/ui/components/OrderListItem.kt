package com.tfkcolin.maomao.ui.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Card
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.Order
import com.tfkcolin.maomao.ui.components.StatusBadge
import com.tfkcolin.maomao.ui.components.StatusType
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * A reusable composable that displays a summary of an individual order in a list.
 * It is used in the "My Orders List" screen.
 *
 * @param order The order data to display.
 * @param onOrderClick A lambda function invoked when the order item is clicked,
 *   typically navigating to the Order Details screen.
 *
 * Usage:
 * ```
 * OrderListItem(
 *     order = Order(
 *         id = "order1",
 *         orderNumber = "CB-1001",
 *         createdAt = System.currentTimeMillis(),
 *         totalAmount = 150.00,
 *         status = "Action Required"
 *     ),
 *     onOrderClick = { orderId -> /* navigate to order details */ }
 * )
 * ```
 */
@Composable
fun OrderListItem(
    order: Order,
    onOrderClick: (orderId: String) -> Unit
) {
    val dateFormatter = remember { SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()) }
    val formattedDate = remember(order.createdAt) { dateFormatter.format(Date(order.createdAt)) }

    val statusType = when (order.status) {
        "Action Required" -> StatusType.Warning
        "Delivered" -> StatusType.Success
        "Cancelled" -> StatusType.Error
        else -> StatusType.Default
    }

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
            .clickable { onOrderClick(order.id) }
    ) {
        Column(modifier = Modifier.padding(16.dp)) {
            Row(modifier = Modifier.fillMaxWidth()) {
                Text(text = "Order #${order.orderNumber}", modifier = Modifier.weight(1f))
                Text(text = formattedDate)
            }
            Text(text = "Total: ${order.currency}${String.format("%.2f", order.totalAmount)}")
            Row(modifier = Modifier.fillMaxWidth()) {
                Text(text = "Status:", modifier = Modifier.weight(1f))
                StatusBadge(statusText = order.status, type = statusType)
            }
        }
    }
}
