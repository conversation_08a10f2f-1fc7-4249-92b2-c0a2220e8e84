package com.tfkcolin.maomao.ui.screens.checkout

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.ui.Alignment
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.maomao.data.models.OrderItem
import com.tfkcolin.maomao.data.models.ShippingAddress
import com.tfkcolin.maomao.ui.components.OrderItemDisplay
import com.tfkcolin.maomao.ui.components.OrderSummary

@Composable
fun CheckoutReviewScreen(onPlaceOrder: (orderNumber: String) -> Unit) {
    // Mock data for order items using the new data models
    val orderItems = remember {
        listOf(
            OrderItem(
                id = "reviewItem1",
                productVariantId = "pv_review1",
                productSnapshot = mapOf("name" to "Review Product X", "sku" to "SKU00X"),
                quantity = 1,
                pricePerUnitPaid = 15.0,
                fulfillmentStatus = "Pending Purchase"
            ),
            OrderItem(
                id = "reviewItem2",
                productVariantId = "pv_review2",
                productSnapshot = mapOf("name" to "Review Product Y", "sku" to "SKU00Y"),
                quantity = 2,
                pricePerUnitPaid = 25.0,
                fulfillmentStatus = "Pending Purchase"
            )
        )
    }

    // Mock data for shipping address using the new data model
    val shippingAddress = remember {
        ShippingAddress(
            id = "addr_review",
            addressLine1 = "123 Main St",
            city = "Anytown",
            stateOrProvince = "CA",
            postalCode = "90210",
            country = "USA"
        )
    }

    // Calculate summary from mock data
    val subtotal = orderItems.sumOf { it.quantity * it.pricePerUnitPaid }
    val taxes = subtotal * 0.10
    val total = subtotal + taxes

    LazyColumn(
        modifier = Modifier.fillMaxSize().padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        item { Text(text = "Checkout: Review", style = MaterialTheme.typography.headlineMedium, modifier = Modifier.padding(bottom = 16.dp)) }

        item { Text(text = "Items:", style = MaterialTheme.typography.titleMedium, modifier = Modifier.fillMaxWidth().padding(bottom = 8.dp)) }
        items(orderItems) { item ->
            OrderItemDisplay(orderItem = item)
            Spacer(modifier = Modifier.height(8.dp))
        }

        item {
            Spacer(modifier = Modifier.height(16.dp))
            Text(text = "Shipping Address:", style = MaterialTheme.typography.titleMedium, modifier = Modifier.fillMaxWidth().padding(bottom = 8.dp))
            Text(text = "${shippingAddress.addressLine1}, ${shippingAddress.city}", modifier = Modifier.fillMaxWidth())
            Text(text = "${shippingAddress.stateOrProvince}, ${shippingAddress.postalCode}, ${shippingAddress.country}", modifier = Modifier.fillMaxWidth())
            Spacer(modifier = Modifier.height(16.dp))

            Text(text = "Payment Method: Placeholder (e.g., Visa **** 1234)", style = MaterialTheme.typography.titleMedium, modifier = Modifier.fillMaxWidth().padding(bottom = 8.dp))
            Spacer(modifier = Modifier.height(16.dp))

            OrderSummary(
                subtotal = subtotal,
                taxes = taxes,
                total = total,
                currency = "$"
            )
            Spacer(modifier = Modifier.height(16.dp))
            Button(onClick = { onPlaceOrder("ORDER123") }, modifier = Modifier.fillMaxWidth()) {
                Text("Place Order")
            }
        }
    }
}
