package com.tfkcolin.maomao

import android.app.Application
import com.google.firebase.Firebase
import com.google.firebase.FirebaseApp
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import com.google.firebase.firestore.PersistentCacheSettings
import com.google.firebase.firestore.firestore
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

@HiltAndroidApp
class MaomaoApplication: Application() {
    @Inject
    override fun onCreate() {
        super.onCreate()
        FirebaseApp.initializeApp(applicationContext)
        if (BuildConfig.DEBUG){
            // 1. Auth Emulator
//            FirebaseAuth.getInstance().useEmulator("10.0.2.2", 9099)
            // 2. Firestore Emulator
            Firebase.firestore.useEmulator("10.0.2.2", 8080)
            // 3. Storage Emulator
//            FirebaseStorage.getInstance().useEmulator("10.0.2.2", 9199)
        }
        val settings = FirebaseFirestoreSettings.Builder()
            .setLocalCacheSettings(PersistentCacheSettings.newBuilder().setSizeBytes(1024 * 1024 * 1024).build())
            .build()
        FirebaseFirestore.getInstance().firestoreSettings = settings
    }
}