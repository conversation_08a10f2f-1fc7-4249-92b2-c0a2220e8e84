package com.tfkcolin.maomao.data.repository

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.maomao.data.models.GroupBuy
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GroupBuyRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {

    companion object {
        private const val GROUP_BUYS_COLLECTION = "groupBuys"
    }

    /**
     * Get all active group buys
     */
    fun getActiveGroupBuys(): Flow<Result<List<GroupBuy>>> = flow {
        try {
            val snapshot = firestore.collection(GROUP_BUYS_COLLECTION)
                .whereEqualTo("status", "Active")
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val groupBuys = snapshot.documents.mapNotNull { doc ->
                doc.toObject(GroupBuy::class.java)?.copy(id = doc.id)
            }
            emit(Result.success(groupBuys))
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }

    /**
     * Get group buy by ID
     */
    suspend fun getGroupBuyById(groupBuyId: String): Result<GroupBuy?> {
        return try {
            val snapshot = firestore.collection(GROUP_BUYS_COLLECTION)
                .document(groupBuyId)
                .get()
                .await()

            val groupBuy = snapshot.toObject(GroupBuy::class.java)?.copy(id = snapshot.id)
            Result.success(groupBuy)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get group buys for a specific product variant
     */
    suspend fun getGroupBuysForProductVariant(productVariantId: String): Result<List<GroupBuy>> {
        return try {
            val snapshot = firestore.collection(GROUP_BUYS_COLLECTION)
                .whereEqualTo("productVariantId", productVariantId)
                .whereEqualTo("status", "Active")
                .get()
                .await()

            val groupBuys = snapshot.documents.mapNotNull { doc ->
                doc.toObject(GroupBuy::class.java)?.copy(id = doc.id)
            }
            Result.success(groupBuys)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get trending group buys (those close to target or recently successful)
     */
    fun getTrendingGroupBuys(limit: Int = 10): Flow<Result<List<GroupBuy>>> = flow {
        try {
            val snapshot = firestore.collection(GROUP_BUYS_COLLECTION)
                .whereIn("status", listOf("Active", "Successful"))
                .orderBy("currentQuantity", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()

            val groupBuys = snapshot.documents.mapNotNull { doc ->
                doc.toObject(GroupBuy::class.java)?.copy(id = doc.id)
            }
            emit(Result.success(groupBuys))
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }

    /**
     * Join a group buy (increment current quantity)
     */
    suspend fun joinGroupBuy(groupBuyId: String, quantity: Int = 1): Result<Unit> {
        return try {
            val groupBuyRef = firestore.collection(GROUP_BUYS_COLLECTION).document(groupBuyId)
            
            firestore.runTransaction { transaction ->
                val snapshot = transaction.get(groupBuyRef)
                val groupBuy = snapshot.toObject(GroupBuy::class.java)
                
                if (groupBuy == null) {
                    throw Exception("Group buy not found")
                }
                
                if (groupBuy.status != "Active") {
                    throw Exception("Group buy is not active")
                }
                
                val newCurrentQuantity = groupBuy.currentQuantity + quantity
                
                // Update current quantity
                transaction.update(groupBuyRef, "currentQuantity", newCurrentQuantity)
                
                // Check if target is reached and update status if needed
                if (newCurrentQuantity >= groupBuy.targetQuantity) {
                    transaction.update(groupBuyRef, "status", "Successful")
                }
                
                null
            }.await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Leave a group buy (decrement current quantity)
     */
    suspend fun leaveGroupBuy(groupBuyId: String, quantity: Int = 1): Result<Unit> {
        return try {
            val groupBuyRef = firestore.collection(GROUP_BUYS_COLLECTION).document(groupBuyId)
            
            firestore.runTransaction { transaction ->
                val snapshot = transaction.get(groupBuyRef)
                val groupBuy = snapshot.toObject(GroupBuy::class.java)
                
                if (groupBuy == null) {
                    throw Exception("Group buy not found")
                }
                
                val newCurrentQuantity = maxOf(0, groupBuy.currentQuantity - quantity)
                
                // Update current quantity
                transaction.update(groupBuyRef, "currentQuantity", newCurrentQuantity)
                
                // If it was successful but now below target, revert to active
                if (groupBuy.status == "Successful" && newCurrentQuantity < groupBuy.targetQuantity) {
                    transaction.update(groupBuyRef, "status", "Active")
                }
                
                null
            }.await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Check if group buy is expired
     */
    suspend fun checkAndUpdateExpiredGroupBuys(): Result<Int> {
        return try {
            val currentTime = System.currentTimeMillis()
            val snapshot = firestore.collection(GROUP_BUYS_COLLECTION)
                .whereEqualTo("status", "Active")
                .whereLessThan("expiresAt", currentTime)
                .get()
                .await()

            val batch = firestore.batch()
            var updatedCount = 0

            snapshot.documents.forEach { doc ->
                val groupBuy = doc.toObject(GroupBuy::class.java)
                if (groupBuy != null) {
                    val newStatus = if (groupBuy.currentQuantity >= groupBuy.targetQuantity) {
                        "Successful"
                    } else {
                        "Failed"
                    }
                    batch.update(doc.reference, "status", newStatus)
                    updatedCount++
                }
            }

            if (updatedCount > 0) {
                batch.commit().await()
            }

            Result.success(updatedCount)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get group buy progress percentage
     */
    fun getGroupBuyProgress(groupBuy: GroupBuy): Float {
        return if (groupBuy.targetQuantity > 0) {
            (groupBuy.currentQuantity.toFloat() / groupBuy.targetQuantity.toFloat()).coerceAtMost(1.0f)
        } else {
            0.0f
        }
    }

    /**
     * Check if group buy is close to expiring (within 24 hours)
     */
    fun isGroupBuyExpiringSoon(groupBuy: GroupBuy): Boolean {
        val currentTime = System.currentTimeMillis()
        val twentyFourHours = 24 * 60 * 60 * 1000L
        return groupBuy.expiresAt - currentTime <= twentyFourHours
    }

    /**
     * Get time remaining for group buy in milliseconds
     */
    fun getTimeRemaining(groupBuy: GroupBuy): Long {
        val currentTime = System.currentTimeMillis()
        return maxOf(0L, groupBuy.expiresAt - currentTime)
    }

    /**
     * Listen to real-time group buy updates
     */
    fun listenToGroupBuyUpdates(groupBuyId: String): Flow<Result<GroupBuy?>> = flow {
        try {
            firestore.collection(GROUP_BUYS_COLLECTION)
                .document(groupBuyId)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        // Handle error - in a real implementation, you'd use a callback mechanism
                        return@addSnapshotListener
                    }
                    
                    if (snapshot != null && snapshot.exists()) {
                        val groupBuy = snapshot.toObject(GroupBuy::class.java)?.copy(id = snapshot.id)
                        // In a real implementation, you'd emit this through a proper flow mechanism
                    }
                }
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }

    /**
     * Get successful group buys for analytics
     */
    fun getSuccessfulGroupBuys(): Flow<Result<List<GroupBuy>>> = flow {
        try {
            val snapshot = firestore.collection(GROUP_BUYS_COLLECTION)
                .whereEqualTo("status", "Successful")
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val groupBuys = snapshot.documents.mapNotNull { doc ->
                doc.toObject(GroupBuy::class.java)?.copy(id = doc.id)
            }
            emit(Result.success(groupBuys))
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }
}
