package com.tfkcolin.maomao.data.models

/**
 * Represents a fulfillment exception or problem in the `orderIssues` Firestore collection.
 * This serves as a dedicated log for every fulfillment issue, creating a clear audit trail.
 *
 * @property id The auto-generated document ID for this order issue.
 * @property orderId The ID of the parent order, referencing the `orders` collection.
 * @property orderItemId The ID of the specific order item this issue is related to, referencing the `orderItems` subcollection.
 * @property userId The ID of the user associated with this order, referencing the `users` collection.
 * @property issueType The type of issue (e.g., 'OutOfStock', 'PriceIncrease', 'SupplierQualityIssue', 'ShippingDelay').
 * @property status The current status of the issue. Enum: 'PendingCustomerResponse', 'CustomerResponded', 'Resolved-PriceAccepted', 'Resolved-AlternativeAccepted', 'Resolved-Cancelled', 'AwaitingAdminAction'.
 * @property details The detailed message from the admin to the customer explaining the issue and options.
 * @property resolution A summary of how the issue was resolved. Can be null if unresolved.
 * @property priceDifference The monetary difference if the issue is a price increase. Can be null if not applicable.
 * @property createdAt The timestamp when the issue was created (milliseconds since epoch).
 * @property resolvedAt The timestamp when the issue was resolved (milliseconds since epoch). Can be null if unresolved.
 */
data class OrderIssue(
    val id: String = "",
    val orderId: String = "",
    val orderItemId: String = "",
    val userId: String = "",
    val issueType: String = "", // Enum: 'OutOfStock', 'PriceIncrease', 'SupplierQualityIssue', 'ShippingDelay'
    val status: String = "PendingCustomerResponse", // Enum: 'PendingCustomerResponse', 'CustomerResponded', 'Resolved-PriceAccepted', 'Resolved-AlternativeAccepted', 'Resolved-Cancelled', 'AwaitingAdminAction'
    val details: String = "",
    val resolution: String? = null,
    val priceDifference: Double? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val resolvedAt: Long? = null
)
