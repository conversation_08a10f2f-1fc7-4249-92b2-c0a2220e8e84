package com.tfkcolin.maomao.data.models

/**
 * Represents a user's saved shipping address, typically found in a subcollection under `users`.
 *
 * @property id The auto-generated document ID for this shipping address.
 * @property addressLine1 The first line of the street address.
 * @property addressLine2 The second line of the street address (e.g., apartment, suite number). Can be null.
 * @property city The city of the address.
 * @property stateOrProvince The state or province of the address.
 * @property postalCode The postal code or ZIP code of the address.
 * @property country The country of the address.
 * @property isDefault A boolean indicating if this is the user's default shipping address.
 */
data class ShippingAddress(
    val id: String = "",
    val addressLine1: String = "",
    val addressLine2: String? = null,
    val city: String = "",
    val stateOrProvince: String = "",
    val postalCode: String = "",
    val country: String = "",
    val isDefault: Boolean = false
)
