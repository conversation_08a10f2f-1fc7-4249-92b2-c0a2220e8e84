package com.tfkcolin.maomao.data.repository

import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.maomao.data.models.Product
import com.tfkcolin.maomao.data.models.ProductVariant
import com.tfkcolin.maomao.data.models.SourceLink
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ProductRepository @Inject constructor(
    private val firestore: FirebaseFirestore
) {

    companion object {
        private const val PRODUCTS_COLLECTION = "products"
        private const val PRODUCT_VARIANTS_COLLECTION = "productVariants"
        private const val SOURCE_LINKS_COLLECTION = "sourceLinks"
    }

    /**
     * Get all active products
     */
    fun getActiveProducts(): Flow<Result<List<Product>>> = flow {
        try {
            val snapshot = firestore.collection(PRODUCTS_COLLECTION)
                .whereEqualTo("status", "Active")
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val products = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)?.copy(id = doc.id)
            }
            emit(Result.success(products))
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }

    /**
     * Get products by category
     */
    fun getProductsByCategory(category: String): Flow<Result<List<Product>>> = flow {
        try {
            val snapshot = firestore.collection(PRODUCTS_COLLECTION)
                .whereEqualTo("status", "Active")
                .whereEqualTo("category", category)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val products = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)?.copy(id = doc.id)
            }
            emit(Result.success(products))
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }

    /**
     * Get product by ID
     */
    suspend fun getProductById(productId: String): Result<Product?> {
        return try {
            val snapshot = firestore.collection(PRODUCTS_COLLECTION)
                .document(productId)
                .get()
                .await()

            val product = snapshot.toObject(Product::class.java)?.copy(id = snapshot.id)
            Result.success(product)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Search products by name or description
     */
    fun searchProducts(query: String): Flow<Result<List<Product>>> = flow {
        try {
            // Note: Firestore doesn't support full-text search natively
            // This is a basic implementation that searches by name prefix
            // For production, consider using Algolia or similar service
            val snapshot = firestore.collection(PRODUCTS_COLLECTION)
                .whereEqualTo("status", "Active")
                .orderBy("name")
                .startAt(query)
                .endAt(query + "\uf8ff")
                .get()
                .await()

            val products = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)?.copy(id = doc.id)
            }
            emit(Result.success(products))
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }

    /**
     * Get product variants for a specific product
     */
    suspend fun getProductVariants(productId: String): Result<List<ProductVariant>> {
        return try {
            val snapshot = firestore.collection(PRODUCT_VARIANTS_COLLECTION)
                .whereEqualTo("productId", productId)
                .whereEqualTo("status", "Active")
                .get()
                .await()

            val variants = snapshot.documents.mapNotNull { doc ->
                doc.toObject(ProductVariant::class.java)?.copy(id = doc.id)
            }
            Result.success(variants)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get product variant by ID
     */
    suspend fun getProductVariantById(variantId: String): Result<ProductVariant?> {
        return try {
            val snapshot = firestore.collection(PRODUCT_VARIANTS_COLLECTION)
                .document(variantId)
                .get()
                .await()

            val variant = snapshot.toObject(ProductVariant::class.java)?.copy(id = snapshot.id)
            Result.success(variant)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get source links for a product variant
     */
    suspend fun getSourceLinks(productVariantId: String): Result<List<SourceLink>> {
        return try {
            val snapshot = firestore.collection(SOURCE_LINKS_COLLECTION)
                .whereEqualTo("productVariantId", productVariantId)
                .orderBy("priority")
                .get()
                .await()

            val sourceLinks = snapshot.documents.mapNotNull { doc ->
                doc.toObject(SourceLink::class.java)?.copy(id = doc.id)
            }
            Result.success(sourceLinks)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get featured/curated products for home screen
     */
    fun getFeaturedProducts(limit: Int = 10): Flow<Result<List<Product>>> = flow {
        try {
            val snapshot = firestore.collection(PRODUCTS_COLLECTION)
                .whereEqualTo("status", "Active")
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()

            val products = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)?.copy(id = doc.id)
            }
            emit(Result.success(products))
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }

    /**
     * Get all available categories
     */
    suspend fun getCategories(): Result<List<String>> {
        return try {
            val snapshot = firestore.collection(PRODUCTS_COLLECTION)
                .whereEqualTo("status", "Active")
                .get()
                .await()

            val categories = snapshot.documents
                .mapNotNull { it.getString("category") }
                .distinct()
                .sorted()

            Result.success(categories)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
