package com.tfkcolin.maomao.data.repository

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.tfkcolin.maomao.data.models.Order
import com.tfkcolin.maomao.data.models.OrderItem
import com.tfkcolin.maomao.data.models.OrderIssue
import com.tfkcolin.maomao.data.models.ShippingAddress
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class OrderRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val firebaseAuth: FirebaseAuth
) {

    companion object {
        private const val ORDERS_COLLECTION = "orders"
        private const val ORDER_ITEMS_SUBCOLLECTION = "orderItems"
        private const val ORDER_ISSUES_COLLECTION = "orderIssues"
    }

    /**
     * Create a new order
     */
    suspend fun createOrder(
        orderItems: List<OrderItem>,
        totalAmount: Double,
        shippingAddress: ShippingAddress,
        currency: String = "USD"
    ): Result<String> {
        return try {
            val currentUser = firebaseAuth.currentUser
                ?: return Result.failure(Exception("User not authenticated"))

            val orderNumber = generateOrderNumber()
            
            val order = Order(
                userId = currentUser.uid,
                orderNumber = orderNumber,
                status = "Pending Payment",
                totalAmount = totalAmount,
                currency = currency,
                shippingAddress = mapOf(
                    "addressLine1" to shippingAddress.addressLine1,
                    "addressLine2" to (shippingAddress.addressLine2 ?: ""),
                    "city" to shippingAddress.city,
                    "stateOrProvince" to shippingAddress.stateOrProvince,
                    "postalCode" to shippingAddress.postalCode,
                    "country" to shippingAddress.country
                ),
                createdAt = System.currentTimeMillis()
            )

            // Create order document
            val orderRef = firestore.collection(ORDERS_COLLECTION).document()
            orderRef.set(order.copy(id = orderRef.id)).await()

            // Add order items as subcollection
            val batch = firestore.batch()
            orderItems.forEach { item ->
                val itemRef = orderRef.collection(ORDER_ITEMS_SUBCOLLECTION).document()
                batch.set(itemRef, item.copy(id = itemRef.id))
            }
            batch.commit().await()

            Result.success(orderRef.id)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get user's orders
     */
    fun getUserOrders(): Flow<Result<List<Order>>> = flow {
        try {
            val currentUser = firebaseAuth.currentUser
            if (currentUser == null) {
                emit(Result.failure(Exception("User not authenticated")))
                return@flow
            }

            val snapshot = firestore.collection(ORDERS_COLLECTION)
                .whereEqualTo("userId", currentUser.uid)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val orders = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Order::class.java)?.copy(id = doc.id)
            }
            emit(Result.success(orders))
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }

    /**
     * Get order by ID
     */
    suspend fun getOrderById(orderId: String): Result<Order?> {
        return try {
            val snapshot = firestore.collection(ORDERS_COLLECTION)
                .document(orderId)
                .get()
                .await()

            val order = snapshot.toObject(Order::class.java)?.copy(id = snapshot.id)
            Result.success(order)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get order items for a specific order
     */
    suspend fun getOrderItems(orderId: String): Result<List<OrderItem>> {
        return try {
            val snapshot = firestore.collection(ORDERS_COLLECTION)
                .document(orderId)
                .collection(ORDER_ITEMS_SUBCOLLECTION)
                .get()
                .await()

            val orderItems = snapshot.documents.mapNotNull { doc ->
                doc.toObject(OrderItem::class.java)?.copy(id = doc.id)
            }
            Result.success(orderItems)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get order issues for a specific order
     */
    suspend fun getOrderIssues(orderId: String): Result<List<OrderIssue>> {
        return try {
            val snapshot = firestore.collection(ORDER_ISSUES_COLLECTION)
                .whereEqualTo("orderId", orderId)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val issues = snapshot.documents.mapNotNull { doc ->
                doc.toObject(OrderIssue::class.java)?.copy(id = doc.id)
            }
            Result.success(issues)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get order issue by ID
     */
    suspend fun getOrderIssueById(issueId: String): Result<OrderIssue?> {
        return try {
            val snapshot = firestore.collection(ORDER_ISSUES_COLLECTION)
                .document(issueId)
                .get()
                .await()

            val issue = snapshot.toObject(OrderIssue::class.java)?.copy(id = snapshot.id)
            Result.success(issue)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update order status
     */
    suspend fun updateOrderStatus(orderId: String, newStatus: String): Result<Unit> {
        return try {
            firestore.collection(ORDERS_COLLECTION)
                .document(orderId)
                .update("status", newStatus)
                .await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Respond to order issue
     */
    suspend fun respondToOrderIssue(
        issueId: String,
        response: String,
        newStatus: String
    ): Result<Unit> {
        return try {
            val updates = mapOf(
                "status" to newStatus,
                "customerResponse" to response,
                "respondedAt" to System.currentTimeMillis()
            )

            firestore.collection(ORDER_ISSUES_COLLECTION)
                .document(issueId)
                .update(updates)
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get orders with "Action Required" status
     */
    fun getActionRequiredOrders(): Flow<Result<List<Order>>> = flow {
        try {
            val currentUser = firebaseAuth.currentUser
            if (currentUser == null) {
                emit(Result.failure(Exception("User not authenticated")))
                return@flow
            }

            val snapshot = firestore.collection(ORDERS_COLLECTION)
                .whereEqualTo("userId", currentUser.uid)
                .whereEqualTo("status", "Action Required")
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()

            val orders = snapshot.documents.mapNotNull { doc ->
                doc.toObject(Order::class.java)?.copy(id = doc.id)
            }
            emit(Result.success(orders))
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }

    /**
     * Generate unique order number
     */
    private fun generateOrderNumber(): String {
        val timestamp = System.currentTimeMillis()
        val random = (1000..9999).random()
        return "CB-$timestamp-$random"
    }

    /**
     * Listen to real-time order updates
     */
    fun listenToOrderUpdates(orderId: String): Flow<Result<Order?>> = flow {
        try {
            firestore.collection(ORDERS_COLLECTION)
                .document(orderId)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        // Handle error - in a real implementation, you'd use a callback mechanism
                        return@addSnapshotListener
                    }
                    
                    if (snapshot != null && snapshot.exists()) {
                        val order = snapshot.toObject(Order::class.java)?.copy(id = snapshot.id)
                        // In a real implementation, you'd emit this through a proper flow mechanism
                    }
                }
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }
}
