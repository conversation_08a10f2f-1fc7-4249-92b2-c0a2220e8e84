package com.tfkcolin.maomao.data.models

/**
 * Manages the state of active and completed group-buying campaigns in the `groupBuys` Firestore collection.
 *
 * @property id The auto-generated document ID for this group buy.
 * @property productVariantId The ID of the associated product variant in the `productVariants` collection.
 * @property targetQuantity The minimum number of participants required for the group buy to succeed.
 * @property currentQuantity The current number of participants who have joined the group buy.
 * @property groupPrice The discounted price per unit for participants in this group buy.
 * @property status The current status of the group buy. Enum: 'Active', 'Successful', 'Failed', 'Fulfilled'.
 * @property expiresAt The timestamp indicating the deadline for the group buy (milliseconds since epoch).
 * @property createdAt The timestamp when the group buy was created (milliseconds since epoch).
 */
data class GroupBuy(
    val id: String = "",
    val productVariantId: String = "",
    val targetQuantity: Int = 0,
    val currentQuantity: Int = 0,
    val groupPrice: Double = 0.0,
    val status: String = "Active", // Enum: 'Active', 'Successful', 'Failed', 'Fulfilled'
    val expiresAt: Long = 0L,
    val createdAt: Long = System.currentTimeMillis()
)
