package com.tfkcolin.maomao.data.repository

import com.tfkcolin.maomao.data.models.OrderItem
import com.tfkcolin.maomao.data.models.ProductVariant
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for managing shopping cart state
 * Uses in-memory storage for cart items until checkout
 */
@Singleton
class CartRepository @Inject constructor() {

    private val _cartItems = MutableStateFlow<List<CartItem>>(emptyList())
    val cartItems: Flow<List<CartItem>> = _cartItems.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: Flow<Boolean> = _isLoading.asStateFlow()

    /**
     * Data class representing a cart item
     */
    data class CartItem(
        val id: String = "",
        val productVariantId: String = "",
        val productName: String = "",
        val productSku: String = "",
        val productImageUrl: String = "",
        val quantity: Int = 1,
        val pricePerUnit: Double = 0.0,
        val isGroupBuy: Boolean = false,
        val groupBuyId: String? = null,
        val variantAttributes: Map<String, String> = emptyMap()
    )

    /**
     * Add item to cart
     */
    fun addToCart(
        productVariantId: String,
        productName: String,
        productSku: String,
        productImageUrl: String,
        quantity: Int,
        pricePerUnit: Double,
        isGroupBuy: Boolean = false,
        groupBuyId: String? = null,
        variantAttributes: Map<String, String> = emptyMap()
    ): Result<Unit> {
        return try {
            val currentItems = _cartItems.value.toMutableList()
            
            // Check if item already exists in cart
            val existingItemIndex = currentItems.indexOfFirst { 
                it.productVariantId == productVariantId && 
                it.isGroupBuy == isGroupBuy &&
                it.groupBuyId == groupBuyId
            }

            if (existingItemIndex != -1) {
                // Update quantity of existing item
                val existingItem = currentItems[existingItemIndex]
                currentItems[existingItemIndex] = existingItem.copy(
                    quantity = existingItem.quantity + quantity
                )
            } else {
                // Add new item
                val newItem = CartItem(
                    id = generateCartItemId(),
                    productVariantId = productVariantId,
                    productName = productName,
                    productSku = productSku,
                    productImageUrl = productImageUrl,
                    quantity = quantity,
                    pricePerUnit = pricePerUnit,
                    isGroupBuy = isGroupBuy,
                    groupBuyId = groupBuyId,
                    variantAttributes = variantAttributes
                )
                currentItems.add(newItem)
            }

            _cartItems.value = currentItems
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Remove item from cart
     */
    fun removeFromCart(cartItemId: String): Result<Unit> {
        return try {
            val currentItems = _cartItems.value.toMutableList()
            currentItems.removeAll { it.id == cartItemId }
            _cartItems.value = currentItems
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update item quantity
     */
    fun updateQuantity(cartItemId: String, newQuantity: Int): Result<Unit> {
        return try {
            if (newQuantity <= 0) {
                return removeFromCart(cartItemId)
            }

            val currentItems = _cartItems.value.toMutableList()
            val itemIndex = currentItems.indexOfFirst { it.id == cartItemId }
            
            if (itemIndex != -1) {
                currentItems[itemIndex] = currentItems[itemIndex].copy(quantity = newQuantity)
                _cartItems.value = currentItems
            }
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Clear all items from cart
     */
    fun clearCart(): Result<Unit> {
        return try {
            _cartItems.value = emptyList()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get cart item count
     */
    fun getCartItemCount(): Int {
        return _cartItems.value.sumOf { it.quantity }
    }

    /**
     * Get cart total amount
     */
    fun getCartTotal(): Double {
        return _cartItems.value.sumOf { it.quantity * it.pricePerUnit }
    }

    /**
     * Convert cart items to order items for checkout
     */
    fun convertToOrderItems(): List<OrderItem> {
        return _cartItems.value.map { cartItem ->
            OrderItem(
                id = "", // Will be generated during order creation
                productVariantId = cartItem.productVariantId,
                productSnapshot = mapOf(
                    "name" to cartItem.productName,
                    "sku" to cartItem.productSku,
                    "image" to cartItem.productImageUrl
                ) + cartItem.variantAttributes,
                quantity = cartItem.quantity,
                pricePerUnitPaid = cartItem.pricePerUnit,
                fulfillmentStatus = "Pending Purchase",
                isGroupBuy = cartItem.isGroupBuy
            )
        }
    }

    /**
     * Check if cart has group buy items
     */
    fun hasGroupBuyItems(): Boolean {
        return _cartItems.value.any { it.isGroupBuy }
    }

    /**
     * Get group buy items separately
     */
    fun getGroupBuyItems(): List<CartItem> {
        return _cartItems.value.filter { it.isGroupBuy }
    }

    /**
     * Get regular items separately
     */
    fun getRegularItems(): List<CartItem> {
        return _cartItems.value.filter { !it.isGroupBuy }
    }

    /**
     * Generate unique cart item ID
     */
    private fun generateCartItemId(): String {
        return "cart_${System.currentTimeMillis()}_${(1000..9999).random()}"
    }

    /**
     * Set loading state
     */
    fun setLoading(loading: Boolean) {
        _isLoading.value = loading
    }
}
