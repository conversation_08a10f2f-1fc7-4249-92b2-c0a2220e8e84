package com.tfkcolin.maomao.data.models

/**
 * Represents a link to an actual supplier listing for a product variant in the `sourceLinks` Firestore collection.
 * This is the heart of the sourcing operation, allowing a single product variant to have multiple prioritized suppliers.
 *
 * @property id The auto-generated document ID for this source link.
 * @property productVariantId The ID of the associated product variant in the `productVariants` collection.
 * @property sourcePlatform The platform where the supplier listing is found (e.g., 'Pinduoduo', 'Taobao', '1688.com', 'Other').
 * @property sourceUrl The direct URL to the supplier's product page.
 * @property sourcePrice The price paid to the supplier for this item.
 * @property sourceCurrency The currency of the `sourcePrice` (e.g., "CNY").
 * @property priority The order in which to check this supplier (1 = primary, 2 = backup, etc.).
 * @property notes Admin-only notes about this supplier link (e.g., "Supplier ships fast," "Check for holiday discounts"). Can be null.
 * @property lastCheckedAt The timestamp when an admin last manually verified this link and price (milliseconds since epoch). Can be null.
 */
data class SourceLink(
    val id: String = "",
    val productVariantId: String = "",
    val sourcePlatform: String = "", // Enum: 'Pinduoduo', 'Taobao', '1688.com', 'Other'
    val sourceUrl: String = "",
    val sourcePrice: Double = 0.0,
    val sourceCurrency: String = "CNY",
    val priority: Int = 1,
    val notes: String? = null,
    val lastCheckedAt: Long? = null
)
