package com.tfkcolin.maomao.data.models

/**
 * Represents an item within an order, typically stored in an `orderItems` subcollection under an `Order` document.
 *
 * @property id The auto-generated document ID for this order item.
 * @property productVariantId The ID of the associated product variant, referencing the `productVariants` collection.
 * @property productSnapshot A map containing a copy of key product details (e.g., `name`, `sku`, `image`) at the time of purchase.
 *   This protects against future edits to the original product data.
 * @property quantity The quantity of this product variant in the order.
 * @property pricePerUnitPaid The exact price the customer paid per unit for this item.
 * @property isGroupBuy A boolean indicating if this item was part of a group buy.
 * @property fulfillmentStatus The current fulfillment status of this specific item. Enum: 'Pending Purchase', 'Purchased in China', 'Awaiting Resolution', 'Ready for Int'l Shipping', 'Shipped'.
 * @property sourceLinkUsedId The ID of the `SourceLink` document that was used for fulfillment of this item. Can be null if not yet fulfilled.
 * @property orderIssueId The ID of an `OrderIssue` document if a problem arises with this specific order item. Can be null.
 */
data class OrderItem(
    val id: String = "",
    val productVariantId: String = "",
    val productSnapshot: Map<String, String> = emptyMap(), // Copy of key product details
    val quantity: Int = 0,
    val pricePerUnitPaid: Double = 0.0,
    val isGroupBuy: Boolean = false,
    val fulfillmentStatus: String = "Pending Purchase", // Enum: 'Pending Purchase', 'Purchased in China', 'Awaiting Resolution', 'Ready for Int'l Shipping', 'Shipped'
    val sourceLinkUsedId: String? = null,
    val orderIssueId: String? = null
)
