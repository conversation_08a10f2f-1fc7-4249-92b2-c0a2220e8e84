package com.tfkcolin.maomao.data.models

/**
 * Represents a specific, purchasable version of a product in the `productVariants` Firestore collection.
 *
 * @property id The auto-generated document ID for this product variant.
 * @property productId The ID of the parent product in the `products` collection.
 * @property sku Your unique, internal Stock Keeping Unit for this specific variant.
 * @property name A descriptive name for the variant (e.g., "Red / Medium").
 * @property attributes A map of variant properties (e.g., `{ "color": "Red", "size": "M" }`).
 * @property myPrice The price you charge the customer for this variant.
 * @property currency The currency for `myPrice` (e.g., "USD").
 * @property mainImageUrl The URL to an image specific to this variant. Can be null. If null, use `products.coverImageUrl`.
 * @property weight The weight of the item.
 * @property weightUnit The unit of weight (e.g., 'g', 'kg', 'lb').
 * @property virtualStock A non-binding stock number for display purposes (e.g., 999). Actual availability is checked at fulfillment.
 */
data class ProductVariant(
    val id: String = "",
    val productId: String = "",
    val sku: String = "",
    val name: String = "",
    val attributes: Map<String, String> = emptyMap(),
    val myPrice: Double = 0.0,
    val currency: String = "USD",
    val mainImageUrl: String? = null,
    val weight: Double = 0.0,
    val weightUnit: String = "g",
    val virtualStock: Int = 0
)
