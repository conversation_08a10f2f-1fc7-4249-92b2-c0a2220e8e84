package com.tfkcolin.maomao.data.models

/**
 * Represents a user profile in the `users` Firestore collection.
 *
 * @property uid The Firebase Authentication User ID. This serves as the document ID in Firestore.
 * @property email The user's email address.
 * @property displayName The user's display name.
 * @property photoURL The URL to the user's profile picture. Can be null.
 * @property createdAt The timestamp when the user profile was created (milliseconds since epoch).
 * @property isAdmin A boolean indicating if the user has administrator privileges. Used for access control.
 */
data class User(
    val uid: String = "",
    val email: String = "",
    val displayName: String? = null,
    val photoURL: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val isAdmin: Boolean = false
)
