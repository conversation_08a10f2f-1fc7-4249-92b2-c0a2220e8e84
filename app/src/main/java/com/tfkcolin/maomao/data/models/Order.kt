package com.tfkcolin.maomao.data.models

/**
 * Represents a customer order in the `orders` Firestore collection.
 *
 * @property id The auto-generated document ID for this order.
 * @property userId The ID of the user who placed this order, referencing the `users` collection.
 * @property orderNumber A human-readable order number (e.g., "CB-1001").
 * @property status The current status of the order. Enum: 'Pending Payment', 'Processing', 'Action Required', 'Partially Shipped', 'Shipped', 'Delivered', 'Cancelled'.
 * @property totalAmount The total amount paid by the customer for this order.
 * @property currency The currency of the `totalAmount`.
 * @property shippingAddress A map containing a copy of the selected shipping address details at the time of order.
 * @property createdAt The timestamp when the order was placed (milliseconds since epoch).
 */
data class Order(
    val id: String = "",
    val userId: String = "",
    val orderNumber: String = "",
    val status: String = "Pending Payment", // Enum: 'Pending Payment', 'Processing', 'Action Required', 'Partially Shipped', 'Shipped', 'Delivered', 'Cancelled'
    val totalAmount: Double = 0.0,
    val currency: String = "USD",
    val shippingAddress: Map<String, String> = emptyMap(), // Copy of ShippingAddress details
    val createdAt: Long = System.currentTimeMillis()
)
