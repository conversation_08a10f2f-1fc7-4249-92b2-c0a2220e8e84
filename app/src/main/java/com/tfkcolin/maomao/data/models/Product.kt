package com.tfkcolin.maomao.data.models

/**
 * Represents a curated product in the `products` Firestore collection.
 * This is the core information presented to the customer.
 *
 * @property id The auto-generated document ID for this product.
 * @property name The user-friendly name of the product (e.g., "Handmade Ceramic Matcha Bowl").
 * @property description The "Curator's Note" - a detailed, enticing description of the product.
 * @property coverImageUrl The URL to the primary image for the product, stored in Firebase Storage.
 * @property images A list of additional image URLs for the product. Can be empty.
 * @property category The product's category (e.g., "Home Goods", "Kitchenware").
 * @property status The current status of the product. Enum: 'Active', 'Archived', 'Discontinued'. Controls visibility.
 * @property createdAt The timestamp when the product was created (milliseconds since epoch).
 * @property updatedAt The timestamp when the product was last updated (milliseconds since epoch).
 */
data class Product(
    val id: String = "",
    val name: String = "",
    val description: String = "",
    val coverImageUrl: String = "",
    val images: List<String> = emptyList(),
    val category: String = "",
    val status: String = "Active", // Enum: 'Active', 'Archived', 'Discontinued'
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)
