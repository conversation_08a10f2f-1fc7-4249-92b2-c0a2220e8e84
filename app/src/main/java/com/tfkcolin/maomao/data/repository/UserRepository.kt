package com.tfkcolin.maomao.data.repository

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.tfkcolin.maomao.data.models.ShippingAddress
import com.tfkcolin.maomao.data.models.User
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val firebaseAuth: FirebaseAuth
) {

    companion object {
        private const val USERS_COLLECTION = "users"
        private const val SHIPPING_ADDRESSES_SUBCOLLECTION = "shippingAddresses"
    }

    /**
     * Create or update user profile
     */
    suspend fun createOrUpdateUserProfile(
        displayName: String? = null,
        photoURL: String? = null
    ): Result<Unit> {
        return try {
            val currentUser = firebaseAuth.currentUser
                ?: return Result.failure(Exception("User not authenticated"))

            val user = User(
                uid = currentUser.uid,
                email = currentUser.email ?: "",
                displayName = displayName ?: currentUser.displayName,
                photoURL = photoURL ?: currentUser.photoUrl?.toString(),
                createdAt = System.currentTimeMillis(),
                isAdmin = false
            )

            firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .set(user)
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get current user profile
     */
    suspend fun getCurrentUserProfile(): Result<User?> {
        return try {
            val currentUser = firebaseAuth.currentUser
                ?: return Result.failure(Exception("User not authenticated"))

            val snapshot = firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .get()
                .await()

            val user = snapshot.toObject(User::class.java)
            Result.success(user)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update user profile
     */
    suspend fun updateUserProfile(
        displayName: String? = null,
        photoURL: String? = null
    ): Result<Unit> {
        return try {
            val currentUser = firebaseAuth.currentUser
                ?: return Result.failure(Exception("User not authenticated"))

            val updates = mutableMapOf<String, Any>()
            displayName?.let { updates["displayName"] = it }
            photoURL?.let { updates["photoURL"] = it }

            if (updates.isNotEmpty()) {
                firestore.collection(USERS_COLLECTION)
                    .document(currentUser.uid)
                    .update(updates)
                    .await()
            }

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Add shipping address
     */
    suspend fun addShippingAddress(address: ShippingAddress): Result<String> {
        return try {
            val currentUser = firebaseAuth.currentUser
                ?: return Result.failure(Exception("User not authenticated"))

            // If this is the first address, make it default
            val existingAddresses = getShippingAddresses()
            val isFirstAddress = existingAddresses.getOrNull()?.isEmpty() ?: true

            val addressToAdd = if (isFirstAddress) {
                address.copy(isDefault = true)
            } else {
                address
            }

            val addressRef = firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .collection(SHIPPING_ADDRESSES_SUBCOLLECTION)
                .document()

            addressRef.set(addressToAdd.copy(id = addressRef.id)).await()

            Result.success(addressRef.id)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get all shipping addresses for current user
     */
    suspend fun getShippingAddresses(): Result<List<ShippingAddress>> {
        return try {
            val currentUser = firebaseAuth.currentUser
                ?: return Result.failure(Exception("User not authenticated"))

            val snapshot = firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .collection(SHIPPING_ADDRESSES_SUBCOLLECTION)
                .get()
                .await()

            val addresses = snapshot.documents.mapNotNull { doc ->
                doc.toObject(ShippingAddress::class.java)?.copy(id = doc.id)
            }

            Result.success(addresses)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get default shipping address
     */
    suspend fun getDefaultShippingAddress(): Result<ShippingAddress?> {
        return try {
            val addresses = getShippingAddresses()
            val addressList = addresses.getOrNull() ?: return Result.success(null)
            
            val defaultAddress = addressList.find { it.isDefault }
            Result.success(defaultAddress)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Update shipping address
     */
    suspend fun updateShippingAddress(address: ShippingAddress): Result<Unit> {
        return try {
            val currentUser = firebaseAuth.currentUser
                ?: return Result.failure(Exception("User not authenticated"))

            firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .collection(SHIPPING_ADDRESSES_SUBCOLLECTION)
                .document(address.id)
                .set(address)
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Delete shipping address
     */
    suspend fun deleteShippingAddress(addressId: String): Result<Unit> {
        return try {
            val currentUser = firebaseAuth.currentUser
                ?: return Result.failure(Exception("User not authenticated"))

            firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .collection(SHIPPING_ADDRESSES_SUBCOLLECTION)
                .document(addressId)
                .delete()
                .await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Set default shipping address
     */
    suspend fun setDefaultShippingAddress(addressId: String): Result<Unit> {
        return try {
            val currentUser = firebaseAuth.currentUser
                ?: return Result.failure(Exception("User not authenticated"))

            val userRef = firestore.collection(USERS_COLLECTION).document(currentUser.uid)

            // Fetch all existing shipping addresses before the transaction
            val existingAddressesResult = getShippingAddresses()
            val existingAddresses = existingAddressesResult.getOrThrow()

            firestore.runTransaction { transaction ->
                // First, unset all addresses as default
                existingAddresses.forEach { address ->
                    val addressDocRef = userRef.collection(SHIPPING_ADDRESSES_SUBCOLLECTION).document(address.id)
                    transaction.update(addressDocRef, "isDefault", false)
                }

                // Then set the specified address as default
                val targetAddressRef = userRef.collection(SHIPPING_ADDRESSES_SUBCOLLECTION)
                    .document(addressId)
                transaction.update(targetAddressRef, "isDefault", true)

                null
            }.await()

            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Get user profile as Flow for real-time updates
     */
    fun getUserProfileFlow(): Flow<Result<User?>> = flow {
        try {
            val currentUser = firebaseAuth.currentUser
            if (currentUser == null) {
                emit(Result.failure(Exception("User not authenticated")))
                return@flow
            }

            firestore.collection(USERS_COLLECTION)
                .document(currentUser.uid)
                .addSnapshotListener { snapshot, error ->
                    if (error != null) {
                        // Handle error - in a real implementation, you'd use a callback mechanism
                        return@addSnapshotListener
                    }
                    
                    if (snapshot != null && snapshot.exists()) {
                        val user = snapshot.toObject(User::class.java)
                        // In a real implementation, you'd emit this through a proper flow mechanism
                    }
                }
        } catch (e: Exception) {
            emit(Result.failure(e))
        }
    }

    /**
     * Check if current user is admin
     */
    suspend fun isCurrentUserAdmin(): Result<Boolean> {
        return try {
            val userProfile = getCurrentUserProfile()
            val user = userProfile.getOrNull()
            Result.success(user?.isAdmin ?: false)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * Initialize user profile after authentication
     */
    suspend fun initializeUserProfile(): Result<Unit> {
        return try {
            val currentUser = firebaseAuth.currentUser
                ?: return Result.failure(Exception("User not authenticated"))

            // Check if profile already exists
            val existingProfile = getCurrentUserProfile()
            if (existingProfile.getOrNull() != null) {
                return Result.success(Unit)
            }

            // Create new profile
            createOrUpdateUserProfile(
                displayName = currentUser.displayName,
                photoURL = currentUser.photoUrl?.toString()
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
