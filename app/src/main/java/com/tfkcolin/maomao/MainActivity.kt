package com.tfkcolin.maomao

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.navigation.compose.rememberNavController
import com.tfkcolin.maomao.ui.AppNavHost
import com.tfkcolin.maomao.ui.theme.MaomaoTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    companion object {
        private const val TAG = "MainActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        installSplashScreen()

        setContent {
            MaomaoTheme {
                MaomaoApp()
            }
        }
    }
}

@Composable
fun MaomaoApp(
) {
    val navController = rememberNavController()
    AppNavHost(navController = navController)
}
